import axios from "axios";
import { httpInstances } from "./config";

// 动态导入所有实例模块
const instanceModules = import.meta.glob("./instances/*.js", { eager: true });

const createHttpMethods = (instance) => ({
  baseUrl: instance.defaults.baseURL,
  request(config) {
    return instance(config);
  },
  get(url, params = {}, options = {}) {
    return instance.get(url, { params, ...options });
  },
  post(url, params, options = {}) {
    return instance.post(url, params, options);
  },
  put(url, params, options = {}) {
    return instance.put(url, params, options);
  },
  delete(url, params, options = {}) {
    return instance.delete(url, { data: params, ...options });
  },
  download(url, params, method = "get", options = {}) {
    return method === "get" ? instance.get(url, { params, responseType: "blob", ...options }) : instance.post(url, params, { responseType: "blob", ...options });
  },
  upload(url, params = {}, options = {}) {
    return instance.post(url, params, {
      headers: { "Content-Type": "multipart/form-data" },
      ...options
    });
  },
  all(iterable) {
    return axios.all(iterable);
  },
  spread(callback) {
    return axios.spread(callback);
  }
});

const exportsHttpInstance = {};

for (const path in instanceModules) {
  const moduleName = path.match(/\.\/instances\/(.*)\.js$/)[1];
  const instance = instanceModules[path].default;
  const alias = httpInstances[moduleName];

  if (alias) {
    exportsHttpInstance[alias] = createHttpMethods(instance);
  } else {
    console.warn(`No alias found for instance module "${moduleName}". Please add it to the alias configuration.`);
  }
}

export default exportsHttpInstance;
