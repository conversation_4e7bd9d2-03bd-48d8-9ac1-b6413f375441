import services from "@/services";
const { http } = services;

const prefix = "";

export const KnowledgeApi = {
  // 获取预案
  getPlanByDeviceName: (name) => http.get(prefix + `/api/planForNeo4j/getPlanNodesByDeviceName`, { deviceName: name }),

  // 获取预案
  getPlanById: (id) => http.get(prefix + `/accident/planListByPlanId`, { idList: id }),

  // 获取预案图谱
  getNodeGraphByPlanId: (id) => http.get(prefix + `/accident/getNeo4jNodeByPlanId`, { planId: id }),

  // 展开图谱节点
  expandNodesByNodeId: (params) => http.get(prefix + `/accident/getPath`, params)
};
