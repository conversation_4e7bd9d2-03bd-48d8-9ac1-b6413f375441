// 调试工具函数
export const debugKnowledgeGraphMap = {
  // 检查组件状态
  checkComponentState(component) {
    console.log('=== Knowledge Graph Map Debug Info ===');
    console.log('Chart instance:', component.chart);
    console.log('SVG instance:', component.svg);
    console.log('Simulation instance:', component.simulation);
    console.log('Graph data:', component.graphData);
    console.log('Node positions cache:', component.nodePositions);
    console.log('Last map transform:', component.lastMapTransform);
    
    if (component.chart) {
      console.log('Chart option:', component.chart.getOption());
    }
    
    if (component.svg) {
      console.log('SVG nodes count:', component.svg.selectAll('.nodes g').size());
      console.log('SVG links count:', component.svg.selectAll('.links line').size());
    }
  },

  // 测试坐标转换
  testCoordinateConversion(component, geoCoords) {
    if (!component.chart) {
      console.error('Chart not initialized');
      return null;
    }
    
    try {
      const screenCoords = component.chart.convertToPixel('geo', geoCoords);
      console.log(`Geo ${geoCoords} -> Screen ${screenCoords}`);
      return screenCoords;
    } catch (error) {
      console.error('Coordinate conversion failed:', error);
      return null;
    }
  },

  // 测试所有节点的坐标转换
  testAllNodesConversion(component) {
    if (!component.graphData || !component.graphData.nodes) {
      console.error('No graph data available');
      return;
    }
    
    console.log('=== Testing All Nodes Coordinate Conversion ===');
    component.graphData.nodes.forEach(node => {
      if (node.geo) {
        const screenCoords = this.testCoordinateConversion(component, node.geo);
        console.log(`Node ${node.name}:`, {
          geo: node.geo,
          screen: screenCoords,
          cached: component.nodePositions.get(node.id)
        });
      }
    });
  },

  // 强制刷新节点位置
  forceRefreshNodes(component) {
    console.log('=== Force Refreshing Node Positions ===');
    component.nodePositions.clear();
    component.lastMapTransform = null;
    component.updateGraphPosition();
  },

  // 添加调试节点
  addDebugNode(component, name, geoCoords, color = '#ff0000') {
    const debugNode = {
      id: `debug-${Date.now()}`,
      name: name,
      geo: geoCoords,
      size: 15,
      color: color,
      type: 'debug'
    };
    
    if (!component.graphData.nodes) {
      component.graphData.nodes = [];
    }
    
    component.graphData.nodes.push(debugNode);
    console.log('Added debug node:', debugNode);
    
    // 触发更新
    component.updateGraph();
  }
};

// 在浏览器控制台中使用的全局调试函数
if (typeof window !== 'undefined') {
  window.debugKnowledgeGraphMap = debugKnowledgeGraphMap;
}
