<template>
  <div
    class="realtion-graph-container"
    :class="theme"
  >
    <div class="graphic-header">
      <div class="graphic-legend">
        <Legend-Panel
          v-if="legendInitialized"
          :legendMap="legendMap"
          :labelCountMap="labelCountMap"
          :theme="theme"
          @doFilter="doFilter"
        ></Legend-Panel>
      </div>
      <div class="graphic-menu">
        <!-- <el-select
          v-if="isShowThemePanel"
          size="mini"
          v-model="theme"
          style="width: 90px; margin-right: 5px"
        >
          <el-option
            label="暗色调"
            value="dark"
          ></el-option>
          <el-option
            label="浅色调"
            value="light"
          ></el-option>
        </el-select> -->
      </div>
    </div>
    <div
      class="graphic-panel"
      @mousemove="onMouseMove"
    >
      <RelationGraph
        v-if="graphInitialized"
        ref="graphRef"
        :options="graphOptions"
        :on-node-click="onNodeClick"
        :on-line-click="onLineClick"
        :on-canvas-click="onCanvasClick"
        :on-zoom-end="onZoomEnd"
        :on-node-drag-end="onNodeDragEnd"
        :on-canvas-drag-end="onCanvasDragEnd"
      >
        <template #graph-plug>
          <!-- <SurroundMenu
            v-if="showNodeMenu"
            :currentNode="currentNode"
            :nodeMenuPanel="nodeMenuPanel"
            @node-menu-click="clickNodeMenu"
          ></SurroundMenu> -->
          <TogglePanel
            v-if="isShowPropertyPanel"
            :top="'5px'"
            :left="'3px'"
            :title="'节点详情'"
            :state="false"
            :theme="theme"
          >
            <node-property-detail
              :properties="currentNode.data"
              :theme="theme"
            ></node-property-detail>
          </TogglePanel>
          <div
            v-if="isShowNodeTips"
            class="c-tips"
            :style="{ left: nodeTipsPosition.x + 'px', top: nodeTipsPosition.y + 'px' }"
          >
            <div>text: {{ currentTipsNode.data.text }}</div>
            <div>label: {{ currentTipsNode.data.label }}</div>
          </div>
        </template>
      </RelationGraph>
    </div>
  </div>
</template>
<script>
import RelationGraph from "relation-graph";
import TogglePanel from "./components/toggle-panel";
import LegendPanel from "./components/legend";
import SurroundMenu from "./components/surround-menu";
import NodePropertyDetail from "./components/node-property-detail";
import { initDataBySource } from "./tools/DataProcessorBasic";
import { BasicGraphicApi } from "@/api/kg/basic";
import MockDataV2 from "./mock/response_v2.json";

export default {
  name: "BasicGraphPage",
  // eslint-disable-next-line vue/no-unused-components
  components: { RelationGraph, TogglePanel, LegendPanel, SurroundMenu, NodePropertyDetail },
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    vs: {
      type: String,
      default: ""
    },
    dataDriver: {
      type: String,
      default: "local"
    }
  },
  data() {
    return {
      theme: "transparent", // 主题，light: 亮色主题，dark: 暗黑主题, transparent: 透明主题
      mode: "dev", // 开发模式，dev: 开发模式，prod: 生产模式
      origin: "url", //  数据获取方式，message: 消息模式，url: url模式
      zoom: 60, // 缩放比例
      graphInitialized: true, // 图谱是否初始化
      legendInitialized: false, // 图例是否初始化
      isShowPropertyPanel: false, // 是否显示节点详情
      isShowMiniView: true, // 是否显示迷你视图
      isShowThemePanel: true, // 是否显示主题选择
      showNodeMenu: false,
      nodeMenuPanel: { x: 0, y: 0, width: 120, height: 120 },
      graphData: {},
      nodes: [],
      links: [],
      currentNode: {},
      currentTipsNode: {},
      legendMap: {},
      labelCountMap: {},
      version: "v2",
      colorList: ["#C990C0", "#F79767", "#57C7E3", "#F16667", "#D9C8AE", "#8DCC93", "#ECB5C9", "#4C8EDA", "#FFC454", "#DA7194", "#569480", "#A5ABB6"], // 可用颜色列表
      graphOptions: {
        hideNodeContentByZoom: true,
        allowShowMiniToolBar: false,
        debug: false,
        lineUseTextPath: true,
        defaultLineShape: 1,
        placeSingleNode: true,
        moveToCenterWhenRefresh: true,
        zoomToFitWhenRefresh: true,
        backgroundColor: "transparent",
        layout: {
          label: "中心",
          layoutName: "force"
        },
        defaultFocusRootNode: false,
        defaultNodeBorderWidth: 0,
        defaultNodeColor: "#42ad18",
        reLayoutWhenExpandedOrCollapsed: false,
        useAnimationWhenExpanded: true
        // 这里可以参考"Graph 图谱"中的参数进行设置 https://www.relation-graph.com/#/docs/graph
      },
      // --------Node Tips-----------
      isShowNodeTips: false,
      nodeTipsPosition: { x: 0, y: 0 },
      // 用于记录上一次的宽高
      previousWidth: null,
      previousHeight: null,
      resizeObserver: null, // ResizeObserver 实例,
      resizable: true // 是否开启窗口大小变化监听
    };
  },
  watch: {
    theme(val) {
      this.init();
      this.updateBodyClass(val);
    }
  },
  mounted() {
    this.init();
    this.updateBodyClass(this.theme);
  },
  beforeDestroy() {
    // 在组件销毁前停止观察
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
  },
  methods: {
    init() {
      if (this.origin === "message") {
        this.$nextTick(() => {
          window.addEventListener("message", this.processGraphicDataForEvent);
        });
      } else {
        if (this.dataDriver === "local") {
          if (this.vs === "v2" || this.version === "v2") {
            if (this.mode === "dev") {
              let jsonData = MockDataV2;
              this.processGraphicData(jsonData);
            } else {
              BasicGraphicApi.getNeo4jData().then((res) => {
                this.processGraphicData(res.data);
              });
            }
          }
        } else {
          this.processGraphicData({ nodes: [], relations: [] });
        }
      }
      this.$nextTick(() => {
        // 在组件挂载时监听窗口 resize 事件
        // 创建 ResizeObserver 实例
        if (this.resizable) {
          this.resizeObserver = new ResizeObserver((entries) => {
            // 遍历所有被观察的元素
            for (let entry of entries) {
              if (entry.target === document.body) {
                // 监听的是整个窗口的变化
                this.onWindowResize(entry);
              }
            }
          });

          // 开始监听窗口（监听 document.body 或其他特定容器）
          this.resizeObserver.observe(document.body);
        }
      });
    },
    processGraphicDataForEvent(event) {
      if (event.data.type === "kgDataSource") {
        console.log("捕获数据", event.data.value);
        let result = JSON.parse(event.data.value);
        this.processGraphicData(result);
      }
    },
    processGraphicData(data) {
      this.graphInitialized = false;
      console.log("processGraphicData:", data);
      this.resetAllCounter();
      const result = this.loadData(data, this.version);
      this.graphData = result;
      this.nodes = result.nodes;
      this.links = result.links;

      // 打印到控制台以验证转换
      console.log(this.version + "版本节点数据:", this.nodes);
      console.log(this.version + "版本关系数据:", this.links);

      this.legendInitialized = true;
      this.graphInitialized = true;

      if (this.nodes.length === 0) {
        this.nodes.push({ id: "root", opacity: 0 });
      }

      let dataSource = { rootId: this.nodes[0].id, nodes: this.nodes, lines: this.links };

      // 以上数据中的node和link可以参考"Node节点"和"Link关系"中的参数进行配置
      this.$refs.graphRef.setJsonData(dataSource, async (graphInstance) => {
        // Called when the relation-graph is completed
        // 这些写上当图谱初始化完成后需要执行的代码
        graphInstance.setZoom(this.zoom); // 20表示20%

        // 初始化node节点fontsize; 未完成
        this.updateNodeFontSize(this.nodes, this.$refs.graphRef.$el);

        await graphInstance.startAutoLayout(); // 加载成功后调用自动布局
      });
    },
    processGraphicDataBySearchResult(data) {
      this.graphInitialized = false;
      console.log("processGraphicDataBySearchResult:", data);
      this.resetAllCounter();
      const result = this.loadData(data, this.version);
      this.graphData = result;
      this.nodes = result.nodes;
      this.links = result.links;

      // 打印到控制台以验证转换
      console.log(this.version + "版本节点数据:", this.nodes);
      console.log(this.version + "版本关系数据:", this.links);

      this.legendInitialized = true;
      this.graphInitialized = true;

      if (this.nodes.length === 0) {
        this.nodes.push({ id: "root", opacity: 0 });
      }

      let dataSource = { rootId: this.nodes[0].id, nodes: this.nodes, lines: this.links };

      setTimeout(() => {
        console.log("processGraphicDataBySearchResult:", dataSource);
        // 以上数据中的node和link可以参考"Node节点"和"Link关系"中的参数进行配置
        this.$refs.graphRef.setJsonData(dataSource, async (graphInstance) => {
          // 这些写上当图谱初始化完成后需要执行的代码
          graphInstance.setZoom(this.zoom); // 20表示20%

          // 初始化node节点fontsize; 未完成
          this.updateNodeFontSize(this.nodes, this.$refs.graphRef.$el);

          await graphInstance.startAutoLayout(); // 加载成功后调用自动布局
        });
      }, 100);
    },
    loadData(data, version, operationType = "replace") {
      // 获取当前数据，用于增量或减量操作
      const currentData = operationType !== "replace" ? this.graphData : null;

      const result = initDataBySource(data, this.colorList, (label) => this.getColorForLabel(label, this.colorList), this.version, this.theme, operationType, currentData);

      return result;
    },
    expandNode(node) {
      // 如果节点已展开，则直接返回（避免重复请求数据）
      if (node.expand) {
        this.$message({ type: "warn", message: node.data.text + "节点已展开，无需再次请求数据。" });
        return;
      }
      // 调用BasicGraphicApi的getNeo4jExpandData方法获取节点扩展数据
      BasicGraphicApi.getNeo4jExpandData(node.id).then((res) => {
        const graphInstance = this.$refs.graphRef.getInstance();
        const result = this.loadData(res.data, this.version, "add");
        console.log("legendMap:", this.legendMap);
        // 获取图实例
        graphInstance.addNodes(result.nodes);
        // 新增线条数据
        graphInstance.addLines(result.links);

        node.expand = true; // 标记节点已展开

        graphInstance.doLayout();
      });
    },
    getColorForLabel(label, colorList) {
      if (this.labelCountMap[label]) {
        this.$set(this.labelCountMap, label, this.labelCountMap[label] + 1);
      } else {
        this.$set(this.labelCountMap, label, 1);
      }

      if (this.legendMap[label]) {
        return this.legendMap[label];
      } else {
        const color = colorList.shift();
        this.$set(this.legendMap, label, color);
        return color;
      }
    },
    onNodeClick(nodeObject, $event) {
      console.log("onNodeClick:", nodeObject);
      this.cancelAllNodeSelected();
      setTimeout(() => {
        this.currentNode = nodeObject;
        this.updateNodeMenuPosition();
        this.showNodeMenu = true;
        this.isShowPropertyPanel = true;
      }, 100);
    },
    // 定义窗口大小变化时的处理方法
    onWindowResize(entries) {
      // 通过 $nextTick 刷新其他 UI 和业务逻辑
      this.$nextTick(() => {
        if (this.$refs.graphRef) {
          this.$refs.graphRef.getInstance().resetViewSize();
          this.$refs.graphRef.getInstance().refresh();
        }
      });
    },
    onLineClick() {},
    onCanvasClick() {
      this.cancelAllNodeSelected();
    },
    onZoomEnd() {
      this.updateNodeMenuPosition();
    },
    onNodeDragEnd() {
      this.updateNodeMenuPosition();
    },
    onCanvasDragEnd() {
      this.updateNodeMenuPosition();
    },
    updateNodeMenuPosition() {
      if (this.currentNode && this.$refs.graphRef && this.currentNode.el) {
        const graphInstance = this.$refs.graphRef.getInstance();
        const viewCoordinate = graphInstance.getClientCoordinateByCanvasCoordinate({
          x: this.currentNode.x + this.currentNode.el.offsetWidth / 2,
          y: this.currentNode.y + this.currentNode.el.offsetHeight / 2
        });
        this.nodeMenuPanel.x = viewCoordinate.x - graphInstance.options.canvasOffset.x - this.nodeMenuPanel.width / 2;
        this.nodeMenuPanel.y = viewCoordinate.y - graphInstance.options.canvasOffset.y - this.nodeMenuPanel.height / 2;
      }
    },
    updateGraphicOptions(optionKey, optionValue) {
      this.graphOptions[optionKey] = optionValue;
      this.$refs.graphRef.setOptions(this.graphOptions, null);
      this.$refs.graphRef.getInstance().refresh();
    },
    updateNodeFontSize(nodes, graphRef) {
      // 初始化node节点fontSize
      this.$nextTick(() => {
        let relNodeList = graphRef.querySelectorAll(".rel-node-peel");
        let map = new Map();
        for (let i = 0; i < relNodeList.length; i++) {
          let node = relNodeList[i];
          let id = node.getAttribute("data-id");
          if (!map.has(id)) {
            map.set(id, node);
          }
        }
        for (let i = 0; i < nodes.length; i++) {
          let node = nodes[i];
          let id = node.id;
          if (node.fontSize) {
            let nodeDom = map.get(id);
            let cNodeText = nodeDom.querySelector(".c-node-text");
            if (cNodeText) {
              if (
                node.fontSize.indexOf("px") !== -1 &&
                node.fontSize.indexOf("%") !== -1 &&
                node.fontSize.indexOf("vw") !== -1 &&
                node.fontSize.indexOf("vh") !== -1 &&
                node.fontSize.indexOf("rem") !== -1 &&
                node.fontSize.indexOf("em") !== -1
              ) {
                cNodeText.style.fontSize = node.fontSize;
              } else {
                cNodeText.style.fontSize = node.fontSize + "px";
              }
            }
          }
        }
      });
    },
    clickNodeMenu(menuId, e) {
      this.showNodeMenu = false;
      // this.$message({ type: "success", message: "Node:" + this.currentNode.text + " Menu Event:" + menuId });
      if (menuId === "1") {
        // this.showNodeInfo();
      } else if (menuId === "2") {
        // this.copyNode();
      } else if (menuId === "3") {
        // this.createLine(e);
      } else if (menuId === "4") {
        this.expandNode(this.currentNode);
      }
    },
    onMouseMove($event) {
      if (this.$refs.graphRef) {
        const graphInstance = this.$refs.graphRef.getInstance();
        const node = graphInstance.isNode($event.target);
        if (node) {
          this.showNodeTips($event, node);
          this.isShowNodeTips = true;
        } else {
          this.isShowNodeTips = false;
        }
      }
    },
    showNodeTips($event, nodeObject) {
      const graphInstance = this.$refs.graphRef.getInstance();
      const _base_position = graphInstance.options.fullscreen ? { x: 0, y: 0 } : graphInstance.getBoundingClientRect();
      this.currentTipsNode = nodeObject;
      this.nodeTipsPosition.x = $event.clientX - _base_position.x + 10;
      this.nodeTipsPosition.y = $event.clientY - _base_position.y + 10;
    },
    cancelAllNodeSelected() {
      const graphInstance = this.$refs.graphRef.getInstance();
      graphInstance.clearChecked();
      this.showNodeMenu = false;
      this.showNodeInfoCard = false;
      this.showNodeOptionsEditor = false;
      this.isShowPropertyPanel = false;
    },
    doFilter(label) {
      const _all_nodes = this.$refs.graphRef.getInstance().getNodes();
      const _all_links = this.$refs.graphRef.getInstance().getLinks();
      this.cancelAllNodeSelected();
      if (label !== "all") {
        let hiddenNodeIds = [];
        _all_nodes.forEach((thisNode) => {
          let _isHide = false;
          if (thisNode.data["label"] !== label) {
            _isHide = true;
            hiddenNodeIds.push(thisNode.id);
          }
          thisNode.opacity = _isHide ? 0.1 : 1;
        });
        _all_links.forEach((thisLink) => {
          thisLink.relations.forEach((thisLine) => {
            if (hiddenNodeIds.includes(thisLine.from) || hiddenNodeIds.includes(thisLine.to)) {
              thisLine.isHide = true;
            }
          });
        });
      } else {
        _all_nodes.forEach((thisNode) => {
          thisNode.opacity = 1;
        });
        _all_links.forEach((thisLink) => {
          thisLink.relations.forEach((thisLine) => {
            thisLine.isHide = false;
          });
        });
      }
      this.$refs.graphRef.getInstance().dataUpdated();
    },
    focusNodeByText(text) {
      const instance = this.$refs.graphRef.getInstance();
      const _all_nodes = instance.getNodes();
      this.cancelAllNodeSelected();
      _all_nodes.forEach((node) => {
        if (text === node.data["text"]) {
          instance.focusNodeById(node.data["id"]);
        }
      });
    },
    search(searchMode, keyword, nodeName1, nodeName2) {
      return new Promise((resolve) => {
        if (searchMode === "node") {
          this.focusNodeByText(keyword);
        }
        // 模拟异步操作，比如API请求
        setTimeout(() => {
          console.log("Search completed");
          resolve(); // 当操作完成时调用resolve
        }, 2000);
      });
    },
    toggleAllPanel() {
      this.isShowPropertyPanel = false;
      this.isShowMiniView = !this.isShowMiniView;
    },
    toggleAllPanelForServer() {
      this.isShowPropertyPanel = false;
      this.isShowMiniView = false;
    },
    resetAllCounter() {
      this.colorList = ["#C990C0", "#F79767", "#57C7E3", "#F16667", "#D9C8AE", "#8DCC93", "#ECB5C9", "#4C8EDA", "#FFC454", "#DA7194", "#569480", "#A5ABB6"];
      this.labelCountMap = {};
      this.legendMap = {};
    },
    // 添加/移除 body 的 class 方法
    updateBodyClass(theme) {
      // 清理旧的 class，添加新的 class
      document.body.classList.remove("light", "dark", "transparent");
      document.body.classList.add(theme);
    }
  }
};
</script>
<style lang="scss" scoped>
.realtion-graph-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .graphic-header {
    width: 100%;
    height: 40px;
    padding: 0 5px;
    display: flex;
    align-items: center;
    box-sizing: border-box;

    .graphic-legend {
      width: 90%;
      height: 100%;
    }

    .graphic-menu {
      width: fit-content;
      height: 100%;
      margin: 0 5px;
      display: flex;
      align-items: center;

      .graphic-menu-item {
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }
  }

  .graphic-panel {
    width: 100% !important;
    height: calc(100% - 40px) !important;
    overflow: hidden;
  }
}
</style>
<style lang="scss" scoped>
::v-deep .rel-node-shape-0 {
  padding: 3px !important;
}
// --------------------------Node menu-----------------------
@keyframes growUp {
  from {
    scale: 10%;
    rotate: 0deg;
  }
  50% {
    scale: 30%;
    rotate: 90deg;
  }
  to {
    scale: 100%;
    rotate: 360deg;
  }
}
.c-surround-menu-panel {
  position: absolute;
  width: 160px;
  height: 160px;
  z-index: 999;
  animation: growUp 0.5s linear;
  box-shadow: 0 0 0 38px rgba(255, 255, 255, 0.56) inset;
  border-radius: 50%;
}
.c-svg-button {
  fill: rgb(22 21 122 / 65%);
  cursor: pointer;
}
.c-svg-bottom-text {
  cursor: pointer;
  color: #ffffff;
}
.c-svg-button:hover {
  fill: rgba(26, 23, 28, 0.85);
}
@keyframes node-text-in {
  from {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.c-current-node-text {
  position: absolute;
  top: calc(100% + 16px);
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  animation: node-text-in 1s linear;
  input {
    background-color: rgba(35, 30, 37, 0.68);
    color: #ffffff;
    border-radius: 5px;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    border-width: 0px;
    &:focus {
      background-color: #dfac03;
    }
  }
}
// --------------------------Node info card-----------------------
.c-operate-panels {
  position: absolute;
  z-index: 700;
  left: 30px;
  top: 30px;
  width: 200px;
}
.c-node-info-card {
  text-align: left;
  padding: 10px;
  background-color: rgba(233, 210, 243, 0.68);
  border-radius: 10px;
  font-size: 12px;
  line-height: 25px;
}
.c-person-pic {
  width: 120px;
  border-radius: 50%;
  margin-top: 10px;
}
// --------------------------Node options panel-----------------------
.c-node-options-panel {
  border-radius: 10px;
  font-size: 12px;
  line-height: 25px;
  margin-top: 20px;
}

.c-tips {
  z-index: 999;
  padding: 10px;
  min-width: 200px;
  max-width: 300px;
  position: absolute;
  border-radius: 10px;
  background-color: #333333;
  color: #ffffff;
  border: #eeeeee solid 1px;
  box-shadow: 0px 0px 8px #cccccc;
  & > div {
    line-height: 25px;
    padding-left: 10px;
    font-size: 12px;
  }
}
</style>
<style lang="scss">
.relation-graph .rel-toolbar-h-right {
  right: 5px;
}

.light {
  &.realtion-graph-container {
    background-color: #fff;
  }

  .relation-graph .rel-easy-view {
    background-color: #fff;
  }
}

.transparent {
  &.realtion-graph-container {
    background-color: transparent;
  }

  .relation-graph .rel-easy-view {
    background-color: transparent;
  }
}

.dark {
  &.realtion-graph-container {
    background-color: #0f1c2e;

    .graphic-menu-item {
      color: #4d648d;
    }
  }

  .relation-graph .rel-miniview canvas {
    background-color: #0f1c2e;
    opacity: 0.9;
  }

  .relation-graph .rel-toolbar {
    background-color: rgb(21 36 59 / 90%);
    color: #ffffff;
    .c-current-zoom {
      color: #ffffff;
    }
  }

  .relation-graph .rel-easy-view {
    background-color: #0f1c2e;
  }

  .relation-graph .rel-miniview {
    border: #3f4b78 solid 1px;
    border-radius: 5px;
    box-shadow: 1px 1px 4px 1px #3f4b78;
    background-color: #fff;
  }

  .relation-graph .rel-node-checked {
    transition: background-color 0.2s ease, outline 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 0 0 8px #ffb84d, 0 0 20px #ffb84d; /* 柔和橙色光晕 */
    animation: flicker 3s; /* 3秒闪烁 */
  }

  .el-input__inner,
  .el-textarea__inner {
    background-color: transparent;
    border-color: #707070;
    color: #fff;
  }
}

@keyframes flicker {
  0%,
  100% {
    box-shadow: 0 0 0 8px #8fffbf, 0 0 20px #8fffbf; /* 柔和绿色光晕 */
    background-color: #8fffbf; /* 柔和绿色 */
  }
  50% {
    box-shadow: 0 0 0 8px rgba(143, 255, 191, 0.5), 0 0 15px rgba(143, 255, 191, 0.5);
    background-color: rgba(143, 255, 191, 0.8); /* 更暗一点的绿色 */
  }
}
</style>
