// src/utils/dataProcessor.js

import { Node } from "@/model/Node";
import { Link } from "@/model/Link";

export function initDataBySource(data, colorList, getColorForLabel, dataVersion, theme) {
  if (dataVersion === "v1") {
    return initDataBySourceV1(data, colorList, getColorForLabel, theme);
  } else {
    return initDataBySourceV2(data, colorList, getColorForLabel, theme);
  }
}

function initDataBySourceV1(data, colorList, getColorForLabel, theme) {
  if (!data.result) {
    data = { result: { entity: data.nodes ? data.nodes : data.entity, relation: data.relations ? data.relations : data.relation, labelField: data.aliasLabel } };
  }
  const { entity, relation, labelField } = data.result ? data.result : data.data;

  if (labelField) {
    entity.forEach((item) => {
      item.label = item[labelField] ? item[labelField] : "未分类";
    });
  }

  console.log(labelField);

  // 转换 节点 数据
  const nodes = entity.map(
    (item) =>
      new Node({
        id: item.id.toString(),
        text: item.name.length > 10 ? item.name.substring(0, 9) + "..." : item.name,
        color: getColorForLabel(item.label, colorList),
        fontSize: item.fontSize ? item.fontSize : null,
        fontColor: item.fontColor ? item.fontColor : null,
        weight: item.weight ? item.weight : 1,
        data: {
          id: item.id,
          text: item.name,
          label: item.label,
          description: item.description,
          attribute: item.attribute ? item.attribute : {}
        }
      })
  );

  // 转换 关系 数据
  const links = relation.map(
    (item) =>
      new Link({
        from: item.startPro.toString(),
        to: item.endPro.toString(),
        text: item.rel,
        fontColor: theme === "dark" ? "#fff" : "#000",
        color: theme === "dark" ? "#cee8ff" : "rgb(136 133 255)",
        data: {
          id: item.id,
          startName: item.startName,
          endName: item.endName
        }
      })
  );

  return { nodes, links };
}

function initDataBySourceV2(data, colorList, getColorForLabel, theme) {
  if (!data.result) {
    data = { result: { entity: data.nodes ? data.nodes : data.entity, relation: data.relations ? data.relations : data.relation, labelField: data.aliasLabel } };
  }
  const { entity, relation, labelField } = data.result ? data.result : data.data;

  if (labelField) {
    entity.forEach((item) => {
      item.label = item[labelField] ? item[labelField] : "未分类";
    });
  }

  console.log(labelField);

  // 转换 节点 数据
  const nodes = entity.map(
    (item) =>
      new Node({
        id: item.id.toString(),
        text: item.name.length > 10 ? item.name.substring(0, 9) + "..." : item.name,
        color: getColorForLabel(item.label, colorList),
        weight: item.weight ? item.weight : 1,
        fontSize: item.fontSize ? item.fontSize : null,
        fontColor: item.fontColor ? item.fontColor : null,
        data: {
          id: item.id,
          text: item.name,
          label: item.label,
          description: item.description,
          attribute: item.attribute ? item.attribute : {}
        }
      })
  );

  // 转换 关系 数据
  const links = relation.map((item) => {
    let linkOption = {
      from: item.from.toString(),
      to: item.to.toString(),
      text: item.text,
      fontColor: theme === "dark" ? "#fff" : "#000",
      color: theme === "dark" ? "#cee8ff" : "rgb(136 133 255)"
    };

    return new Link(Object.assign(item, linkOption));
  });

  return { nodes, links };
}
