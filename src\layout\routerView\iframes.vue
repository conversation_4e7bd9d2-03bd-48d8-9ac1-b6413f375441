<template>
  <div
    class="wh-100"
    v-loading="iframeLoading"
  >
    <iframe
      :src="meta && meta.isLink"
      frameborder="0"
      height="100%"
      width="100%"
      id="iframe"
    ></iframe>
  </div>
</template>

<script>
export default {
  name: "layoutIfameView",
  props: {
    meta: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      iframeLoading: true
    };
  },
  created() {
    this.$bus.$on("onTagsViewRefreshRouterView", (path) => {
      if (this.$route.path !== path) return false;
      this.$emit("getCurrentRouteMeta");
    });
  },
  mounted() {
    this.initIframeLoad();
  },
  methods: {
    // 初始化页面加载 loading
    initIframeLoad() {
      this.$nextTick(() => {
        this.iframeLoading = true;
        const iframe = document.getElementById("iframe");
        if (!iframe) return false;
        if (iframe.attachEvent) {
          iframe.attachEvent("onload", () => {
            this.iframeLoading = false;
          });
        } else {
          iframe.onload = () => {
            this.iframeLoading = false;
          };
        }
      });
    }
  }
};
</script>
