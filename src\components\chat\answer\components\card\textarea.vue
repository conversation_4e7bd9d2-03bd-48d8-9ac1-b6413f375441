<template>
  <el-input
    type="textarea"
    :autosize="{ minRows: 6, maxRows: 10 }"
    placeholder="请输入内容"
    readonly
    resize="none"
    v-model="content"
  >
  </el-input>
</template>

<script>
export default {
  name: "TextareaCard",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    content() {
      return this.data.text;
    }
  }
};
</script>
<style lang="scss" scoped></style>
