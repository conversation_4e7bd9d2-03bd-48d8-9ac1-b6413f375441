<template>
  <div>
    <div
      class="image-container"
      v-if="isAvatar"
    >
      <div :class="['avatar', responding ? 'animation' : '']">
        <img
          src="@/assets/images/chat/avatar.png"
          alt=""
        />
      </div>
    </div>
    <div
      class="chat-answer-container w-full group overflow-hidden rounded-xl"
      :class="item.content && 'bg-gradient'"
    >
      <div class="relative chat-text answer-text h-full text-gray-900">
        <Operation
          v-if="!responding && item.question === '开始处置'"
          :hasWorkflowProcess="!!item.workflowProcess"
          :item="item"
          :question="question"
          :index="index"
        />
        <WorkflowProcess
          v-if="item.workflowProcess && item.question === '开始处置' && hideProcessDetail"
          :data="item.workflowProcess"
          :item="item"
          :taskId="taskId"
          hideInfo
          :hideProcessDetail="hideProcessDetail"
        />
        <!-- <WorkflowProcess
          v-if="hideProcessDetail"
          :data="item.workflowProcess"
          :item="item"
          :taskId="taskId"
          hideInfo
          :hideProcessDetail="hideProcessDetail"
        /> -->
        <div
          v-if="responding && !item.content && !hasAgentThoughts"
          class="flex items-center justify-center w-6 h-5"
        >
          <LoadingAnim type="text" />
        </div>
        <BasicContent
          v-if="item.content && !hasAgentThoughts"
          :item="item"
        />
      </div>
    </div>
  </div>
</template>

<script>
import WorkflowProcess from "./workflow-process";
import BasicContent from "./basic-content";
import LoadingAnim from "../loading-anim";
import Operation from "./operation";
export default {
  name: "Answer",
  components: {
    WorkflowProcess,
    BasicContent,
    LoadingAnim,
    Operation
  },
  props: {
    taskId: {
      type: String,
      default: ""
    },
    question: {
      type: String,
      default: ""
    },
    item: {
      type: Object,
      default: () => ({})
    },
    index: {
      type: Number,
      default: 0
    },
    appData: {
      type: Object,
      default: () => ({})
    },
    responding: {
      type: Boolean,
      default: false
    },
    hideProcessDetail: {
      type: Boolean,
      default: false
    },
    isAvatar: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    workflowProcess() {
      return this.item.workflowProcess;
    },
    hasAgentThoughts() {
      return !!this.item.agent_thoughts?.length;
    },
    outputs() {
      return this.workflowProcess?.workflowData?.outputs;
    }
  }
};
</script>
