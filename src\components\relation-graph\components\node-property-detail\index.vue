<template>
  <div
    class="box"
    :class="theme"
  >
    <el-row class="content-row-auto">
      <el-col
        :span="24"
        class="col-value"
        style="height: auto"
      >
        <i
          class="el-icon-copy-document"
          title="复制节点属性"
          style="cursor: pointer"
          @click="copyProperties"
        ></i>
      </el-col>
    </el-row>
    <el-row class="content-row-auto">
      <el-col
        :span="4"
        class="col-label"
        style="height: auto"
      >
        <span class="text-label">ID</span>
      </el-col>
      <el-col
        :span="20"
        class="col-value"
      >
        {{ properties["id"] }}
        <i
          class="el-icon-copy-document copy-btn"
          title="复制值"
          style="cursor: pointer"
          @click="copyProp(properties['id'])"
        ></i>
      </el-col>
    </el-row>
    <el-row class="content-row-auto">
      <el-col
        :span="4"
        class="col-label"
        style="height: auto"
      >
        <span class="text-label">Text</span>
      </el-col>
      <el-col
        :span="20"
        class="col-value"
      >
        {{ properties["text"] }}
        <i
          class="el-icon-copy-document copy-btn"
          title="复制值"
          style="cursor: pointer"
          @click="copyProp(properties['text'])"
        ></i>
      </el-col>
    </el-row>
    <el-row class="content-row-auto">
      <el-col
        :span="4"
        class="col-label"
        style="height: auto"
      >
        <span class="text-label">label</span>
      </el-col>
      <el-col
        :span="20"
        class="col-value"
      >
        {{ properties["label"] }}
        <i
          class="el-icon-copy-document copy-btn"
          title="复制值"
          style="cursor: pointer"
          @click="copyProp(properties['label'])"
        ></i>
      </el-col>
    </el-row>
    <el-row class="content-row-auto">
      <el-col
        :span="4"
        class="col-label"
        style="height: auto"
      >
        <span class="text-label">描述</span>
      </el-col>
      <el-col
        :span="20"
        class="col-value"
      >
        {{ properties["description"] }}
        <i
          class="el-icon-copy-document copy-btn"
          title="复制值"
          style="cursor: pointer"
          @click="copyProp(properties['description'])"
        ></i>
      </el-col>
    </el-row>
    <template v-if="properties.attribute">
      <el-row
        v-for="(key, value, index) in properties.attribute"
        class="content-row-auto"
        :key="index"
      >
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">{{ value }}</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value"
        >
          {{ key }}
          <i
            class="el-icon-copy-document copy-btn"
            title="复制值"
            style="cursor: pointer"
            @click="copyProp(key)"
          ></i>
        </el-col>
      </el-row>
    </template>
    <input
      id="copy_content"
      type="text"
      value=""
      style="position: absolute; top: 0; left: 0; opacity: 0; z-index: -10"
    />
  </div>
</template>
<script>
export default {
  name: "NodePropertyDetail",
  props: {
    properties: {
      type: Object,
      default() {
        return {};
      }
    },
    theme: {
      type: String,
      default: "light"
    }
  },
  data() {
    return {};
  },
  methods: {
    copyProperties() {
      let props = {};
      for (let key in this.properties) {
        props[key] = this.properties[key];
      }
      // 获取点击的值
      let val = JSON.stringify(props);
      let clickContent = val;
      // 获取要赋值的input的元素
      let inputElement = document.getElementById("copy_content");
      // 给input框赋值
      inputElement.value = clickContent;
      // 选中input框的内容
      inputElement.select();
      // 执行浏览器复制命令
      document.execCommand("Copy");
      // 提示已复制
      this.$message.success("已复制");
    },
    copyProp(value) {
      // 获取点击的值
      let val = value;
      let clickContent = val;
      // 获取要赋值的input的元素
      let inputElement = document.getElementById("copy_content");
      // 给input框赋值
      inputElement.value = clickContent;
      // 选中input框的内容
      inputElement.select();
      // 执行浏览器复制命令
      document.execCommand("Copy");
      // 提示已复制
      this.$message.success("已复制");
    }
  }
};
</script>
<style lang="scss" scoped>
.box {
  width: 100%;
  padding: 5px;
  box-sizing: border-box;

  .content-row-auto {
    min-height: 25px;
    margin-top: 5px;
    display: flex;
    align-items: stretch;
  }

  .col-label {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: end;
    padding-right: 10px;
    font-size: 14px;
    font-weight: bold;
    color: #7aaad5;
    background-color: transparent;
  }

  .text-label {
    font-size: 13px;
    font-weight: 600;
  }

  .col-value {
    height: auto;
    padding: 5px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-size: 14px;
    font-family: "DM Sans";
    color: #fff;
    border: none;
    box-sizing: border-box;
    position: relative;

    .copy-btn {
      position: absolute;
      right: 0px;
      top: 10px;
      font-size: 12px;
      z-index: 10;
    }
  }
}

.dark,
.transparent {
  .col-label {
    color: #7aaad5;
  }

  .col-value {
    color: #fff;
  }
}

.light {
  .col-label {
    color: #7aaad5;
  }

  .col-value {
    color: #000;
  }
}
</style>
