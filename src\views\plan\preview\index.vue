<!-- <template>
  <div>
    <vue-office-docx
      v-if="$route.query.extension === 'docx'"
      style="height: 100vh"
      :src="$route.query.url"
      @rendered="rendered"
    />
    <vue-office-excel
      v-else-if="$route.query.extension === 'xlsx'"
      style="height: 100vh"
      :src="$route.query.url"
    />
  </div>
</template>
<script>
import VueOfficeDocx from "@vue-office/docx";
import VueOfficeExcel from "@vue-office/excel";
export default {
  name: "preview",
  components: {
    VueOfficeDocx,
    VueOfficeExcel
  },
  mounted() {
    console.log(this.$route.query.content);
  },
  methods: {
    rendered() {
      setTimeout(() => {
        this.findElementByTextXPath(this.$route.query.content);
      }, 1000);
    },
    findElementByTextXPath(text) {
      // 处理特殊字符和空格
      const escapedText = text.replace(/'/g, "\\'");
      const xpath = `(//*[normalize-space(.)='${escapedText}'])[last()]`;
      const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
      if (result.singleNodeValue) {
        result.singleNodeValue.style.backgroundColor = "yellow";
        result.singleNodeValue.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    }
  }
};
</script> -->
