<template>
  <div class="layout-footer mt-2.5">
    <div class="layout-footer-warp">
      <div>vue-admin，Made by lyt with ❤️</div>
      <div class="mt-1">copyright</div>
    </div>
    <el-divider>
      <div class="layout-footer-warp"></div>
    </el-divider>
  </div>
</template>

<script>
export default {
  name: "layoutFooter",
  data() {
    return {};
  }
};
</script>

<style scoped lang="scss">
.layout-footer {
  width: 100%;
  display: flex;
  &-warp {
    margin: auto;
    color: var(--nari-c-text-inverse-2);
    text-align: center;
  }
}
</style>
