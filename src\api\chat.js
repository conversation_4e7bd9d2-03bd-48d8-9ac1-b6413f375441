import services from "@/services";
const { http } = services;
const { BASE_URL_LLM_TOOLS } = window.$global;

const prefix = `${BASE_URL_LLM_TOOLS}/llm-tools/api/dify/app`;

export const chatApi = {
  fetchMessage: (appId) => prefix + `/${appId}/chat`,
  // 获取会话列表
  fetchConversations: ({ appId, ...params }) => http.get(prefix + `/${appId}/conversations`, params),
  // 获取会话历史消息
  fetchChatList: ({ appId, ...params }) => http.get(prefix + `/${appId}/messages`, params)
};
