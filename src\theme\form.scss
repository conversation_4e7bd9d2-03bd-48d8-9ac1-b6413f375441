.form-box {
  $border-color: #2e6369;
  width: 100%;
  padding: 5px;

  .content-row-auto {
    min-height: 40px;
    display: flex;
    align-items: stretch;
    border-top: 1px solid $border-color;
    border-left: 1px solid $border-color;
    &:last-child {
      border-bottom: 1px solid $border-color;
    }
  }

  // 新增flex布局混合器
  @mixin flex-layout($justify: flex-start) {
    display: flex;
    align-items: center;
    justify-content: $justify;
  }

  .col-label {
    $border-color: $border-color;
    $bg-color: #1b315c;

    @include flex-layout(flex-end);
    height: 100%;
    padding: 0 2px;
    font-size: 14px;
    color: #d2d2d2;
    background: $bg-color;
    border-right: 1px solid $border-color;

    &.is-strong {
      background-color: var(--nari-c-header-bg);
    }

    & + .col-value {
      border-right: 1px solid $border-color;
    }
  }

  .text-label {
    font-size: 13px;
    font-weight: 600;
  }

  .col-value {
    @include flex-layout;
    flex-wrap: wrap;
    height: auto;
    font-size: 12px;
    padding: 5px;
    border-right: 1px solid $border-color;

    .sub-label {
      @include flex-layout;
      width: fit-content;
      min-width: 80px;
      min-height: 20px;
      padding: 3px 8px;
      margin: 0 2px;
      box-sizing: border-box;
      background-color: #e8edf7;

      .sub-label-text {
        color: #333;
        font-size: 12px;
      }
    }
  }
}
