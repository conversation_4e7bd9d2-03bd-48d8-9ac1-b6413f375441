import { Api } from "@/api";
import { getFlatMenuList, getShowMenuList, getAllBreadcrumbList } from "@/utils/menu";
const routesListModule = {
  namespaced: true,
  state: {
    routesList: []
  },
  getters: {
    // 菜单权限列表 ==> 这里的菜单没有经过任何处理
    routesListGet: (state) => state.routesList,
    // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
    showMenuListGet: (state) => getShowMenuList(state.routesList),
    // 菜单权限列表 ==> 扁平化之后的一维数组菜单，主要用来添加动态路由
    flatMenuListGet: (state) => getFlatMenuList(state.routesList),
    // 递归处理后的所有面包屑导航列表
    breadcrumbListGet: (state) => getAllBreadcrumbList(state.routesList)
  },
  mutations: {
    // 设置路由，菜单中使用到
    saveRoutesList(state, data) {
      state.routesList = data;
    }
  },
  actions: {
    // 设置路由，菜单中使用到
    async getMenuList({ commit }) {
      const { data } = await Api.fetchMenuList();
      commit("saveRoutesList", data);
    }
  }
};

export default routesListModule;
