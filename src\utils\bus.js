import Vue from "vue";

/**
 * 全局事件总线
 * 用于组件间通信，不相关组件之间传递数据
 *
 * 使用方式:
 * 1. 在需要接收事件的组件中：
 *    mounted() {
 *      bus.$on('event-name', data => {
 *        // 处理数据
 *      })
 *    },
 *
 *    beforeDestroy() {
 *      bus.$off('event-name') // 移除监听，避免内存泄漏
 *    }
 *
 * 2. 在需要发送事件的组件中：
 *    methods: {
 *      sendData() {
 *        bus.$emit('event-name', data)
 *      }
 *    }
 */

export default new Vue();
