<template>
  <div class="text-container">
    <el-link
      type="primary"
      @click="dialogVisible = true"
      >{{ data.name }}</el-link
    >
    <nari-dialog
      class="dialog-card"
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :title="data.name || '详情'"
      :show-footer="false"
    >
      <component
        class="text-white"
        :is="data.component"
        :id="data.id"
      />
    </nari-dialog>
  </div>
</template>

<script>
import PlanDetail from "@/views/plan";
export default {
  name: "DialogCard",
  components: {
    PlanDetail
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false
    };
  }
};
</script>

<style scoped lang="scss">
.dialog-card {
  ::v-deep .el-dialog {
    background: #14246a;
    border-radius: 10px;
    &__body {
      max-height: 70vh;
      padding: 10px 20px 20px;
    }
  }
}
</style>
