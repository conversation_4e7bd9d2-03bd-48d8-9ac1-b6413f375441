import * as d3 from "d3";

/**
 * 渲染管理器类
 * 负责协调各个渲染器的工作
 */
export class RenderManager {
  constructor() {
    this.svg = null;
    this.container = null;
    this.zoom = null;
    this.width = 0;
    this.height = 0;

    // 渲染后的D3选择集
    this.renderedNodes = null;
    this.renderedLinks = null;
    this.renderedLabels = null;
    this.renderedLinkLabels = null;
    this.renderedLinkIcons = null;
  }

  /**
   * 初始化SVG容器
   * @param {HTMLElement} containerElement DOM容器元素
   * @param {number} width 宽度
   * @param {number} height 高度
   */
  initSVG(containerElement, width, height) {
    this.width = width;
    this.height = height;

    // 创建SVG元素
    this.svg = d3.select(containerElement).append("svg").attr("width", width).attr("height", height).attr("class", "knowledge-graph");

    // 添加SVG滤镜定义
    this.initFilters();

    // 添加缩放和平移功能
    this.initZoom();

    // 添加一个包含所有元素的容器，方便缩放
    this.container = this.svg.append("g");

    return this.svg;
  }

  /**
   * 初始化SVG滤镜
   */
  initFilters() {
    const defs = this.svg.append("defs");

    // 创建发光效果滤镜
    const glowFilter = defs.append("filter").attr("id", "glow").attr("x", "-50%").attr("y", "-50%").attr("width", "200%").attr("height", "200%");

    glowFilter.append("feGaussianBlur").attr("stdDeviation", "3.5").attr("result", "coloredBlur");

    const feMerge = glowFilter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "coloredBlur");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");
  }

  /**
   * 初始化缩放功能
   */
  initZoom() {
    this.zoom = d3
      .zoom()
      .scaleExtent([0.1, 8])
      .on("zoom", (event) => {
        if (this.container) {
          this.container.attr("transform", event.transform);
        }
      });

    this.svg.call(this.zoom);
  }

  /**
   * 创建动态发光滤镜
   * @param {string} color 颜色值
   * @returns {string} 滤镜ID
   */
  createDynamicGlowFilter(color) {
    if (!/^#([0-9A-F]{3}){1,2}$/i.test(color)) {
      console.warn(`无效的颜色格式 '${color}'`);
      return null;
    }

    const filterIdSuffix = color.replace("#", "");
    const filterId = `pure-glow-effect-${filterIdSuffix}`;
    const filterUrl = `url(#${filterId})`;

    const defs = this.svg.select("defs");
    if (defs.select(`#${filterId}`).empty()) {
      const newFilter = defs.append("filter").attr("id", filterId).attr("x", "-50%").attr("y", "-50%").attr("width", "200%").attr("height", "200%");

      newFilter.append("feMorphology").attr("operator", "dilate").attr("radius", "1.5").attr("in", "SourceAlpha").attr("result", "dilatedAlpha");

      newFilter.append("feGaussianBlur").attr("in", "dilatedAlpha").attr("stdDeviation", "3").attr("result", "blurredAlpha");

      newFilter.append("feFlood").attr("flood-color", color).attr("result", "glowColor");

      newFilter.append("feComposite").attr("in", "glowColor").attr("in2", "blurredAlpha").attr("operator", "in").attr("result", "finalPureGlow");

      // console.log(`已为颜色 ${color} 创建动态辉光滤镜 ${filterId}`);
    }

    return filterUrl;
  }

  /**
   * 更新SVG尺寸
   * @param {number} width 新宽度
   * @param {number} height 新高度
   */
  updateSize(width, height) {
    this.width = width;
    this.height = height;
    if (this.svg) {
      this.svg.attr("width", width).attr("height", height);
    }
  }

  /**
   * 重置缩放
   */
  resetZoom() {
    if (this.svg && this.zoom) {
      this.svg.transition().duration(750).call(this.zoom.transform, d3.zoomIdentity);
    }
  }

  /**
   * 缩放以适应节点
   * @param {Array} nodes 节点数组
   */
  zoomToFitNodes(nodes) {
    if (!nodes || nodes.length === 0 || !this.svg || !this.zoom) return;

    // 计算这些节点的包围盒
    let minX = Infinity,
      minY = Infinity;
    let maxX = -Infinity,
      maxY = -Infinity;

    nodes.forEach((node) => {
      minX = Math.min(minX, node.x);
      minY = Math.min(minY, node.y);
      maxX = Math.max(maxX, node.x);
      maxY = Math.max(maxY, node.y);
    });

    // 增加一些边距
    const padding = 50;
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;

    // 计算包围盒的宽度和高度
    const width = maxX - minX;
    const height = maxY - minY;

    // 计算缩放因子
    const scale =
      Math.min(
        this.width / width,
        this.height / height,
        2 // 最大放大倍数
      ) * 0.9; // 再缩小一点以确保完全可见

    // 计算中心点
    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;

    // 应用缩放和平移
    const transform = d3.zoomIdentity.translate(this.width / 2 - centerX * scale, this.height / 2 - centerY * scale).scale(scale);

    this.svg.transition().duration(750).call(this.zoom.transform, transform);
  }

  /**
   * 应用初始缩放
   * @param {number} initialScale 初始缩放级别
   * @param {Array} nodes 节点数组
   */
  applyInitialScale(initialScale, nodes) {
    if (!this.svg || !this.zoom) return;

    if (initialScale !== null) {
      let transform = d3.zoomIdentity.scale(initialScale);

      if (nodes && nodes.length > 0) {
        const graphCenterX = d3.mean(nodes, (d) => d.x);
        const graphCenterY = d3.mean(nodes, (d) => d.y);

        if (typeof graphCenterX === "number" && typeof graphCenterY === "number") {
          const translateX = this.width / 2 - graphCenterX * initialScale;
          const translateY = this.height / 2 - graphCenterY * initialScale;
          transform = d3.zoomIdentity.translate(translateX, translateY).scale(initialScale);
        }
      }

      this.svg.transition().duration(750).call(this.zoom.transform, transform);
    } else {
      // 缩放以适应所有节点并居中
      this.zoomToFitNodes(nodes);
    }
  }

  /**
   * 创建容器组
   * @param {string} className 类名
   * @returns {d3.Selection} D3选择集
   */
  createGroup(className) {
    return this.container.append("g").attr("class", className);
  }

  /**
   * 清除所有渲染内容
   */
  clear() {
    if (this.svg) {
      this.svg.selectAll("*").remove();
      this.svg.remove();
      this.svg = null;
      this.container = null;
    }

    this.renderedNodes = null;
    this.renderedLinks = null;
    this.renderedLabels = null;
    this.renderedLinkLabels = null;
    this.renderedLinkIcons = null;
  }

  /**
   * 添加点击空白区域事件
   * @param {Function} callback 回调函数
   */
  onBackgroundClick(callback) {
    if (this.svg) {
      this.svg.on("click", callback);
    }
  }

  /**
   * 获取渲染统计信息
   * @returns {Object} 统计信息
   */
  getRenderStats() {
    return {
      hasNodes: !!this.renderedNodes,
      hasLinks: !!this.renderedLinks,
      hasLabels: !!this.renderedLabels,
      hasLinkLabels: !!this.renderedLinkLabels,
      hasLinkIcons: !!this.renderedLinkIcons,
      nodesCount: this.renderedNodes ? this.renderedNodes.size() : 0,
      linksCount: this.renderedLinks ? this.renderedLinks.size() : 0,
      svgExists: !!this.svg,
      containerExists: !!this.container
    };
  }
}
