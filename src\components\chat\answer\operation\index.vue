<template>
  <div class="flex items-center absolute -top-6 left-0">
    <CopyBtn @copy="copy" />
    <RestoreBtn @click.native="handleRestore" />
  </div>
</template>

<script>
import CopyBtn from "./copy-btn";
import RestoreBtn from "./restore-btn";
import { copyTextToClipboard } from "@/utils/libs";
export default {
  name: "Operation",
  components: {
    CopyBtn,
    RestoreBtn
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    question: {
      type: String,
      default: ""
    },
    index: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  methods: {
    copy() {
      copyTextToClipboard(this.item?.content);
    },
    handleRegenerate() {
      this.$bus.$emit("regenerate", this.item, this.index);
    },
    handleRestore() {
      this.$bus.$emit("restore", this.item, this.index);
    }
  }
};
</script>

<style scoped lang="scss"></style>
