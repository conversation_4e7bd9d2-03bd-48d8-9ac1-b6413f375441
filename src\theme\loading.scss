@function get-color($variable, $default-color: #409eff) {
  @if type-of($variable) == color {
    @return $variable;
  } @else {
    @return $default-color;
  }
}

@function lighten-color($color, $amount: 30%) {
  @return lighten($color, $amount);
}

$loader-bg: var(--loader-bg);
$loader-size: var(--loader-size);
$loader-color: var(--loader-color);
$lighter-loader: lighten-color(get-color($loader-color));

.loading {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: $loader-bg;

  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .square {
      width: $loader-size;
      height: $loader-size;

      &-item {
        --dim: 33.333333%;
        width: var(--dim);
        height: var(--dim);
        background-color: $loader-color;
        float: left;
        animation: square-animation 1.2s infinite ease;
        border-radius: 1px;

        &:nth-child(1),
        &:nth-child(5),
        &:nth-child(9) {
          animation-delay: 0.2s;
        }

        &:nth-child(2),
        &:nth-child(6) {
          animation-delay: 0.3s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }

        &:nth-child(4),
        &:nth-child(8) {
          animation-delay: 0.1s;
        }

        &:nth-child(7) {
          animation-delay: 0s;
        }
      }

      @keyframes square-animation {
        0%,
        70%,
        100% {
          transform: scale3D(1, 1, 1);
        }

        35% {
          transform: scale3D(0, 0, 1);
        }
      }
    }

    .circle {
      width: $loader-size;
      height: $loader-size;
      position: relative;
      animation: circle-animation 2s linear infinite;

      &-item {
        --dim: 1.2rem;
        width: var(--dim);
        height: var(--dim);
        background-color: $loader-color;
        border-radius: 50%;
        position: absolute;

        &:nth-child(1) {
          top: 0;
          left: 0;
        }

        &:nth-child(2) {
          top: 0;
          right: 0;
        }

        &:nth-child(3) {
          bottom: 0;
          left: 0;
        }

        &:nth-child(4) {
          bottom: 0;
          right: 0;
        }
      }

      @keyframes circle-animation {
        0% {
          transform: scale(1) rotate(0);
        }

        20%,
        25% {
          transform: scale(1.3) rotate(90deg);
        }

        45%,
        50% {
          transform: scale(1) rotate(180deg);
        }

        70%,
        75% {
          transform: scale(1.3) rotate(270deg);
        }

        95%,
        100% {
          transform: scale(1) rotate(360deg);
        }
      }
    }

    .dot {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;

      &-item {
        width: $loader-size;
        height: $loader-size;
        margin-right: 10px;
        border-radius: 50%;
        background-color: $lighter-loader;
        animation: pulse 1.5s infinite ease-in-out;

        &:last-child {
          margin-right: 0;
        }

        &:nth-child(1) {
          animation-delay: -0.3s;
        }

        &:nth-child(2) {
          animation-delay: -0.1s;
        }

        &:nth-child(3) {
          animation-delay: 0.1s;
        }
      }

      @keyframes pulse {
        0% {
          transform: scale(0.8);
          background-color: $lighter-loader;
        }

        50% {
          transform: scale(1.2);
          background-color: $loader-color;
        }

        100% {
          transform: scale(0.8);
          background-color: $lighter-loader;
        }
      }
    }
  }
}
