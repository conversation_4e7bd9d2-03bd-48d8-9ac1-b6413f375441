<template>
  <div class="list-container">
    <div
      class="border border-orange-200 border-solid rounded text-orange-400 p-px my-1"
      v-for="(item, index) in data"
      :key="index"
    >
      {{ item }}
    </div>
  </div>
</template>

<script>
export default {
  name: "ListCard",
  props: {
    data: {
      type: Array,

      default: () => []
    }
  }
};
</script>
<style lang="scss" scoped></style>
