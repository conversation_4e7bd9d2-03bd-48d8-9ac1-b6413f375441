<template>
  <div
    class="knowledge-graph-map"
    ref="containerRef"
  >
    <div
      class="map-container"
      ref="mapRef"
    ></div>
    <svg
      class="graph-container"
      ref="graphRef"
    ></svg>
  </div>
</template>

<script>
import * as echarts from "echarts";
import * as d3 from "d3";
import guangdongGeoJson from "./assets/geo/guangdong.json";

export default {
  name: "KnowledgeGraphMap",
  props: {
    graphData: {
      type: Object,
      default: () => ({
        nodes: [],
        links: []
      })
    },
    mapOptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null,
      svg: null,
      simulation: null
    };
  },
  watch: {
    graphData: {
      deep: true,
      handler() {
        this.updateGraph();
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap();
      setTimeout(() => {
        this.initGraph();
      }, 100);
    });
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    if (this.simulation) {
      this.simulation.stop();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    initMap() {
      try {
        this.chart = echarts.init(this.$refs.mapRef);

        // Register Guangdong map with imported GeoJSON data
        echarts.registerMap("guangdong", guangdongGeoJson);

        const option = {
          backgroundColor: "#1a1a2e",
          tooltip: {
            trigger: "item",
            formatter: "{b}"
          },
          geo: {
            map: "guangdong",
            roam: true,
            zoom: 1.2,
            center: [113.3, 23.0],
            label: {
              show: true,
              fontSize: 12,
              color: "#ffffff"
            },
            itemStyle: {
              areaColor: "#16213e",
              borderColor: "#0f4c75",
              borderWidth: 2,
              shadowBlur: 10,
              shadowColor: "rgba(15, 76, 117, 0.5)"
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 12,
                color: "#000"
              },
              itemStyle: {
                areaColor: "#0f4c75",
                borderColor: "#3282b8",
                borderWidth: 3,
                shadowBlur: 20,
                shadowColor: "rgba(50, 130, 184, 0.8)"
              }
            },
            select: {
              itemStyle: {
                areaColor: "#bdd7e7"
              }
            }
          },
          ...this.mapOptions
        };

        this.chart.setOption(option);
      } catch (error) {
        console.error("Map initialization failed:", error);
        this.$message && this.$message.error("Map initialization failed");
      }

      // Listen to map zoom and pan events
      this.chart.on("georoam", this.updateGraphPosition);

      // Listen to window resize
      window.addEventListener("resize", this.handleResize);
    },

    initGraph() {
      const width = this.$refs.containerRef.clientWidth;
      const height = this.$refs.containerRef.clientHeight;

      this.svg = d3.select(this.$refs.graphRef).attr("width", width).attr("height", height);

      // Create zoom behavior
      const zoom = d3
        .zoom()
        .scaleExtent([0.1, 10])
        .on("zoom", (event) => {
          this.svg.select(".graph-group").attr("transform", event.transform);
        });

      this.svg.call(zoom);

      const g = this.svg.append("g").attr("class", "graph-group");

      // Create nodes
      const node = g
        .append("g")
        .attr("class", "nodes")
        .selectAll("g")
        .data(this.graphData.nodes)
        .enter()
        .append("g")
        .call(d3.drag().on("start", this.dragstarted).on("drag", this.dragged).on("end", this.dragended));

      // Add node circles
      node
        .append("circle")
        .attr("r", (d) => d.size || 10)
        .attr("fill", (d) => d.color || "#69b3a2")
        .attr("stroke", "#fff")
        .attr("stroke-width", 2)
        .style("filter", "drop-shadow(0 0 5px rgba(255, 255, 255, 0.5))");

      // Add node labels
      node
        .append("text")
        .text((d) => d.name)
        .attr("x", 0)
        .attr("y", -15)
        .attr("text-anchor", "middle")
        .attr("font-size", "12px")
        .attr("fill", "#ffffff")
        .style("text-shadow", "1px 1px 2px rgba(0, 0, 0, 0.8)");

      // Create force simulation
      this.simulation = d3
        .forceSimulation(this.graphData.nodes)
        .force(
          "link",
          d3
            .forceLink(this.graphData.links)
            .id((d) => d.id)
            .distance(50)
        )
        .force("charge", d3.forceManyBody().strength(-300))
        .force("center", d3.forceCenter(width / 2, height / 2));

      this.simulation.on("tick", () => {
        this.updateGraphPosition();
      });
    },

    updateGraphPosition() {
      if (!this.chart || !this.svg) return;

      const nodes = this.svg.selectAll(".nodes g");
      const links = this.svg.selectAll(".links line");

      // Convert geo coordinates to screen coordinates
      const self = this;
      nodes.each(function (d) {
        if (d.geo) {
          const screenCoord = self.chart.convertToPixel("geo", d.geo);
          if (screenCoord) {
            d.fx = screenCoord[0];
            d.fy = screenCoord[1];
          }
        }
      });

      // Update node positions
      nodes.attr("transform", (d) => `translate(${d.x || d.fx || 0},${d.y || d.fy || 0})`);

      // Update link positions
      links
        .attr("x1", (d) => d.source.x || d.source.fx || 0)
        .attr("y1", (d) => d.source.y || d.source.fy || 0)
        .attr("x2", (d) => d.target.x || d.target.fx || 0)
        .attr("y2", (d) => d.target.y || d.target.fy || 0);
    },

    dragstarted(event, d) {
      if (!event.active) this.simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    },

    dragged(event, d) {
      d.fx = event.x;
      d.fy = event.y;
    },

    dragended(event, d) {
      if (!event.active) this.simulation.alphaTarget(0);
      if (!d.geo) {
        d.fx = null;
        d.fy = null;
      }
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
      if (this.svg) {
        const width = this.$refs.containerRef.clientWidth;
        const height = this.$refs.containerRef.clientHeight;
        this.svg.attr("width", width).attr("height", height);
        if (this.simulation) {
          this.simulation.force("center", d3.forceCenter(width / 2, height / 2));
          this.simulation.alpha(0.3).restart();
        }
      }
    },

    updateGraph() {
      if (!this.svg) return;

      // Clear existing content
      this.svg.selectAll(".graph-group").remove();

      // Reinitialize graph
      this.initGraph();
    },

    refresh() {
      this.handleResize();
      this.updateGraphPosition();
    }
  }
};
</script>

<style scoped lang="scss">
.knowledge-graph-map {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .map-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  .graph-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;

    ::v-deep .nodes {
      pointer-events: all;
      cursor: pointer;

      g:hover {
        circle {
          filter: brightness(1.2);
        }
        text {
          font-weight: bold;
        }
      }
    }

    ::v-deep .links {
      pointer-events: none;
    }
  }
}
</style>
