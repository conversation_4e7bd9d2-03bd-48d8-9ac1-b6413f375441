/**
 * 右键菜单管理器类
 */
export class MenuManager {
  constructor(graphContainer) {
    this.graphContainer = graphContainer; // 图谱的容器元素
    this.menuElement = null; // 菜单的DOM元素
    this.menuItems = []; // 菜单项 { label: string, action: string, callback: function }
    this.isVisible = false;
    this.hideTimeout = null; // 用于延迟隐藏
    this._createMenuElement();
  }

  /**
   * 创建菜单的DOM结构
   * @private
   */
  _createMenuElement() {
    this.menuElement = document.createElement("div");
    this.menuElement.className = "flow-graph-context-menu"; // 主要样式通过此类名应用
    this.menuElement.style.display = "none"; // <--- 明确设置初始为隐藏
    // position 和 zIndex 由SCSS控制
    this.graphContainer.appendChild(this.menuElement);

    // 点击菜单外部时隐藏菜单
    document.addEventListener("click", (event) => {
      if (this.isVisible && this.menuElement && !this.menuElement.contains(event.target)) {
        this.hide();
      }
    });

    // 防止在菜单内部右键点击时触发浏览器默认菜单
    this.menuElement.addEventListener("contextmenu", (event) => {
      event.preventDefault();
    });

    // 鼠标离开菜单时，延迟隐藏
    this.menuElement.addEventListener("mouseleave", () => {
      this.hideTimeout = setTimeout(() => {
        this.hide();
      }, 300);
    });

    // 鼠标进入菜单时，取消延迟隐藏
    this.menuElement.addEventListener("mouseenter", () => {
      if (this.hideTimeout) {
        clearTimeout(this.hideTimeout);
        this.hideTimeout = null;
      }
    });
  }

  /**
   * 设置菜单项
   * @param {Array<Object>} items - 菜单项数组 e.g. [{ label: 'Reload', action: 'reload', iconClass: 'fas fa-redo' }]
   */
  setMenuItems(items) {
    this.menuItems = items;
    this._renderMenuItems();
  }

  /**
   * 渲染菜单项到DOM
   * @private
   */
  _renderMenuItems() {
    this.menuElement.innerHTML = "";
    const ul = document.createElement("ul");

    this.menuItems.forEach((item, index) => {
      // 支持分割线
      if (item.type === "divider") {
        const divider = document.createElement("li");
        divider.className = "menu-divider";
        ul.appendChild(divider);
        return;
      }

      const li = document.createElement("li");

      if (item.iconClass) {
        const icon = document.createElement("i");
        icon.className = item.iconClass;
        li.appendChild(icon);
      }

      const text = document.createTextNode(item.label);
      li.appendChild(text);

      // 添加禁用状态支持
      if (item.disabled) {
        li.style.opacity = "0.5";
        li.style.cursor = "not-allowed";
        li.style.pointerEvents = "none";
      }

      li.addEventListener("click", (event) => {
        event.stopPropagation(); // 防止触发外部的隐藏逻辑
        this.hide();
        if (item.callback && typeof item.callback === "function" && !item.disabled) {
          item.callback(item.action); // 执行回调，传递action
        }
      });

      // 添加子菜单项悬停效果
      li.addEventListener("mouseenter", () => {
        // 可以在这里添加子菜单展开逻辑
        if (item.submenu) {
          // TODO: 实现子菜单功能
        }
      });

      ul.appendChild(li);
    });
    this.menuElement.appendChild(ul);
  }

  /**
   * 在指定位置显示菜单
   * @param {number} x - 屏幕X坐标
   * @param {number} y - 屏幕Y坐标
   */
  show(x, y) {
    // 将屏幕坐标转换为相对于 graphContainer 的坐标
    const containerRect = this.graphContainer.getBoundingClientRect();
    const relativeX = x - containerRect.left;
    const relativeY = y - containerRect.top;

    // 清除可能存在的隐藏延时
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    // 设置菜单位置
    this.menuElement.style.left = `${relativeX}px`;
    this.menuElement.style.top = `${relativeY}px`;

    // 显示菜单
    this.menuElement.style.display = "block";

    // 检查菜单是否会超出容器边界，如果是则调整位置
    this._adjustMenuPosition();

    // 添加动画类
    this.menuElement.classList.remove("animate-out");
    this.menuElement.classList.add("animate-in", "show");

    this.isVisible = true;

    // 自动聚焦到第一个可用的菜单项
    const firstMenuItem = this.menuElement.querySelector("li:not(.menu-divider):not([style*='pointer-events: none'])");
    if (firstMenuItem) {
      firstMenuItem.focus();
    }
  }

  /**
   * 调整菜单位置以防止超出容器边界
   * @private
   */
  _adjustMenuPosition() {
    const menuRect = this.menuElement.getBoundingClientRect();
    const containerRect = this.graphContainer.getBoundingClientRect();

    let newLeft = parseFloat(this.menuElement.style.left);
    let newTop = parseFloat(this.menuElement.style.top);

    // 检查右边界
    if (menuRect.right > containerRect.right) {
      newLeft = containerRect.width - menuRect.width - 10;
    }

    // 检查底边界
    if (menuRect.bottom > containerRect.bottom) {
      newTop = containerRect.height - menuRect.height - 10;
    }

    // 确保不会超出左边界和上边界
    newLeft = Math.max(10, newLeft);
    newTop = Math.max(10, newTop);

    this.menuElement.style.left = `${newLeft}px`;
    this.menuElement.style.top = `${newTop}px`;
  }

  /**
   * 隐藏菜单
   */
  hide() {
    if (this.menuElement && this.isVisible) {
      // 添加退出动画
      this.menuElement.classList.remove("animate-in", "show");
      this.menuElement.classList.add("animate-out");

      // 等待动画完成后隐藏
      setTimeout(() => {
        if (this.menuElement) {
          this.menuElement.style.display = "none";
          this.menuElement.classList.remove("animate-out");
        }
      }, 150); // 匹配CSS动画时间
    }
    this.isVisible = false;

    // 清除隐藏延时
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
  }

  /**
   * 切换菜单显示状态
   * @param {number} x - 屏幕X坐标
   * @param {number} y - 屏幕Y坐标
   */
  toggle(x, y) {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show(x, y);
    }
  }

  /**
   * 检查菜单是否可见
   * @returns {boolean}
   */
  get visible() {
    return this.isVisible;
  }

  /**
   * 清理资源，移除事件监听器和DOM元素
   */
  destroy() {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    if (this.menuElement) {
      // 事件监听器在 _createMenuElement 中添加到了 document 和 menuElement 上
      // document 上的监听器通常不需要在这里移除，除非 MenuManager 实例频繁创建和销毁
      // menuElement 自身的监听器会随着元素移除而自动清理
      this.graphContainer.removeChild(this.menuElement);
      this.menuElement = null;
    }
    this.menuItems = [];
    this.isVisible = false;
    // 注意：如果 _createMenuElement 中的 document 事件监听器需要精确管理，
    // 则应保存该监听器的引用并在 destroy 中移除。
    // 为简单起见，此处假设 MenuManager 生命周期与组件实例一致。
  }
}
