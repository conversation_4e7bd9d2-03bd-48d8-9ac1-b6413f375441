<template>
  <div class="layout-link-container wh-100">
    <i class="layout-link-icon el-icon-s-promotion"></i>
    <div class="layout-link-msg">页面 "{{ meta && meta.title }}" 已在新窗口中打开</div>
    <el-button
      class="mt-8"
      round
      size="small"
      @click="onGotoFullPage"
    >
      <i class="iconfont icon-lianjie"></i>
      <span>立即前往体验</span>
    </el-button>
  </div>
</template>

<script>
export default {
  name: "layoutLinkView",
  props: {
    meta: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    // 立即前往
    onGotoFullPage() {
      window.open(this.meta.isLink);
    }
  }
};
</script>

<style scoped lang="scss">
.layout-link-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  i.layout-link-icon {
    font-size: 100px;
    color: var(--nari-c-primary);
  }
  .layout-link-msg {
    font-size: 12px;
    opacity: 0.7;
    margin-top: 15px;
  }
}
</style>
