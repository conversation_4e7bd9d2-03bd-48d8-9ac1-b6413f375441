.label-title {
  display: flex;
  justify-content: flex-start;
  font-size: 18px;
  align-items: center;
  font-weight: bold;
  padding: 10px;
  &::before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 15px;
    margin-right: 5px;
    background: var(--nari-c-primary);
  }
}

.el-collapse {
  border-top: none !important;
  border-bottom: 1px solid rgba(30, 55, 95, 0.5) !important;
}

.nari-card {
  height: 100%;
}

.nari-card__body {
  padding: 5px;
}

.el-input__inner,
.el-textarea__inner {
  border-color: #4f6b7f;
}

.el-table {
  background-color: transparent;
}

.el-table::before {
  background-color: transparent;
}

.el-table--mini .el-table__cell {
  padding: 1px 0;
}

.el-table th.el-table__cell {
  color: #3dcafc;
  background: linear-gradient(180deg, #01173a, #14346b);

  &.is-leaf {
    border-bottom: 1px solid #14346b;
  }
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf,
.el-table--border,
.el-table--border th.el-table__cell,
.el-table__fixed-right-patch {
  border-color: transparent;
}

.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: transparent;
}

/* 设置表格行的渐变背景 */
.el-table tr {
  background: linear-gradient(to top, rgba(14, 34, 75, 0.7), rgba(10, 23, 48, 0.4)) !important;
  border-bottom: 1px solid rgba(30, 55, 95, 0.5) !important;
}

/* 鼠标悬停效果 */
.el-table tr:hover {
  background: linear-gradient(to top, rgba(25, 80, 160, 0.9), rgba(20, 60, 120, 0.7), rgba(15, 50, 100, 0.5)) !important;
  box-shadow: 0 0 15px rgba(77, 166, 255, 0.3);
}

/* 移除鼠标悬停时的单元格背景色 */
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: transparent !important;
}

/* 为每行添加底部阴影效果 */
.el-table tr td {
  box-shadow: inset 0 -1px 0 rgba(61, 103, 163, 0.3);
}

.tabs-container {
  width: 100%;
  .el-tabs {
    .el-tabs__nav,
    .el-tabs__item {
      border: none;
    }
    .el-tabs__header {
      border-bottom-color: #3e60c6;
      margin-bottom: 0;
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev,
    .el-tabs__item {
      color: #fff;
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 30px;
    }
    .el-tabs__item {
      height: 25px;
      line-height: 25px;
      margin-bottom: 5px;
      position: relative;
      &::after {
        content: "";
        display: block;
        height: 100%;
        width: 80%;
        background: #102e60;
        border-radius: 4px;
        position: absolute;
        top: 0;
        left: 10%;
        z-index: -1;
        transform: skew(-15deg);
      }
      &.is-active {
        &::after {
          background: linear-gradient(120deg, #6d1ab6 30%, #2e61ee 100%);
        }
      }
    }
    .el-tab-pane {
      font-size: 16px;
    }
  }
}

.selection-card-dialog {
  .el-dialog__body {
    padding: 0 10px 10px 10px;
    text-align: center;
    img {
      display: block;
      width: 100%;
      height: auto;
    }
  }
}
