<template>
  <div class="wrapper absolute inset-0 rounded-[24px]">
    <div class="absolute top-[2px] inset-[1.5px] flex items-center pl-[14px] pr-[8px] py-[14px] bg-primary-25 rounded-[24px] overflow-hidden">
      <canvas
        ref="canvasRef"
        id="voice-input-record"
        class="absolute left-0 bottom-0 w-full h-4"
      />
      <svg-icon
        v-if="startConvert"
        class="mr-2 w-4 h-4 text-primary-700 animate-spin"
        icon-name="loader"
      ></svg-icon>
      <div class="grow">
        <div
          class="text-sm text-gray-500"
          v-if="startRecord"
        >
          现在讲...
        </div>
        <div
          class="convert text-sm"
          v-if="startConvert"
        >
          正在转换为文本...
        </div>
      </div>
      <div
        v-if="startRecord"
        class="flex justify-center items-center mr-1 cursor-pointer"
        @click="handleStopRecorder"
      >
        <svg-icon
          class="text-primary-500"
          icon-name="pause"
        ></svg-icon>
      </div>
      <div
        v-if="startConvert"
        class="flex justify-center items-center mr-1 w-8 h-8 hover:bg-gray-200 rounded-lg cursor-pointer"
        @click="$emit('onCancel')"
      >
        <svg-icon
          class="text-gray-500"
          icon-name="close"
        ></svg-icon>
      </div>
      <div
        class="`w-[45px] pl-1 text-xs font-medium"
        :class="originDuration > 110 ? 'text-[#F04438]' : 'text-gray-700'"
      >
        {{ `0${minutes.toFixed(0)}:${seconds >= 10 ? seconds : `0${seconds}`}` }}
      </div>
    </div>
  </div>
</template>

<script>
import Recorder from "js-audio-recorder";
import { convertToMp3 } from "./utils";
import { voiceApi } from "@/api/voice";
export default {
  name: "VoiceInput",
  props: {
    id: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      recorder: new Recorder({
        sampleBits: 16,
        sampleRate: 16000,
        numChannels: 1,
        compiling: false
      }),
      canvasRef: null,
      ctxRef: null,
      drawRecordId: null,
      clearInterval: null,
      originDuration: 0,
      startRecord: false,
      startConvert: false,
      params: {},
      pathname: ""
    };
  },
  computed: {
    minutes() {
      return parseInt(parseInt(this.originDuration) / 60);
    },
    seconds() {
      return parseInt(this.originDuration) % 60;
    }
  },
  created() {
    this.params = this.$route.params || {};
    this.pathname = this.$route.path;
  },
  mounted() {
    this.initCanvas();
    this.handleStartRecord();
  },
  beforeDestroy() {
    this.recorder && this.recorder.stop();
    clearInterval(this.clearInterval);
  },
  methods: {
    setTime() {
      this.clearInterval = setInterval(() => {
        this.originDuration += 1;
      }, 1000);
    },
    drawRecord() {
      this.drawRecordId = requestAnimationFrame(this.drawRecord);
      const canvas = this.canvasRef;
      const ctx = this.ctxRef;
      const dataUnit8Array = this.recorder.getRecordAnalyseData();
      const dataArray = [].slice.call(dataUnit8Array);
      const lineLength = parseInt(`${canvas.width / 3}`);
      const gap = parseInt(`${1024 / lineLength}`);

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.beginPath();
      let x = 0;
      for (let i = 0; i < lineLength; i++) {
        let v =
          dataArray.slice(i * gap, i * gap + gap).reduce((prev, next) => {
            return prev + next;
          }, 0) / gap;

        if (v < 128) v = 128;
        if (v > 178) v = 178;
        const y = ((v - 128) / 50) * canvas.height;

        ctx.moveTo(x, 16);
        if (ctx.roundRect) ctx.roundRect(x, 16 - y, 2, y, [1, 1, 0, 0]);
        else ctx.rect(x, 16 - y, 2, y);
        ctx.fill();
        x += 3;
      }
      ctx.closePath();
    },
    async handleStopRecorder() {
      clearInterval(this.clearInterval);
      this.startRecord = false;
      this.startConvert = true;
      this.recorder.stop();
      this.drawRecordId && cancelAnimationFrame(this.drawRecordId);
      this.drawRecordId = null;
      const canvas = this.canvasRef;
      const ctx = this.ctxRef;
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      const mp3Blob = convertToMp3(this.recorder);
      const mp3File = new File([mp3Blob], "temp.mp3", { type: "audio/mp3" });
      const formData = new FormData();
      formData.append("audio", mp3File);
      try {
        const audioResponse = await voiceApi.audioToText(formData);
        this.$emit("onConverted", audioResponse.results);
        this.$emit("onCancel");
      } catch (e) {
        console.log("e: ", e);
        this.$emit("onConverted", "");
        this.$emit("onCancel");
      }
    },
    async handleStartRecord() {
      this.setTime();
      try {
        await this.recorder.start();
        this.startRecord = true;
        this.startConvert = false;

        if (this.canvasRef && this.ctxRef) this.drawRecord();
      } catch (e) {
        this.$emit("onCancel");
      }
    },
    initCanvas() {
      const dpr = window.devicePixelRatio || 1;
      const canvas = document.getElementById("voice-input-record");

      if (canvas) {
        const { width: cssWidth, height: cssHeight } = canvas.getBoundingClientRect();

        canvas.width = dpr * cssWidth;
        canvas.height = dpr * cssHeight;
        this.canvasRef = canvas;

        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.scale(dpr, dpr);
          ctx.fillStyle = "rgba(209, 224, 255, 1)";
          this.ctxRef = ctx;
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.wrapper {
  background: linear-gradient(131deg, #2250f2, #0ebcf3);
  box-shadow: 0 4px 6px -2px rgba(16, 24, 40, 0.03), 0 12px 16px -4px rgba(16, 24, 40, 0.08);
}

.convert {
  background: linear-gradient(91.92deg, #104ae1 -1.74%, #0098ee 75.74%);
  background-clip: text;
  color: transparent;
}
</style>
