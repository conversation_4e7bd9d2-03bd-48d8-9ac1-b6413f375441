<template>
  <div
    class="dot-flashing"
    :class="`${type === 'text' ? 'text' : 'avatar'}`"
  ></div>
</template>

<script>
export default {
  name: "LoaidingAnim",
  props: {
    type: {
      type: String,
      default: "text"
    }
  }
};
</script>

<style scoped lang="scss">
.dot-flashing {
  position: relative;
  animation: 1s infinite linear alternate;
  animation-delay: 0.5s;
  &::before,
  &::after {
    content: "";
    display: inline-block;
    position: absolute;
    top: 0;
    animation: 1s infinite linear alternate;
  }
  &::before {
    animation-delay: 0s;
  }
  &::after {
    animation-delay: 1s;
  }
}

.text,
.text::before,
.text::after {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #667085;
  color: #667085;
  animation-name: dot-flashing;
}

.text::before {
  left: -7px;
}

.text::after {
  left: 7px;
}

.avatar,
.avatar::before,
.avatar::after {
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: #155eef;
  color: #155eef;
  animation-name: dot-flashing-avatar;
}

.avatar::before {
  left: -5px;
}

.avatar::after {
  left: 5px;
}

@keyframes dot-flashing {
  0% {
    background-color: #667085;
  }

  50%,
  100% {
    background-color: rgba(102, 112, 133, 0.3);
  }
}

@keyframes dot-flashing-avatar {
  0% {
    background-color: #155eef;
  }

  50%,
  100% {
    background-color: rgba(21, 94, 239, 0.3);
  }
}
</style>
