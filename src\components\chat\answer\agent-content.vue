<template>
  <Markdown
    v-if="item.annotation?.logAnnotation"
    :content="item.annotation?.logAnnotation.content || ''"
  />
  <div v-else>
    <div
      v-for="(thought, index) in item.agent_thoughts"
      :key="index"
    >
      <Markdown
        v-if="thought.thought"
        :content="thought.thought"
      />
      <Thought
        v-if="!!item.thought.tool"
        :thought="item.thought"
        :allToolIcons="allToolIcons"
        :isFinished="!!thought.observation || !responding"
      />

      <ImageGallery
        v-if="getImgs(thought.message_files).length > 0"
        :srcs="getImgs(thought.message_files).map((file) => file.url)"
      />
    </div>
  </div>
</template>

<script>
import Markdown from "@/components/base/Markdown";
import ImageGallery from "@/components/base/ImageGallery";
import Thought from "../thought";
export default {
  name: "AgentContent",
  components: {
    Markdown,
    ImageGallery,
    Thought
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    responding: {
      type: Boolean,
      default: false
    },
    allToolIcons: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {
    getImgs(list) {
      if (!list) return [];
      return list.filter((file) => file.type === "image" && file.belongs_to === "assistant");
    }
  }
};
</script>

<style scoped lang="scss"></style>
