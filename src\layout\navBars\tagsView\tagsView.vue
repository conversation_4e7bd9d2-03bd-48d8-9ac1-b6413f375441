<template>
  <div class="layout-navbars-tagsview">
    <el-scrollbar
      ref="scrollbarRef"
      @wheel.native.prevent="onHandleScroll"
    >
      <ul
        class="layout-navbars-tagsview-ul"
        style="height: 35px"
        :class="setTagsStyle"
        ref="tagsUlRef"
      >
        <li
          v-for="(item, index) in tagsViewList"
          :key="index"
          class="layout-navbars-tagsview-ul-li"
          :data-name="item.name"
          :class="{ 'is-active': item.path === tagsRoutePath }"
          @contextmenu.prevent="onContextmenu(item, $event)"
          @click="onTagsClick(item, index)"
          ref="tagsRefs"
        >
          <span>{{ item.meta.title }}</span>
          <!-- <i
            class="el-icon-refresh-right layout-navbars-tagsview-ul-li-icon ml-1"
            v-if="item.path === tagsRoutePath"
            @click.stop="refreshCurrentTagsView(item.path)"
          ></i> -->
          <i
            class="el-icon-close layout-navbars-tagsview-ul-li-icon ml-1"
            v-if="item.meta.close"
            @click.stop="closeTagsView(item.path)"
          ></i>
          <div
            v-if="getThemeConfig.tagsStyle !== 'card'"
            class="layout-navbars-tagsview-ul-li-schedule"
          />
        </li>
      </ul>
    </el-scrollbar>
    <Contextmenu
      ref="tagsContextmenu"
      :dropdown="tagsDropdown"
      :tagsViewList="tagsViewList"
      @currentContextmenuClick="onCurrentContextmenuClick"
    />
  </div>
</template>

<script>
import Contextmenu from "@/layout/navBars/tagsView/contextmenu";
import { Session } from "@/utils/storage.js";
export default {
  name: "tagsView",
  components: { Contextmenu },
  data() {
    return {
      userInfo: {},
      tagsViewList: [],
      tagsDropdown: {
        x: "",
        y: ""
      },
      tagsRefsIndex: 0,
      tagsRoutePath: this.$route.path,
      tagsViewRoutesList: []
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    // 动态设置 tagsView 风格样式
    setTagsStyle() {
      return `tags-${this.getThemeConfig.tagsStyle}`;
    }
  },
  watch: {
    // 监听路由变化
    $route: {
      handler(to) {
        this.tagsRoutePath = to.path;
        this.addTagsView(to.path, to);
        this.getTagsRefsIndex(to.path);
        this.tagsViewmoveToCurrentTag();
      },
      deep: true
    }
  },
  created() {
    // 监听非本页面调用 0 刷新当前，1 关闭当前，2 关闭其它，3 关闭全部
    this.$bus.$on("onCurrentContextmenuClick", (data) => {
      this.onCurrentContextmenuClick(data);
    });
  },
  mounted() {
    this.initTagsViewList();
  },
  destroyed() {
    this.$bus.$off("onCurrentContextmenuClick");
  },
  methods: {
    // 获取路由信息
    getRoutesList() {
      return this.$store.state.routesList.routesList;
    },
    // 当前的 tagsView 项点击时
    onTagsClick(item, index) {
      this.tagsRoutePath = item.path;
      this.tagsRefsIndex = index;
      this.$router.push(item);
    },
    // 获取 tagsView 的下标：用于处理 tagsView 点击时的横向滚动
    getTagsRefsIndex(path) {
      if (this.tagsViewList.length) {
        this.tagsRefsIndex = this.tagsViewList.findIndex((item) => item.path === path);
      }
    },
    // 鼠标滚轮滚动
    onHandleScroll(e) {
      this.$refs.scrollbarRef.$refs.wrap.scrollLeft += e.wheelDelta / 4;
    },
    // tagsView 横向滚动
    tagsViewmoveToCurrentTag() {
      this.$nextTick(() => {
        const tagsRefs = this.$refs.tagsRefs;
        if (tagsRefs.length <= 0) return false;
        // 当前 li 元素
        const liDom = tagsRefs[this.tagsRefsIndex];
        // 当前 li 元素下标
        const liIndex = this.tagsRefsIndex;
        // 当前 ul 下 li 元素总长度
        const liLength = tagsRefs.length;
        // 最前 li
        const liFirst = tagsRefs[0];
        // 最后 li
        const liLast = tagsRefs[tagsRefs.length - 1];
        // 当前滚动条的值
        const scrollRefs = this.$refs.scrollbarRef.$refs.wrap;
        // 当前滚动条滚动宽度
        const scrollS = scrollRefs.scrollWidth;
        // 当前滚动条偏移宽度
        const offsetW = scrollRefs.offsetWidth;
        // 当前滚动条偏移距离
        const scrollL = scrollRefs.scrollLeft;
        // 上一个 tags li dom
        const liPrevTag = tagsRefs[this.tagsRefsIndex - 1];
        // 下一个 tags li dom
        const liNextTag = tagsRefs[this.tagsRefsIndex + 1];
        // 上一个 tags li dom 的偏移距离
        let beforePrevL = "";
        // 下一个 tags li dom 的偏移距离
        let afterNextL = "";
        if (liDom === liFirst) {
          // 头部
          scrollRefs.scrollLeft = 0;
        } else if (liDom === liLast) {
          // 尾部
          scrollRefs.scrollLeft = scrollS - offsetW;
        } else {
          // 非头/尾部
          if (liIndex === 0) beforePrevL = liFirst?.offsetLeft - 5;
          else beforePrevL = liPrevTag?.offsetLeft - 5;
          if (liIndex === liLength) {
            afterNextL = liLast?.offsetLeft + liLast.offsetWidth + 5;
          } else afterNextL = liNextTag?.offsetLeft + liNextTag.offsetWidth + 5;
          if (afterNextL > scrollL + offsetW) {
            scrollRefs.scrollLeft = afterNextL - offsetW;
          } else if (beforePrevL < scrollL) {
            scrollRefs.scrollLeft = beforePrevL;
          }
        }
        // 更新滚动条，防止不出现
        this.updateScrollbar();
      });
    },
    // 更新滚动条显示
    updateScrollbar() {
      this.$refs.scrollbarRef.update();
    },
    // 存储 tagsViewList 到浏览器临时缓存中，页面刷新时，保留记录
    addBrowserSetSession(tagsViewList) {
      Session.set("tagsViewList", tagsViewList);
      const KeepAliveList = tagsViewList.filter((item) => item.meta.isKeepAlive).map((item) => item.path);
      this.$store.commit("keepAliveNames/setKeepAliveName", KeepAliveList);
    },
    // 初始化设置了 tagsView 数据
    initTagsViewList() {
      this.tagsRoutePath = this.$route.path;
      this.tagsViewList = [];
      if (!this.getThemeConfig.isCacheTagsView) {
        Session.remove("tagsViewList");
      }
      this.tagsViewRoutesList = this.$store.getters["routesList/flatMenuListGet"];
      const cachedTagsViewList = Session.get("tagsViewList");
      if (cachedTagsViewList && this.getThemeConfig.isCacheTagsView) {
        this.tagsViewList = cachedTagsViewList;
        const KeepAliveList = cachedTagsViewList.filter((item) => item.meta.isKeepAlive).map((item) => item.path);
        this.$store.commit("keepAliveNames/setKeepAliveName", KeepAliveList);
      }
      this.addTagsView(this.$route.path);
      // 初始化当前元素(li)的下标
      this.getTagsRefsIndex(this.$route.path);
      // 添加初始化横向滚动条移动到对应位置
      this.tagsViewmoveToCurrentTag();
    },
    // 添加 tagsView：未设置隐藏（isHide）也添加到在 tagsView 中
    addTagsView(path, to) {
      if (this.tagsViewList.some((item) => item.path === path)) return;
      const item = this.tagsViewRoutesList.find((item) => item.path === path);
      if (!item || (item.meta.isLink && !item.meta.isIframe)) return;
      const tabsParams = {
        ...item,
        query: to?.query || this.$route.query
      };
      this.tagsViewList.push(tabsParams);
      this.addBrowserSetSession(this.tagsViewList);
    },
    // 右键菜单点击时显示菜单列表
    onContextmenu(item, e) {
      const { clientX, clientY } = e;
      this.tagsDropdown.x = clientX;
      this.tagsDropdown.y = clientY;
      this.$refs.tagsContextmenu.openContextmenu(item);
    },
    // 当前项右键菜单点击
    onCurrentContextmenuClick(data) {
      const { id, path } = data;
      switch (id) {
        case "refresh":
          this.refreshCurrentTagsView(path);
          break;
        case "closeCurrent":
          this.closeTagsView(path);
          break;
        case "closeLeft":
          this.closeTagsView(path, "left");
          break;
        case "closeRight":
          this.closeTagsView(path, "right");
          break;
        case "closeOther":
          this.closeTagsView(path, "other");
          break;
        case "closeAll":
          this.closeTagsView(path, "all");
          break;
        default:
          break;
      }
    },
    closeTagsView(path, type = "current") {
      if (type === "current") {
        this.closeCurrentTagsView(path);
      } else if (type === "other") {
        this.closeMultipleTagsView(path);
        const targetPath = this.tagsViewList.some((item) => item.path === path) ? path : this.tagsViewList[0]?.path;
        if (targetPath) {
          this.$router.push({ path: targetPath, query: this.$route.query });
        }
      } else if (type === "all") {
        this.closeMultipleTagsView();
        this.$router.push("/");
      } else {
        const currentIndex = this.tagsViewList.findIndex((item) => item.path === path);
        if (currentIndex !== -1) {
          const range = type === "left" ? [0, currentIndex] : [currentIndex + 1, this.tagsViewList.length];
          this.tagsViewList = this.tagsViewList.filter((item, index) => {
            return index < range[0] || index >= range[1] || !item.meta.close;
          });
        }
        const currentTag = this.tagsViewList.find((item) => item.path === path);
        this.$router.push({ path, query: currentTag.query });
      }
      this.addBrowserSetSession(this.tagsViewList);
    },
    // 1、刷新当前 tagsView：
    refreshCurrentTagsView(path) {
      this.$bus.$emit("onTagsViewRefreshRouterView", path);
    },
    // 2、关闭当前 tagsView：当前项 `tags-view` icon 关闭时点击，如果是设置了固定的（isAffix），不可以关闭
    closeCurrentTagsView(path) {
      const index = this.tagsViewList.findIndex((item) => item.path === path);
      if (index !== -1) {
        this.tagsViewList.splice(index, 1);

        const nextTag = this.tagsViewList[index] || this.tagsViewList[this.tagsViewList.length - 1];
        if (nextTag) {
          this.$router.push({ path: nextTag.path, query: nextTag.query });
        }
      }
    },
    // 3、关闭其它/全部 tagsView：如果是设置了固定的（isAffix），不进行关闭
    closeMultipleTagsView(path) {
      this.tagsViewList = this.tagsViewRoutesList.filter((item) => item.path === path || !item.meta.close);
      if (path) {
        const keepAliveNames = this.$store.state.keepAliveNames.keepAliveNames;
        this.tagsViewList.sort((a, b) => keepAliveNames.indexOf(a.path) - keepAliveNames.indexOf(b.path));
      }
    }
  }
};
</script>

<style scoped lang="scss">
.layout-navbars-tagsview {
  flex: 1;
  height: 35px;
  background-color: var(--nari-c-bg);
  border-bottom: 1px solid var(--nari-c-border-base);
  & ::v-deep .is-vertical {
    display: none !important;
  }
  &-ul {
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    align-items: center;
    white-space: nowrap;
    color: var(--nari-c-text-2);
    font-size: 12px;
    &-li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      margin-right: 5px;
      position: relative;
      z-index: 0;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.2, 1, 0.3, 1);
      &-icon {
        border-radius: 100%;
        position: relative;
        height: 14px;
        width: 14px;
        font-size: 12px;
        text-align: center;
        line-height: 14px;
        top: 1px;
      }
      .el-icon-close {
        display: none;
      }
    }
    /* 卡片模式 */
    &.tags-card {
      padding: 0 5px;
      .layout-navbars-tagsview-ul-li {
        height: 28px;
        line-height: 28px;
        border: 1px solid var(--nari-c-border-base);
        border-radius: 2px;
        &::before {
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          background: var(--nari-c-primary);
          z-index: -1;
          opacity: 0;
          transform: scale3d(0.7, 1, 1);
          transition: transform 0.3s, opacity 0.3s;
          transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
        }
        &:hover {
          color: var(--nari-c-white);
          transition: all 0.3s cubic-bezier(0.2, 1, 0.3, 1);
          border-color: transparent;
          &::before {
            opacity: 1;
            transform: translate3d(0, 0, 0);
            border-radius: 2px;
          }
          .el-icon-close {
            display: block;
          }
        }
        &-icon {
          color: var(--nari-c-white);
        }
      }
      .is-active {
        color: var(--nari-c-white);
        transition: all 0.3s cubic-bezier(0.2, 1, 0.3, 1);
        border-color: transparent;
        &::before {
          opacity: 1;
          transform: translate3d(0, 0, 0);
          border-radius: 2px;
        }
        .el-icon-close {
          display: block;
        }
      }
    }
    /* 灵动模式 */
    &.tags-schedule {
      .layout-navbars-tagsview-ul-li {
        height: 34px;
        line-height: 34px;
        &-icon {
          color: var(--nari-c-primary);
        }
        .layout-navbars-tagsview-ul-li-schedule {
          width: 0;
          height: 2px;
          position: absolute;
          left: 0;
          bottom: 0;
          background: var(--nari-c-primary);
          animation: scheduleOutWidth 200ms ease-in;
        }
        &:hover {
          .el-icon-close {
            display: block;
          }
          .layout-navbars-tagsview-ul-li-schedule {
            width: 100%;
            animation: scheduleInWidth 200ms ease-in;
          }
        }
      }
      .is-active {
        color: var(--nari-c-primary);
        box-shadow: 0 0 2px #888;
        .el-icon-close {
          display: block;
        }
        .layout-navbars-tagsview-ul-li-schedule,
        &:hover .layout-navbars-tagsview-ul-li-schedule {
          width: 100%;
          animation: none;
        }
      }
    }
  }
  & ::-webkit-scrollbar {
    display: none !important;
  }
  ::v-deep .el-scrollbar {
    height: 35px;
  }
}
</style>
