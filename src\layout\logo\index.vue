<template>
  <div
    style="height: 60px"
    :class="getThemeConfig.layout === 'defaults' ? 'layout-logo-size layout-logo-size-defaults' : 'layout-logo'"
    v-if="setShowLogo"
    @click="onThemeConfigChange"
  >
    <svg-icon
      style="width: 170px"
      class="layout-logo-medium-img"
      icon-name="logo"
    ></svg-icon>
    <template v-if="getThemeConfig.layout !== 'defaults'">
      <div class="layout-logo-split"></div>
      <span class="layout-logo-title">{{ getThemeConfig.globalTitle }}</span>
    </template>
  </div>
  <div
    style="height: 60px"
    class="layout-logo-size"
    :class="getThemeConfig.layout === 'defaults' ? 'layout-logo-size-defaults' : ''"
    v-else
    @click="onThemeConfigChange"
  >
    <svg-icon
      class="layout-logo-size-img"
      icon-name="logo-small"
    ></svg-icon>
  </div>
</template>

<script>
export default {
  name: "layoutLogo",
  data() {
    return {};
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    // 设置 logo 是否显示
    setShowLogo() {
      const { isCollapse, layout } = this.getThemeConfig;
      if (layout === "horizontal" || layout === "mix") {
        return true;
      }
      return !isCollapse || layout === "vertical";
    }
  },
  methods: {
    // logo 点击实现菜单展开/收起
    onThemeConfigChange() {
      this.$router.push("/");
      // if (this.getThemeConfig.layout === "horizontal") return false;
      // this.getThemeConfig.isCollapse = !this.getThemeConfig.isCollapse;
    }
  }
};
</script>

<style scoped lang="scss">
.layout-logo {
  min-width: 34.3%;
  max-width: 45%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: var(--nari-c-primary);
  cursor: pointer;
  animation: logoAnimation 0.3s ease-in-out;
  &:hover {
    span {
      opacity: 0.9;
    }
  }
  &-medium-img {
    height: 90%;
    color: var(--nari-c-white);
  }
  &-split {
    width: 0;
    height: 25px;
    border: 1px solid var(--nari-c-white);
    margin-left: 20px;
  }
  &-title {
    display: inline-block;
    vertical-align: top;
    font-size: 20px;
    padding-left: 10px;
    color: var(--nari-c-white);
    &::before {
      content: "";
      height: 10px;
      width: 0px;
      margin-right: 10px;
    }
  }
  &-size {
    width: 100%;
    height: 100%;
    display: flex;
    color: var(--nari-c-white);
    animation: logoAnimation 0.3s ease-in-out;
    cursor: pointer;
    &-defaults {
      justify-content: center;
      align-items: center;
    }
    &-img {
      width: 60%;
      height: 90%;
      animation: logoAnimation 0.3s ease-in-out;
    }
  }
}
</style>
