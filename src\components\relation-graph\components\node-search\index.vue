<template>
  <div
    class="box"
    :class="theme"
  >
    <el-row class="content-row-auto">
      <el-col
        :span="24"
        class="col-value"
      >
        <el-select
          size="mini"
          v-model="searchMode"
          style="width: 99%"
        >
          <el-option
            v-for="item in searchModes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <template v-if="searchMode === 'node'">
      <el-row class="content-row-auto">
        <el-col
          :span="6"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">节点名称</span>
        </el-col>
        <el-col
          :span="18"
          class="col-value"
        >
          <el-input
            size="mini"
            v-model="keyword"
            placeholder="请输入关键字"
            style="width: 99%"
          ></el-input>
        </el-col>
      </el-row>
    </template>
    <template v-if="searchMode === 'relationship'">
      <el-row class="content-row-auto">
        <el-col
          :span="6"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">关系名称</span>
        </el-col>
        <el-col
          :span="18"
          class="col-value"
        >
          <el-input
            size="mini"
            v-model="keyword"
            placeholder="请输入关键字"
            style="width: 99%"
          ></el-input>
        </el-col>
      </el-row>
    </template>
    <template v-if="searchMode === 'paths'">
      <el-row class="content-row-auto">
        <el-col
          :span="7"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">首节点名称</span>
        </el-col>
        <el-col
          :span="17"
          class="col-value"
        >
          <el-input
            size="mini"
            v-model="nodeName1"
            placeholder="请输入关键字"
            style="width: 99%"
          ></el-input>
        </el-col>
      </el-row>
      <el-row class="content-row-auto">
        <el-col
          :span="7"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">末节点名称</span>
        </el-col>
        <el-col
          :span="17"
          class="col-value"
        >
          <el-input
            size="mini"
            v-model="nodeName2"
            placeholder="请输入关键字"
            style="width: 99%"
          ></el-input>
        </el-col>
      </el-row>
    </template>
    <template v-if="searchMode === '站内设备图谱' || searchMode === '厂站关联图谱'">
      <el-row class="content-row-auto">
        <el-col
          :span="6"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">厂站</span>
        </el-col>
        <el-col
          :span="18"
          class="col-value"
        >
          <el-input
            size="mini"
            v-model="facId"
            placeholder="请输入厂站名称"
            style="width: 99%"
          ></el-input>
        </el-col>
      </el-row>
    </template>
    <template v-if="searchMode === '设备间隔图谱'">
      <el-row class="content-row-auto">
        <el-col
          :span="6"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">设备</span>
        </el-col>
        <el-col
          :span="18"
          class="col-value"
        >
          <el-input
            size="mini"
            v-model="equId"
            placeholder="请输入设备名称"
            style="width: 99%"
          ></el-input>
        </el-col>
      </el-row>
    </template>
    <template v-if="searchMode === '两侧厂站连接图谱'">
      <el-row class="content-row-auto">
        <el-col
          :span="6"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">首厂站</span>
        </el-col>
        <el-col
          :span="18"
          class="col-value"
        >
          <el-input
            size="mini"
            v-model="facIdA"
            placeholder="请输入厂站名称"
            style="width: 99%"
          ></el-input>
        </el-col>
      </el-row>
      <el-row class="content-row-auto">
        <el-col
          :span="6"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">末厂站</span>
        </el-col>
        <el-col
          :span="18"
          class="col-value"
        >
          <el-input
            size="mini"
            v-model="facIdB"
            placeholder="请输入厂站名称"
            style="width: 99%"
          ></el-input>
        </el-col>
      </el-row>
    </template>
    <el-row class="content-row-auto">
      <el-col
        :span="24"
        class="col-value"
      >
        <el-button
          type="primary"
          size="mini"
          style="width: 99%"
          :loading="searchLoading"
          @click="search"
          >搜索</el-button
        >
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { GraphicApi } from "@/api/business/api";
export default {
  name: "SearchNodeInfoBox",
  props: {
    searchNodeInfo: {
      type: Function,
      default() {}
    },
    theme: { type: String, default: "light" }
  },
  watch: {
    theme: {
      handler(val) {
        if (this.$route.query.query === "true") this.searchByBusiness();
      }
    }
  },
  data() {
    return {
      searchMode: "node",
      searchModes: [
        {
          label: "节点查询",
          value: "node"
        },
        {
          label: "关系查询",
          value: "relationship"
        },
        {
          label: "路径查询",
          value: "paths"
        },
        {
          label: "站图谱",
          value: "站图谱"
        },
        {
          label: "站内设备图谱",
          value: "站内设备图谱"
        },
        {
          label: "设备间隔图谱",
          value: "设备间隔图谱"
        },
        {
          label: "厂站关联图谱",
          value: "厂站关联图谱"
        },
        {
          label: "两侧厂站连接图谱",
          value: "两侧厂站连接图谱"
        }
      ],
      keyword: "",
      nodeName1: "",
      nodeName2: "",
      facId: "",
      equId: "",
      facIdA: "",
      facIdB: "",
      searchLoading: false
    };
  },
  mounted() {
    if (this.$route.query.query === "true") this.searchByBusiness();
  },
  methods: {
    search() {
      this.searchLoading = true;
      if (["node", "relationship", "paths"].includes(this.searchMode)) {
        this.searchNodeInfo(this.searchMode, this.keyword, this.nodeName1, this.nodeName2).then(() => {
          this.searchLoading = false;
        });
      } else {
        this.searchByBusinessForClick();
      }
    },
    searchByBusiness() {
      console.log(this.$route.query);
      if (this.$route.query.facId) {
        this.facId = this.$route.query.facId;
        this.getEquipmentNeo4j();
      } else if (this.$route.query.equId) {
        this.equId = this.$route.query.equId;
        this.getEquipmentNeo4jBay();
      } else if (this.$route.query.facIdA && this.$route.query.facIdB) {
        this.facIdA = this.$route.query.facIdA;
        this.facIdB = this.$route.query.facIdB;
        this.getStationNeo4jConnected();
      } else {
        this.getStationNeo4j();
      }
    },
    searchByBusinessForClick() {
      switch (this.searchMode) {
        case "站图谱":
          this.getStationNeo4j();
          break;
        case "站内设备图谱":
          this.getEquipmentNeo4j();
          break;
        case "设备间隔图谱":
          this.getEquipmentNeo4jBay();
          break;
        case "厂站关联图谱":
          this.getEquipmentNeo4j();
          break;
        case "两侧厂站连接图谱":
          this.getStationNeo4jConnected();
          break;
        default:
          break;
      }
    },
    getStationNeo4j() {
      GraphicApi.getStationNeo4j()
        .then((res) => {
          if (res.code === 200) {
            this.$emit("processGraphicData", res.data);
            this.searchLoading = false;
          }
        })
        .catch((error) => {
          this.searchLoading = false;
          this.$message.error("获取失败，请稍后再试");
        });
    },
    getEquipmentNeo4j() {
      const params = {
        facId: this.facId
      };
      if (!this.facId) {
        this.searchLoading = false;
        return this.$message.warning("请提供查询参数");
      }
      GraphicApi.getEquipmentNeo4j(params)
        .then((res) => {
          if (res.code === 200) {
            this.$emit("processGraphicData", res.data);
            this.searchLoading = false;
          } else {
            this.searchLoading = false;
            this.$message.error(res.message);
          }
        })
        .catch((error) => {
          this.searchLoading = false;
          this.$message.error("获取失败，请稍后再试");
        });
    },
    getEquipmentNeo4jBay() {
      const params = {
        equId: this.equId
      };
      if (!this.equId) {
        this.searchLoading = false;
        return this.$message.warning("请提供查询参数");
      }
      GraphicApi.getEquipmentNeo4jBay(params)
        .then((res) => {
          if (res.code === 200) {
            this.$emit("processGraphicData", res.data);
            this.searchLoading = false;
          } else {
            this.searchLoading = false;
            this.$message.error(res.message);
          }
        })
        .catch((error) => {
          this.searchLoading = false;
          this.$message.error("获取失败，请稍后再试");
        });
    },
    getStationNeo4jByFacId() {
      const params = {
        facId: this.facId
      };
      if (!this.facId) {
        this.searchLoading = false;
        return this.$message.warning("请提供查询参数");
      }
      GraphicApi.getStationNeo4jByFacId(params)
        .then((res) => {
          if (res.code === 200) {
            this.$emit("processGraphicData", res.data);
            this.searchLoading = false;
          } else {
            this.searchLoading = false;
            this.$message.error(res.message);
          }
        })
        .catch((error) => {
          this.searchLoading = false;
          this.$message.error("获取失败，请稍后再试");
        });
    },
    getStationNeo4jConnected() {
      const params = {
        facIdA: this.facIdA,
        facIdB: this.facIdB
      };
      if (!this.facIdA && !this.facIdB) {
        this.searchLoading = false;
        return this.$message.warning("请提供查询参数");
      }
      GraphicApi.getStationNeo4jConnected(params)
        .then((res) => {
          if (res.code === 200) {
            this.$emit("processGraphicData", res.data);
            this.searchLoading = false;
          } else {
            this.searchLoading = false;
            this.$message.error(res.message);
          }
        })
        .catch((error) => {
          this.searchLoading = false;
          this.$message.error("获取失败，请稍后再试");
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.box {
  width: 100%;
  padding: 5px;
  box-sizing: border-box;

  .content-row-auto {
    min-height: 25px;
    margin-top: 5px;
    display: flex;
    align-items: stretch;
  }

  .col-label {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: end;
    padding-right: 10px;
    font-size: 12px;
    font-weight: bold;
    color: #7aaad5;
    background-color: transparent;
  }

  .text-label {
    font-size: 13px;
    font-weight: 600;
  }

  .col-value {
    height: auto;
    padding: 5px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-size: 14px;
    font-family: "DM Sans";
    color: #fff;
    border: none;
    box-sizing: border-box;
  }
}

.dark {
  &.box {
    .col-label {
      color: #7aaad5;
      background-color: transparent;
    }

    .col-value {
      color: #fff;
    }
  }
}

.light,
.transparent {
  &.box {
    .col-label {
      color: #7aaad5;
      background-color: transparent;
    }

    .col-value {
      color: #222222;
    }
  }
}
</style>
