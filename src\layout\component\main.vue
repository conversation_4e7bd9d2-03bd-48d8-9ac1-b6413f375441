<template>
  <div
    class="layout-scrollbar"
    :style="layoutHeight"
  >
    <template v-if="!currentRouteMeta.isLink && !currentRouteMeta.isIframe">
      <LayoutParentView />
      <Footers v-if="getThemeConfig.isFooter" />
    </template>
    <Links
      :meta="currentRouteMeta"
      v-if="currentRouteMeta.isLink && !currentRouteMeta.isIframe"
    />
    <Iframes
      v-if="currentRouteMeta.isLink && currentRouteMeta.isIframe && isShowLink"
      :meta="currentRouteMeta"
      @getCurrentRouteMeta="onGetCurrentRouteMeta"
    />
  </div>
</template>

<script>
import LayoutParentView from "@/layout/routerView/parent.vue";
import Footers from "@/layout/footer/index.vue";
import Links from "@/layout/routerView/link.vue";
import Iframes from "@/layout/routerView/iframes.vue";
export default {
  name: "layoutMain",
  components: { LayoutParentView, Foot<PERSON>, <PERSON>s, Iframes },
  data() {
    return {
      headerHeight: "",
      currentRouteMeta: {},
      isShowLink: false
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    layoutHeight() {
      if (this.isSideBar) {
        return {
          height: `calc(100vh - ${this.getThemeConfig.layoutHeaderHeight}`,
          overflow: "auto"
        };
      } else {
        return {
          height: `100vh`,
          overflow: "hidden"
        };
      }
    },
    isSideBar() {
      return this.$store.state.themeConfig.themeConfig.isSideBar;
    }
  },
  mounted() {
    this.initCurrentRouteMeta(this.$route.meta);
  },
  methods: {
    // 初始化当前路由 meta 信息
    initCurrentRouteMeta(meta) {
      this.isShowLink = false;
      this.currentRouteMeta = meta;
      setTimeout(() => {
        this.isShowLink = true;
      }, 100);
    },
    // 子组件触发更新
    onGetCurrentRouteMeta() {
      this.initCurrentRouteMeta(this.$route.meta);
    }
  },
  watch: {
    // 监听 vuex 数据变化
    "$store.state.themeConfig.themeConfig": {
      handler(val) {
        this.headerHeight = val.isTagsview ? "95px" : "60px";
      },
      deep: true
    },
    // 监听路由的变化
    $route: {
      handler(to) {
        this.initCurrentRouteMeta(to.meta);
      },
      deep: true
    }
  }
};
</script>
