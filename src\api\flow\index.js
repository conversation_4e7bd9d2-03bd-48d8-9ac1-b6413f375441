import services from "@/services";

const { http } = services;

const prefix = "";

export const FlowApi = {
  fetchFlowGraph: () => http.get(prefix + `/fetchFlowGraph`),
  fetchWorkFlowId: ({ difyWorkFlowName }) => http.get(prefix + `/dify/getWorkFlowId/${difyWorkFlowName}`),
  fetchFactoryAreaFlow: (params) => http.post(prefix + `/svg/getFactoryAreaFlow`, params),
  fetchSuggestSvg: (params) => http.post(prefix + `/svg/getSuggestSvg`, params)
};
