module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: ["eslint:recommended", "plugin:vue/essential", "plugin:prettier/recommended"],
  overrides: [
    {
      env: {
        node: true
      },
      files: [".eslintrc.{js,cjs}"],
      parserOptions: {
        sourceType: "script"
      }
    }
  ],
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
    ecmaFeatures: {
      jsx: true
    }
  },
  plugins: ["vue", "prettier"],
  rules: {
    "prettier/prettier": [
      "error",
      {
        printWidth: 200,
        trailingComma: "none",
        quoteProps: "preserve",
        endOfLine: "crlf",
        singleAttributePerLine: true
      }
    ],
    "arrow-spacing": "error", // 要求箭头函数的箭头部分前后的空格
    "block-spacing": ["error", "always"], // 代码块空格{return} => { return }
    "brace-style": "error", // else 与它的大括号同行
    "comma-dangle": ["error", "never"], // 无多余逗号
    "comma-spacing": ["error", { before: false, after: true }], // 参数间逗号后的空格
    "default-case": "error", // switch语句最后必须有default
    eqeqeq: ["error", "always"], // 要求使用 === 或者 !==
    indent: ["error", 2, { SwitchCase: 1 }],
    "keyword-spacing": ["error"],
    "no-alert": "error", // 禁用alert
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-constant-condition": "error",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-dupe-args": "error",
    "no-dupe-keys": "error",
    "no-empty": "error",
    "no-extra-semi": "error", // 禁止不必要的分号
    "no-mixed-spaces-and-tabs": ["error"], // 禁止空格和 tab 的混合缩进
    "no-multi-spaces": "error", // 禁止使用多余的空格
    "no-trailing-spaces": "error",
    "no-unused-vars": ["error", { args: "none" }], // 禁止出现未使用的变量
    "no-var": "error", // 禁用var，用let和const代替
    "no-whitespace-before-property": "error", // 禁止点调用有空格 a. b => a.b
    "object-curly-spacing": ["error", "always"], // 解构赋值大括号间空格
    quotes: ["error", "double", { avoidEscape: true, allowTemplateLiterals: true }], // 引号类型, 不会禁止使用所有模板字面
    semi: ["error", "always"], // 去掉结尾的分号
    "semi-spacing": "error",
    "spaced-comment": ["error", "always"],
    "space-in-parens": ["error", "never"],
    "space-infix-ops": "error", // 中缀运算符空格
    "space-before-function-paren": ["off", "always"], // 函数定义时括号前面要不要有空格
    "space-before-blocks": ["error"],
    "vue/multi-word-component-names": "off",
    "no-undef": "off"
  }
};
