import { HOME_URL } from "@/config";
/**
 * staticRouter (静态路由)
 */
export const staticRouter = [
  {
    path: "/",
    redirect: HOME_URL
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login"),
    meta: { title: "登录", rank: 101 }
  },
  {
    path: "/layout",
    name: "layout",
    component: () => import("@/layout"),
    redirect: HOME_URL,
    children: []
  }
];

/**
 * errorRouter (错误页面路由)
 */
export const errorRouter = [
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/error/404"),
    meta: {
      title: "找不到此页面",
      rank: 102
    }
  },
  {
    path: "*",
    component: () => import("@/views/error/404")
  }
];
