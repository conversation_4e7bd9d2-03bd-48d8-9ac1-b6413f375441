{"name": "smart-generic-plan-web", "description": "在线预案及事故辅助决策", "author": "", "private": true, "version": "1.1.0", "type": "module", "scripts": {"dev": "vite", "build:local": "vite build --mode localhost", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "build": "pnpm run build:local && pnpm run build:test && pnpm run build:prod", "deploy": "node deploy.cjs", "plop": "plop --plopfile ./plop/plopfile.js", "preview": "vite preview", "lint:eslint": "eslint --ext .jsx,.js,.vue src/", "lint:fix": "eslint --ext .jsx,.js,.vue src/ --fix", "lint:prettier": "prettier --write .", "lint": "pnpm lint:fix && pnpm lint:prettier", "prepare": "husky install"}, "dependencies": {"@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "axios": "1.4.0", "d3": "^7.9.0", "echarts": "5.4.2", "element-ui": "2.15.13", "highlight.js": "11.10.0", "immer": "9.0.21", "js-audio-recorder": "1.0.7", "lamejs": "1.2.1", "leaflet": "^1.9.4", "lodash-es": "4.17.21", "marked": "4.3.0", "moment": "2.29.4", "nari-ui": "^1.1.14", "nprogress": "0.2.0", "relation-graph": "^2.2.7", "vue": "2.6.14", "vue-markdown": "^2.2.4", "vue-router": "3.6.5", "vuex": "3.6.2"}, "devDependencies": {"@vitejs/plugin-legacy": "4.1.1", "autoprefixer": "7.1.2", "chalk": "5.3.0", "eslint": "8.22.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-vue": "7.20.0", "fast-glob": "3.2.12", "husky": "8.0.3", "lint-staged": "12.5.0", "node-ssh": "13.1.0", "plop": "3.1.2", "prettier": "2.7.1", "prompts": "2.4.2", "sass": "1.49.9", "scp2": "0.5.0", "shelljs": "0.8.5", "simple-git": "^3.27.0", "tailwindcss": "3.3.3", "vite": "4.3.9", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue2": "2.0.3", "vue-template-compiler": "2.6.14"}, "engines": {"node": ">=16.0.0"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["webpack"]}}, "lint-staged": {"src/**/*.{js,jsx,vue}": ["prettier --write", "eslint --fix"]}}