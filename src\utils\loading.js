import Vue from "vue";

function generateLoadingHTML(type) {
  let loadingHTML = `<div class="square">${Array(9).fill('<div class="square-item"></div>').join("")}</div>`;
  if (type === "dot") {
    loadingHTML = `<div class="dot">${Array(5).fill('<div class="dot-item"></div>').join("")}</div>`;
  }
  if (type === "circle") {
    loadingHTML = `<div class="circle">${Array(5).fill('<div class="circle-item"></div>').join("")}</div>`;
  }
  return `<div class="loading-box">${loadingHTML}</div>`;
}

function setSize(type, size) {
  const defaultSize = {
    dot: 20,
    circle: 48,
    square: 80
  };
  if (!size) return `${defaultSize[type]}px`;
  return typeof size === "number" ? `${size}px` : size;
}

// 定义方法
export const PrevLoading = {
  // 创建 loading
  start: (options = {}) => {
    const { type = "circle", background, size, color } = options;
    const bodys = document.body;
    const div = document.createElement("div");
    div.setAttribute("class", "loading");

    // 定义 CSS 变量对象
    const variables = {
      "--loader-bg": background || "#fff",
      "--loader-size": setSize(type, size),
      "--loader-color": color || "#409eff"
    };

    // 使用 Object.assign 应用 CSS 变量
    Object.keys(variables).forEach((key) => {
      div.style.setProperty(key, variables[key]);
    });
    const htmls = generateLoadingHTML(type);
    div.innerHTML = htmls;
    bodys.insertBefore(div, bodys.childNodes[0]);
  },
  // 移除 loading
  done: () => {
    Vue.nextTick(() => {
      setTimeout(() => {
        const el = document.querySelector(".loading");
        if (el && el.parentNode) {
          el.parentNode.removeChild(el);
        }
      }, 1000);
    });
  }
};
