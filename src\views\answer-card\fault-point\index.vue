<template>
  <div class="fault-point-container">
    <info-card
      v-for="(card, index) in cardList"
      :key="`${card.key}-${index}`"
      :card-text="card.text"
      :value="card.value"
      :card-key="card.key"
      @input="handleInput"
    />
  </div>
</template>

<script>
import InfoCard from "@/components/info-card";
export default {
  components: {
    InfoCard
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      cardList: [
        { key: "qdlxl", text: "全电缆线路", value: null },
        { key: "sxdl", text: "三相短路", value: null },
        { key: "zngl", text: "站内故障", value: null },
        { key: "clybyq", text: "串联有变压器", value: null },
        { key: "ddzy", text: "带电作业", value: null },
        // { key: "jckytt", text: "交叉跨越同跳", value: null },
        { key: "dccd", text: "单侧充电", value: null },
        // { key: "hxx", text: "混合线", value: null },
        { key: "kgjd", text: "开关拒动", value: null },
        { key: "xtbyzd", text: "系统伴有震荡", value: null },
        { key: "gkbhdz", text: "高亢保护动作", value: null },
        { key: "xlbjbyxtj", text: "线路不具备运行条件", value: null },
        { key: "xlwzbh", text: "线路无主保护", value: null },
        { key: "jxjsfdshsyxxl", text: "检修结束复电时或试运行线路", value: null }
      ]
    };
  },
  methods: {
    handleInput(newValue, info) {
      // 找到相应的卡片并更新值
      const card = this.cardList.find((c) => c.key === info.key && c.text === info.text);
      if (card) {
        card.value = newValue;
      }

      console.log(`组 "${info.key}" 的卡片 "${info.text}" 已更改为: ${newValue}`);

      // 您也可以根据key分组处理
      const sameGroupCards = this.cardList.filter((c) => c.key === info.key);
      console.log(
        `同组卡片状态:`,
        sameGroupCards.map((c) => ({ text: c.text, value: c.value }))
      );
      const isAllSelected = this.cardList.every((c) => c.value === true);
      if (isAllSelected) {
        console.log("所有卡片都已选中");
      }
    }
  },
  created() {
    // 初始化卡片值，如果data中有对应key的值则更新
    this.cardList.forEach((card) => {
      let faultPoints = this.data?.pointsMap[0];
      if (faultPoints[card.key]) {
        card.value = faultPoints[card.key];
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.fault-point-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

::v-deep .el-radio-button--mini .el-radio-button__inner {
  padding: 7px 11px;
}
</style>
