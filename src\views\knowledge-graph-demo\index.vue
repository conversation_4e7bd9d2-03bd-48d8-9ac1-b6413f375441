<template>
  <div class="knowledge-graph-demo">
    <div class="demo-container">
      <!-- Knowledge Graph Map Component -->
      <knowledge-graph-map
        ref="knowledgeGraphMap"
        :graph-data="graphData"
        :map-options="mapOptions"
        class="map-container"
      />

      <!-- Control Panel -->
      <div class="control-panel">
        <h2>Guangdong Knowledge Graph Demo</h2>
        <p class="desc">Based on ECharts Map + D3.js Knowledge Graph Visualization</p>

        <div class="section">
          <h3>Data Control</h3>
          <el-button
            type="primary"
            size="small"
            @click="loadSampleData"
            icon="el-icon-download"
          >
            Load Sample Data
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="clearData"
            icon="el-icon-delete"
          >
            Clear Data
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="addCloseNodes"
            icon="el-icon-plus"
          >
            Add Close Nodes
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="refreshGraph"
            icon="el-icon-refresh"
          >
            Refresh Graph
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="debugGraph"
            icon="el-icon-info"
          >
            Debug Info
          </el-button>
        </div>

        <div class="section">
          <h3>Dynamic Add</h3>
          <el-button
            size="small"
            @click="addRandomNode"
            icon="el-icon-plus"
          >
            Add Random Node
          </el-button>
          <el-button
            size="small"
            @click="addRandomLink"
            icon="el-icon-connection"
          >
            Add Random Link
          </el-button>
        </div>

        <div class="section">
          <h3>Statistics</h3>
          <div class="stats">
            <div class="stat-item">
              <span class="label">Nodes:</span>
              <span class="value">{{ graphData.nodes.length }}</span>
            </div>
            <div class="stat-item">
              <span class="label">Links:</span>
              <span class="value">{{ graphData.links.length }}</span>
            </div>
          </div>
        </div>

        <div class="section">
          <h3>Legend</h3>
          <div class="legend">
            <div class="legend-item">
              <span class="dot city"></span>
              <span>City Node</span>
            </div>
            <div class="legend-item">
              <span class="dot industry"></span>
              <span>Industry Node</span>
            </div>
            <div class="legend-item">
              <span class="line economic"></span>
              <span>Economic Link</span>
            </div>
            <div class="legend-item">
              <span class="line cultural"></span>
              <span>Cultural Link</span>
            </div>
            <div class="legend-item">
              <span class="line industry-link"></span>
              <span>Industry Link</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import KnowledgeGraphMap from "@/components/knowledge-graph-map/index.vue";
import { mockGraphData, mapOptions } from "@/components/knowledge-graph-map/mockData.js";

export default {
  name: "KnowledgeGraphDemo",
  components: {
    KnowledgeGraphMap
  },
  data() {
    return {
      graphData: {
        nodes: [],
        links: []
      },
      mapOptions: mapOptions
    };
  },
  mounted() {
    // Auto load sample data
    setTimeout(() => {
      this.loadSampleData();
    }, 500);
  },
  methods: {
    // Load sample data
    loadSampleData() {
      // Deep copy to avoid modifying original data
      this.graphData = {
        nodes: JSON.parse(JSON.stringify(mockGraphData.nodes)),
        links: JSON.parse(JSON.stringify(mockGraphData.links))
      };
      this.$message.success("Sample data loaded successfully");
    },

    // Clear data
    clearData() {
      this.graphData = {
        nodes: [],
        links: []
      };
      this.$message.info("Data cleared");
    },

    // Add random node
    addRandomNode() {
      const cities = [
        { name: "Heyuan", lat: 23.746, lng: 114.697 },
        { name: "Shanwei", lat: 22.774, lng: 115.364 },
        { name: "Chaozhou", lat: 23.661, lng: 116.632 },
        { name: "Jieyang", lat: 23.543, lng: 116.355 },
        { name: "Yunfu", lat: 22.929, lng: 112.044 },
        { name: "Zhaoqing", lat: 23.051, lng: 112.472 },
        { name: "Meizhou", lat: 24.299, lng: 116.117 }
      ];

      const colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3"];
      const randomCity = cities[Math.floor(Math.random() * cities.length)];
      const randomColor = colors[Math.floor(Math.random() * colors.length)];

      // Check if node already exists
      const exists = this.graphData.nodes.some((node) => node.name === randomCity.name);
      if (exists) {
        this.$message.warning(`${randomCity.name} node already exists`);
        return;
      }

      const newNode = {
        id: `node_${Date.now()}`,
        name: randomCity.name,
        type: "city",
        geo: [randomCity.lng, randomCity.lat],
        color: randomColor,
        size: 10 + Math.random() * 10,
        info: `${randomCity.name} - Dynamically added node`
      };

      this.graphData.nodes.push(newNode);
      this.$message.success(`Successfully added node: ${randomCity.name}`);
    },

    // Add random link
    addRandomLink() {
      if (this.graphData.nodes.length < 2) {
        this.$message.warning("At least 2 nodes are required to add a link");
        return;
      }

      const sourceNode = this.graphData.nodes[Math.floor(Math.random() * this.graphData.nodes.length)];
      let targetNode = this.graphData.nodes[Math.floor(Math.random() * this.graphData.nodes.length)];

      // Ensure not the same node
      while (targetNode.id === sourceNode.id) {
        targetNode = this.graphData.nodes[Math.floor(Math.random() * this.graphData.nodes.length)];
      }

      // Check if link already exists
      const linkExists = this.graphData.links.some((link) => (link.source === sourceNode.id && link.target === targetNode.id) || (link.source === targetNode.id && link.target === sourceNode.id));

      if (linkExists) {
        this.$message.warning("This link already exists");
        return;
      }

      const linkTypes = ["economic", "cultural", "industry"];
      const newLink = {
        source: sourceNode.id,
        target: targetNode.id,
        type: linkTypes[Math.floor(Math.random() * linkTypes.length)],
        value: Math.floor(Math.random() * 5) + 1
      };

      this.graphData.links.push(newLink);
      this.$message.success(`Successfully added link: ${sourceNode.name} - ${targetNode.name}`);
    },

    // 添加接近的测试节点
    addCloseNodes() {
      const closeNodes = [
        {
          id: "test-close-1",
          name: "测试节点1",
          geo: [113.264385, 23.129112], // 与广州相同坐标
          size: 12,
          color: "#ff0000",
          type: "test",
          info: "测试重叠避免功能"
        },
        {
          id: "test-close-2",
          name: "测试节点2",
          geo: [113.2644, 23.12912], // 与广州非常接近
          size: 12,
          color: "#00ff00",
          type: "test",
          info: "测试重叠避免功能"
        },
        {
          id: "test-close-3",
          name: "测试节点3",
          geo: [113.26439, 23.1291], // 与广州非常接近
          size: 12,
          color: "#0000ff",
          type: "test",
          info: "测试重叠避免功能"
        }
      ];

      // 添加测试连接
      const closeLinks = [
        { source: "guangzhou", target: "test-close-1", value: 5, type: "test" },
        { source: "test-close-1", target: "test-close-2", value: 3, type: "test" },
        { source: "test-close-2", target: "test-close-3", value: 3, type: "test" }
      ];

      // 检查是否已存在测试节点
      const existingTestNodes = this.graphData.nodes.filter((node) => node.type === "test");
      if (existingTestNodes.length > 0) {
        this.$message.warning("测试节点已存在，请先清除数据");
        return;
      }

      this.graphData.nodes.push(...closeNodes);
      this.graphData.links.push(...closeLinks);

      this.$message.success("已添加接近节点，观察重叠避免效果");
    },

    // 刷新图谱
    refreshGraph() {
      if (this.$refs.knowledgeGraphMap) {
        this.$refs.knowledgeGraphMap.refresh();
        this.$message.info("图谱已刷新");
      }
    },

    // 调试图谱
    debugGraph() {
      if (this.$refs.knowledgeGraphMap) {
        this.$refs.knowledgeGraphMap.debug();
        this.$refs.knowledgeGraphMap.testCoordinates();
        this.$message.info("调试信息已输出到控制台");
      }
    }
  }
};
</script>

<style scoped lang="scss">
.knowledge-graph-demo {
  width: 100%;
  height: 100vh;
  background: #0f0f0f;
  overflow: hidden;
}

.demo-container {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.map-container {
  flex: 1;
  width: 100%;
  height: 100%;
}

.control-panel {
  width: 320px;
  height: 100%;
  background: rgba(20, 20, 30, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(50, 130, 184, 0.3);
  padding: 20px;
  overflow-y: auto;
  color: #fff;

  h2 {
    margin: 0 0 10px 0;
    font-size: 20px;
    color: #3282b8;
    text-align: center;
  }

  .desc {
    text-align: center;
    color: #888;
    font-size: 12px;
    margin-bottom: 20px;
  }

  .section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    &:last-child {
      border-bottom: none;
    }

    h3 {
      margin: 0 0 15px 0;
      font-size: 14px;
      color: #4ecdc4;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .el-button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  .stats {
    .stat-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      .label {
        color: #888;
        font-size: 14px;
      }

      .value {
        color: #3282b8;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }

  .legend {
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 13px;

      .dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 10px;

        &.city {
          background: #ff6b6b;
        }

        &.industry {
          background: #e74c3c;
        }
      }

      .line {
        width: 20px;
        height: 2px;
        margin-right: 10px;

        &.economic {
          background: #3282b8;
        }

        &.cultural {
          background: #ff6b6b;
        }

        &.industry-link {
          background: #4ecdc4;
        }
      }
    }
  }
}

// Custom scrollbar style
.control-panel::-webkit-scrollbar {
  width: 6px;
}

.control-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.control-panel::-webkit-scrollbar-thumb {
  background: rgba(50, 130, 184, 0.5);
  border-radius: 3px;

  &:hover {
    background: rgba(50, 130, 184, 0.8);
  }
}
</style>
