# 在线预案及事故辅助决策系统

## 介绍

基于 `Vue2.x、Vite4、Vuex、element-ui` 后台管理框架，可配置的主题以及暗黑主题适配，并且提供左侧、顶部、横向、综合这四种菜单布局模式。项目提供强大的 [NariUI](https://10.100.1.19:8443/nari-ui/components/table.html) 组件，在一定程度上节省您的开发效率。另外本项目还封装了一些常用工具函数、动态路由等功能。

## 环境准备

本地环境需要安装 Node.js 16.x+、npm 8.x+

### 安装 Node.js

`node` 版本应不小于 `16` ，推荐安装 `v16.15.0` 及以上版本，npm 为 `v8.5.5`, 推荐安装 Node 版本管理工具 `nvm`，如果本地已经安装了 `Node.js`，可以通过以下命令检查版本是否正确：

```
nvm ls 查看本地所有 Node.js 版本
nvm use v16.15.0 使用指定版本

node -v
npm -v
```

### 使用内网 npm 镜像

```sh
npm config set registry=http://***********:18081/repository/npm-hosted/
```

推荐安装 npm 镜像管理工具 `nrm`，主要用于在 `npm` 源之间快速切换

```sh
# npm 命令安装 nrm

npm install -g nrm

# 所有可用的npm镜像源
nrm ls

# 切换到指定源
nrm use <source-name>

# 添加新的源
nrm add <source-name> <url>

# 删除某个源
nrm del <source-name>

```

## 从现有仓库到新空仓库的流程

# 1. 克隆原始仓库(当前步骤为脚手架自带,无需操作)

```bash
git clone https://github.com/ORIGINAL_OWNER/REPOSITORY_NAME.git
cd REPOSITORY_NAME
```

# 2.添加原始仓库为 upstream(当前步骤为脚手架自带,无需操作)

```bash
git remote add upstream https://github.com/ORIGINAL_OWNER/REPOSITORY_NAME.git
```

# 3、增加权限限制,只保留读取权限(当前步骤为脚手架自带,无需操作)

```bash
git remote set-url --push upstream NO_PUSH
```

# 4. 创建新空仓库（需手动创建新仓库）

```bash
在***********的Gitlab上创建一个空仓库（不初始化README）
```

# 5. 设置 origin 为新仓库（需手动）注意:指令不要错:不是 git remote add origin,而是 git remote set-url origin

```bash
git remote set-url origin https://github.com/YOUR_USERNAME/YOUR_NEW_REPO.git
```

# 6.验证仓库设置（可选，需手动）

```bash
git remote -v
```

````

# 7. 推送到新仓库，推送所有内容（需手动）
```bash
git push -u origin --all
git push -u origin --tags
````

# 注意：从上游更新（需要时）！！！！！！

```bash
git fetch upstream
git merge upstream/main
git push origin main
```

## 用法

```sh

# 安装依赖
npm install

# 运行项目
npm run dev

# 打包
npm run build

# 配置打包配置
{

	"defaultNginxConfPath":"/etc/nginx/web-nginx.conf", // 服务器前端nginx配置路径,不存在则需要自行创建配置文件
	"defaultWebResourcesPath":"/home/<USER>",    // 服务器前端资源存放路径
	"defaultBackUpPath":"/home/<USER>/backup",   // 服务器前端备份文件夹
	"nginxProxyPort": "8443",                           // ng代理统一端口
	"defaultEntry": "index.html",                       // 默认入口html文件
	"maxBackups": 5                                     // 最大备份数量
}

# 一键打包并发布到服务器
npm run deploy

# 新增模块文件目录及添加到路由
npm run plop

? 页面名称: test
? 新页面添加组件吗? Yes
? 组件名称: detail
? 新页面添加到路由吗? Yes
? 菜单名称: 测试
✔  ++ \src\views\test\index.vue
✔  ++ \src\views\test\components\detail\index.vue
✔  _+ \src\router\menu.json

# eslint 检测代码
npm run lint:eslint

# eslint 修复代码
npm run lint:fix

# prettier 格式化代码
npm run lint:prettier

# eslint 修复代码 及 prettier 格式化代码
npm run lint

```

## 环境变量

一共 `三` 个配置文件，都在平台根目录下，具体如下

```
├── .env                  # 基础环境变量配置文件（优先级最低）
├── .env.development      # 开发环境变量配置文件
├── .env.production       # 生产环境变量配置文件
```

| 变量名称                | 说明                                                                                    |
| ----------------------- | --------------------------------------------------------------------------------------- |
| VITE_PORT               | 本地运行端口号，默认为 9000                                                             |
| VITE_OPEN               | 启动时自动打开浏览器, 默认为 true                                                       |
| VITE_APP_BASE_URL       | 读取配置文件路径，默认 ./                                                               |
| VITE_PUBLIC_PATH        | 读取公共资源路径，默认 /                                                                |
| VITE_IS_LOGIN           | 是否使用登录页面，默认 true                                                             |
| VITE_LOCAL_IMPORT_ROUTE | 加载路由方式 true:本地加载, false:远程加载，默认 true                                   |
| VITE_API_URL            | 接口地址                                                                                |
| VITE_PROXY              | 跨域代理，支持配置多个 例如： [["/api","https://xxx"],["/api2","https://xxx"]]，默认 [] |

### 基础用法

```js
const { VITE_PUBLIC_PATH } = import.meta.env;
console.log("当前环境变量VITE_PUBLIC_PATH为：", VITE_PUBLIC_PATH);
```

## 内置功能

### 1、service 模块

1.1、多实例 http 请求

`src/services/instances` 中添加实例，项目中已有默认实例 `default.js(http)`, `instance1.js(http1)`

后续需要添加其它 axios 实例：

```js
  1、在 `src/services/instances` 文件夹中添加实例
  2、`src/services/config/instance-mapping` 实例别名映射中配置key(对应文件名)及别名(后续使用的实列名)
  3、在`.env.development` / `.env.production` 中配置服务路径：例如：`baseURL: import.meta.env.VITE_API_URL`（注意配置的键名需要以 `VITE_` 开头，否则无法读取）
```

#### 1.2、使用 http 请求实例

```js
// src/api/index.js
import services from "@/services";
const { http, http1 } = services;

export const Api = {
  getRequest: (params) => http.get("/xxx", params),
  postRequest: (params) => http1.post("/xxx", params),
  putRequest: (params) => http.put("/xxx", params),
  deleteRequest: (params) => http1.delete("/xxx", params),
  downloadRequest: (params) => http.download("/xxx", params),
  uploadRequest: (params) => http1.upload("/xxx", params)
};
```

### 目录结构

```txt
nari-admin-vue2-template
├─ .husky                 # husky 配置文件
├─ plop                   # 配置模板
│  ├─ utils               # 拉取配置工具类
│  ├─ plopfile.js         # 模板生成配置脚本
├─ build                  # Vite 配置项
├─ public                 # 静态资源文件（该文件夹不会被打包）
├─ src
│  ├─ api                 # API 接口管理
│  ├─ assets              # 静态资源文件
│  ├─ components          # 全局组件
│  ├─ config              # 全局配置项
│  ├─ layout              # 框架布局模块
│  ├─ router              # 路由管理
│  ├─ services            # 多实例 http 请求配置项
│  ├─ store               # vuex store
│  ├─ theme               # 全局样式文件
│  ├─ utils               # 常用工具库
│  ├─ views               # 项目所有页面
│  ├─ App.vue             # 项目主组件
│  └─ main.js             # 项目入口文件
├─ .editorconfig          # 统一不同编辑器的编码风格
├─ .env                   # vite 常用配置
├─ .env.development       # 开发环境配置
├─ .env.production        # 生产环境配置
├─ .eslintignore          # 忽略 Eslint 校验
├─ .eslintrc.cjs          # Eslint 校验配置文件
├─ .gitignore             # 忽略 git 提交
├─ .npmrc                 # npm 配置
├─ .prettierignore        # 忽略 Prettier 格式化
├─ .prettierrc            # Prettier 格式化配置
├─ CHANGELOG.md           # 项目更新日志
├─ deploy-config.json     # 发布默认配置
├─ deploy.cjs             # 发布指令文件
├─ index.html             # 入口 html
├─ package-lock.json      # 依赖包包版本锁
├─ package.json           # 依赖包管理
├─ postcss.config.cjs     # postcss 配置
├─ README.md              # README 介绍
├─ tailwind.config.js     # tailwind 全局配置
└─ vite.config.ts         # vite 全局配置文件
```

### 2、路由和菜单

路由配置在 `src/router` 文件下：

- 项目默认采用动态添加路由方式控制
- 需要其它路由功能根据自己项目需求改造

#### 2.1、目录结构

```txt

router                     # 路由相关文件
├─ modules                 # 路由模块
│ ├─ home.js               # 动态路由模块
│ ├─ menu.js               # 动态路由模块
│ └─ remaining.js          # 静态路由配置
├─ index.js                # 封装后的router
└─ menu.json               # 菜单配置模拟服务数据
```

页面文件必须在 `src/views` 文件下创建，并且文件命名必须和 `component` 路径命名一致，否则无法动态匹配文件生成路由

```js
function eachRouter(item, components) {
  const path = item.component;
  if (isString(path) && path) {
    // 引入 views 文件夹下所有 vue 文件
    const module = path.startsWith("layout") ? Layout : components[`/src/views/${path}.vue`];
    if (module) {
      item.component = module;
    } else {
      console.error(`Module not found for path: ${path}`);
    }
  }
  // 当没有路由名称时，自动设置一个
  if (!item.name) {
    item.name = item.component?.name;
  }
  if (item.children && item.children.length) {
    item.children.forEach((sub) => eachRouter(sub, components));
  }
}
```

```js
// 路由和菜单配置
{
  // 路由地址
  path: string;
  // 路由名字（必须保持唯一）
  name: string;
  // 按需加载需要展示的 layout 主页面
  component: RouteComponent;
  // 路由重定向
  redirect: string;
  meta: {
    // 菜单图标
    icon: string;
    // 菜单名称
    title: string;
    // 是否显示该菜单
    isHide: boolean;
    // 需要内嵌的iframe或第三方链接地址
    isLink: string;
    // 是否缓存该路由页面（开启后，会保存该页面的整体状态，刷新后会清空状态）
    isKeepAlive: boolean;
    // 是否固定标签页（开启后，打开的菜单标签不能关闭）
    isAffix: boolean;
    // 是否内嵌的iframe或第三方页面
    isIframe: boolean;
  };
  // 多级路由嵌套
  children: [**]
};
```

#### 2.2、路由实现

- 默认根据本地 Json 接口返回的数据生成动态路由
- 如果想把路由变成后端请求，直接更改 `src/api/index.js => fetchMenuList` 方法

```
import menuList from "@/router/menu.json";

// 获取菜单列表
export const Api = {
  // fetchMenuList: (params) => http.get(prefix + `/menu/list`, params),
	// 如果想让菜单变为本地数据，注释上一行代码，并引入本地 menu.json 数据
  fetchMenuList: () => menuList
};
```

#### 2.3、新增路由

```js
// 一级路由
// 最简代码，也就是这些字段必须有
const home = [
  {
    path: "/home",
    name: "home",
    component: "home/index",
    meta: {
      title: "首页"
    }
  }
];

const home = {
  path: "/home/<USER>",
  name: "home",
  component: "home/index",
  meta: {
    icon: "el-icon-s-home",
    title: "首页",
    isLink: "",
    isHide: false,
    isKeepAlive: true,
    isAffix: true,
    isIframe: false
  }
};

// 二级路由
const menu = [
  {
    path: "/menu",
    name: "menu",
    component: "layout/routerView/parent",
    redirect: "/menu/menu1",
    meta: {
      title: "菜单嵌套"
    },
    children: [
      {
        path: "/menu/menu1",
        name: "menu1",
        component: "menu/menu1/index",
        meta: {
          title: "菜单一"
        }
      },
      {
        path: "/menu/menu2",
        name: "menu2",
        component: "menu/menu2/index",
        meta: {
          title: "菜单二"
        }
      }
    ]
  }
];
```

#### 2.4、菜单

- 根据后台返回的菜单数据转换而来
- 所以只要更改后台数据菜单会随之改变，整个项目只需维护一套数据即可

> 菜单展示时需要过滤掉 isHide 属性为 true 的路由

```js
// utils/menu.js：
/**
 * @description 使用递归过滤出需要渲染在左侧菜单的列表 (需剔除 isHide == true 的菜单)
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 * */
export function getShowMenuList(menuList) {
  let newMenuList = JSON.parse(JSON.stringify(menuList));
  return newMenuList.filter((item) => {
    item.children?.length && (item.children = getShowMenuList(item.children));
    return !item.meta?.isHide;
  });
}
```

### 3、图标

本地图标是指通过本地安装各类图标库依赖包或者本地引入静态资源（比如 iconfont、svg ）的图标

#### svg 的引入

平台将 `svg` 放到了 `src/assets/svg` 文件夹里，该文件夹作为 `svg` 文件统一存放处

- 平台引入了 vite-plugin-svg-icons `vite` 插件 , 您可以像 `vue` 组件一样使用，注意 `icon-name` 的名称必须跟 svg 文件命名一致，svg 图标需要设置颜色，文件需把 `fill` 属性删除

```js
<template>
  <svg-icon icon-name="logo-small"></svg-icon>
</template>
```

### 4、样式

平台将样式放到了 `src/theme` 文件夹里作为统一存放处，后续开发需求添加修改样式可在 `other.scss` 文件添加，或新增样式文件在 `index.scss` 中引入使用

## 函数工具和组件

### 1、函数工具

- 封装一些常用的工具函数（utils）
- 后续开发必须在 src/utils 添加文件哦
- 现提供 **`本地存储相关函数`** 工具函数，storageLocal、storageSession 方法

| 方法名 | 说明                        | 参数                   |
| ------ | --------------------------- | ---------------------- |
| set    | 储存对应键名的 Storage 对象 | k （键名）, v （键值） |
| get    | 获取对应键名的 Storage 对象 | k （键名）, v （键值） |
| remove | 删除对应键名的 Storage 对象 | k （键名）             |
| clear  | 删除此域的所有 Storage 对象 |

```js
<script>
// storageLocal、storageSession 使用方法一致
import { Local, Session } from "@/utils/storage";
export default {
  methods: {
    set() {
      Local().set("info", {
        name: "xiaoming",
        age: 18
      });
    },
    get() {
      const info = Local().get("info");
    },
    remove() {
      Local().remove("info");
    },
    clear() {
      Local().clear();
    }
  }
}
</script>
```
