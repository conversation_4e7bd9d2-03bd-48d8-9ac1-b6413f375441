import { ThemeManager } from "../core/ThemeManager.js";

/**
 * 处理原始图形数据，将API返回的数据转换为适合图形渲染的格式
 * @param {Object} graphAPIData - 从API获取的原始图形数据
 * @param {number} graphWidth - 图形容器的宽度
 * @param {number} graphHeight - 图形容器的高度
 * @returns {Object} 包含处理后的节点、链接和节点映射的对象
 */
export function processRawGraphData(graphAPIData, graphWidth, graphHeight) {
  if (!graphAPIData || !graphAPIData.data || !graphAPIData.data.data) {
    console.warn("数据格式不符合规则,无法处理");
    return { nodes: [], links: [], nodeMap: new Map() };
  }

  const rawNodes = graphAPIData.data.data;
  const rawLinks = graphAPIData.data.links || [];
  const nodeMap = new Map();

  const nodes = rawNodes
    .filter((item) => item.id !== undefined || item.name) // 确保节点有id或name
    .map((node) => {
      const newNode = { ...node };
      if (newNode.id === null || newNode.id === undefined) {
        newNode.id = newNode.name; // 如果id为null/undefined，则使用name作为id
      }
      // 初始化节点位置以避免NaN，如果尚未存在
      newNode.x = newNode.x || Math.random() * graphWidth;
      newNode.y = newNode.y || Math.random() * graphHeight;
      nodeMap.set(newNode.id, newNode);
      return newNode;
    });

  const links = rawLinks
    .map((link, index) => {
      const sourceId = typeof link.source === "object" && link.source !== null ? link.source.id : link.source;
      const targetId = typeof link.target === "object" && link.target !== null ? link.target.id : link.target;

      const processedLink = {
        ...link,
        source: sourceId,
        target: targetId
      };

      // 为没有ID的连接线生成唯一ID
      if (!processedLink.id) {
        // 使用时间戳+随机数生成唯一ID
        processedLink.id = `link_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      return processedLink;
    })
    .filter((link) => {
      const sourceExists = nodeMap.has(link.source);
      const targetExists = nodeMap.has(link.target);
      if (!sourceExists) console.warn(`Source node with id "${link.source}" not found for a link.`);
      if (!targetExists) console.warn(`Target node with id "${link.target}" not found for a link.`);
      return sourceExists && targetExists;
    });

  return { nodes, links, nodeMap };
}

/**
 * 创建类别颜色比例函数和类别名称映射
 * @param {Object} graphAPIData - 包含类别信息的图形API数据
 * @param {Object|null} customColorMap - 自定义颜色映射，如果为null则使用ThemeManager的颜色
 * @param {ThemeManager} themeManager - 主题管理器实例，用于获取主题颜色
 * @returns {Object} 包含类别颜色比例函数、类别名称映射和颜色映射的对象
 */
export function createCategoryColorScaleAndNames(graphAPIData, customColorMap = null, themeManager = null) {
  const categoryNames = {};
  let colorMap = customColorMap || getDefaultColorMap(themeManager);

  // 如果graphAPIData中有categories且包含color属性，优先使用这些颜色
  if (graphAPIData && graphAPIData.data && graphAPIData.data.categories) {
    // 创建一个新的颜色映射，以免修改原有的colorMap
    const categoriesColorMap = { ...colorMap };

    graphAPIData.data.categories.forEach((cat, index) => {
      // 使用cat.id（如果可用），否则使用索引。确保键是一致的。
      const key = cat.id !== undefined ? cat.id : index;
      categoryNames[key] = cat.name;

      // 如果category有color属性，使用它来覆盖默认颜色
      if (cat.color) {
        categoriesColorMap[key] = cat.color;
      }
    });

    // 使用更新后的颜色映射
    colorMap = categoriesColorMap;
  }

  const categoryColorScale = (category) => {
    // 处理直接类别ID和可能传递"default"的情况
    return colorMap[category] !== undefined ? colorMap[category] : colorMap["default"];
  };

  return { categoryColorScale, categoryNames, colorMap }; // 同时返回colorMap用于getCategoryColorByName
}

/**
 * 获取默认颜色映射，从ThemeManager读取当前主题的类别颜色
 * @param {ThemeManager} themeManager - 主题管理器实例，如果未提供则创建新实例
 * @returns {Object} 颜色映射对象
 */
export function getDefaultColorMap(themeManager = null) {
  // 如果没有传入ThemeManager实例，创建一个新的
  const manager = themeManager || new ThemeManager();

  // 获取当前主题的类别颜色配置
  const currentThemeConfig = manager.getCurrentThemeConfig();

  return currentThemeConfig.category;
}

/**
 * 根据类别名称获取对应的颜色
 * @param {string} categoryName - 类别名称
 * @param {Object} categoryNames - 类别名称映射对象
 * @param {Object} colorMap - 颜色映射对象
 * @returns {string} 对应的颜色值
 */
export function getCategoryColorByName(categoryName, categoryNames, colorMap) {
  let categoryId = "default"; // 默认为'default'
  if (categoryNames) {
    for (const [id, name] of Object.entries(categoryNames)) {
      if (name === categoryName) {
        categoryId = id; // categoryNames的键可能是字符串或数字
        break;
      }
    }
  }
  // 如果categoryId像'类别X'且未找到，则使用数字部分，或保持默认
  if (typeof categoryId === "string" && !colorMap[categoryId] && categoryId.startsWith("类别")) {
    const numPart = parseInt(categoryId.replace("类别", ""), 10);
    if (!isNaN(numPart) && colorMap[numPart] !== undefined) {
      categoryId = numPart;
    } else {
      categoryId = "default";
    }
  } else if (typeof categoryId !== "number" && colorMap[categoryId] === undefined) {
    // 如果categoryId是字符串但不是'default'且不在colorMap中，尝试解析为数字
    const numId = parseInt(categoryId, 10);
    if (!isNaN(numId) && colorMap[numId] !== undefined) {
      categoryId = numId;
    } else {
      categoryId = "default"; // 如果是无法识别的字符串ID，则回退到默认值
    }
  }

  return colorMap[categoryId] !== undefined ? colorMap[categoryId] : colorMap["default"];
}

/**
 * 统计各类别节点的数量
 * @param {Array} nodes - 节点数组
 * @param {Object} categoryNames - 类别名称映射对象
 * @returns {Object} 包含各类别及其节点数量的统计对象
 */
export function countCategoryNodeStats(nodes, categoryNames) {
  const stats = {};
  // 使用categoryNames中的所有类别名称初始化统计，设置计数为0
  if (categoryNames) {
    Object.values(categoryNames).forEach((name) => {
      stats[name] = 0;
    });
  }
  // 确保"其他"类别存在，如果特定名称未覆盖
  if (!stats["其他"] && !Object.values(categoryNames).includes("其他")) {
    stats["其他"] = 0;
  }

  nodes.forEach((node) => {
    const categoryId = node.category !== undefined ? node.category : "default";
    let categoryName = categoryNames && categoryNames[categoryId] ? categoryNames[categoryId] : "其他";

    // 如果categoryId是'default'但categoryNames['default']未定义，确保映射到"其他"
    if (categoryId === "default" && !categoryNames[categoryId]) {
      categoryName = "其他";
    } else if (!categoryNames[categoryId] && categoryId !== "default") {
      // 如果categoryId是某个不在categoryNames中的数字，映射到"类别X"或"其他"
      categoryName = `类别${categoryId}`;
      if (!stats[categoryName]) {
        // 如果"类别X"不是预定义名称
        categoryName = "其他";
      }
    }

    if (!stats[categoryName]) {
      // 应该已经初始化，但作为后备方案
      stats[categoryName] = 0;
    }
    stats[categoryName]++;
  });
  return stats;
}

/**
 * 在节点数组中查找指定标识符的节点
 * @param {Array} nodes - 节点数组
 * @param {string|number} identifier - 用于搜索的标识符（ID、名称或描述）
 * @returns {Object|null} 找到的节点对象，如果未找到则返回null
 */
export function findNode(nodes, identifier) {
  if (!identifier || !nodes || nodes.length === 0) return null;

  const searchText = String(identifier).toLowerCase();

  // 1. 精确ID匹配
  let node = nodes.find((n) => n.id !== undefined && String(n.id).toLowerCase() === searchText);
  if (node) return node;

  // 2. 精确名称匹配
  node = nodes.find((n) => n.name && String(n.name).toLowerCase() === searchText);
  if (node) return node;

  // 3. 在ID、名称或描述中进行子字符串匹配
  node = nodes.find(
    (n) =>
      (n.id !== undefined && String(n.id).toLowerCase().includes(searchText)) ||
      (n.name && String(n.name).toLowerCase().includes(searchText)) ||
      (n.des && String(n.des).toLowerCase().includes(searchText))
  );
  return node; // 返回找到的节点或null
}

/**
 * 根据查询条件过滤图形元素（节点和链接）
 * @param {Array} allNodes - 所有节点数组
 * @param {Array} allLinks - 所有链接数组
 * @param {string} query - 搜索查询字符串
 * @returns {Object} 包含过滤后的节点和链接数组的对象
 */
export function filterGraphElements(allNodes, allLinks, query) {
  if (!query || !query.trim()) {
    return { filteredNodes: [], filteredLinks: [] };
  }
  const searchText = query.toLowerCase().trim();

  const matchedNodes = allNodes.filter((node) => {
    const id = node.id !== undefined ? String(node.id).toLowerCase() : "";
    const name = node.name ? String(node.name).toLowerCase() : "";
    const des = node.des ? String(node.des).toLowerCase() : "";
    return id.includes(searchText) || name.includes(searchText) || des.includes(searchText);
  });

  const matchedLinks = allLinks.filter((link) => {
    const id = link.id ? String(link.id).toLowerCase() : "";
    const name = link.name ? String(link.name).toLowerCase() : "";
    const type = link.type ? String(link.type).toLowerCase() : "";
    const measureValue = link.attribute && link.attribute.measurevalue ? String(link.attribute.measurevalue).toLowerCase() : "";
    return id.includes(searchText) || name.includes(searchText) || type.includes(searchText) || measureValue.includes(searchText);
  });

  // 简单排序和切片，如果需要可以更复杂
  const sortedNodes = matchedNodes.slice(0, 5); // 示例：限制为5个结果
  const sortedLinks = matchedLinks.slice(0, 5); // 示例：限制为5个结果

  return { filteredNodes: sortedNodes, filteredLinks: sortedLinks };
}

/**
 * 获取链接的端点节点
 * @param {Object} link - 链接对象
 * @param {Map} nodeMap - 节点映射表
 * @returns {Object} 包含源节点和目标节点的对象
 */
export function getLinkEndNodes(link, nodeMap) {
  if (!link || !nodeMap) return { sourceNode: null, targetNode: null };
  const sourceNode = typeof link.source === "object" ? link.source : nodeMap.get(link.source);
  const targetNode = typeof link.target === "object" ? link.target : nodeMap.get(link.target);
  return { sourceNode, targetNode };
}

/**
 * 根据源节点和目标节点ID查找链接
 * @param {Array} links - 链接数组
 * @param {string|number} sourceNodeId - 源节点ID
 * @param {string|number} targetNodeId - 目标节点ID
 * @returns {Object|null} 找到的链接对象，如果未找到则返回null
 */
export function findLinkByNodes(links, sourceNodeId, targetNodeId) {
  if (!links || !sourceNodeId || !targetNodeId) return null;
  // 确保ID正确比较，特别是当它们来自不同来源时（例如字符串与数字）
  const sIdStr = String(sourceNodeId);
  const tIdStr = String(targetNodeId);

  return links.find((link) => {
    const linkSourceIdStr = String(typeof link.source === "object" ? link.source.id : link.source);
    const linkTargetIdStr = String(typeof link.target === "object" ? link.target.id : link.target);

    return (linkSourceIdStr === sIdStr && linkTargetIdStr === tIdStr) || (linkSourceIdStr === tIdStr && linkTargetIdStr === sIdStr);
  });
}

/**
 * 根据链接ID查找链接
 * @param {Array} links - 链接数组
 * @param {string|number} linkId - 链接ID
 * @returns {Object|null} 找到的链接对象，如果未找到则返回null
 */
export function findLinkById(links, linkId) {
  if (!links || linkId === undefined || linkId === null) return null;
  const idStr = String(linkId);
  return links.find((link) => String(link.id) === idStr);
}

/**
 * 从ThemeManager获取指定类别的颜色
 * @param {string|number} category - 类别ID
 * @param {ThemeManager} themeManager - 主题管理器实例，如果未提供则创建新实例
 * @returns {string} 对应的颜色值
 */
export function getCategoryColorFromTheme(category, themeManager = null) {
  const manager = themeManager || new ThemeManager();
  return manager.getCategoryColor(category);
}

/**
 * 获取当前主题的所有类别颜色映射
 * @param {ThemeManager} themeManager - 主题管理器实例，如果未提供则创建新实例
 * @returns {Object} 完整的类别颜色映射对象
 */
export function getAllCategoryColorsFromTheme(themeManager = null) {
  const manager = themeManager || new ThemeManager();
  const currentThemeConfig = manager.getCurrentThemeConfig();
  return currentThemeConfig.category;
}
