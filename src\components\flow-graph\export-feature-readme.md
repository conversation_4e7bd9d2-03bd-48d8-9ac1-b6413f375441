# FlowGraph 数据导出功能说明

## 功能概述

FlowGraph 组件现在支持右键菜单导出当前图谱数据为 JSON 格式，并且能够正确处理节点拖动后的位置坐标转换。

## 新增功能

### 1. 右键菜单导出项

- 在图谱区域右键点击，会出现右键菜单
- 新增了"导出JSON数据"菜单项，带有📊图标
- 点击后会将当前图谱数据导出为JSON文件

### 2. 坐标转换功能

- 支持将拖动后的屏幕坐标转换回原始pos坐标
- 转换算法考虑了画布尺寸和posLayoutScale缩放比例
- 导出的数据保持原始数据格式，可以重新导入使用

### 3. 数据完整性

- 保持原始数据的所有属性不变
- 导出的JSON格式与原始数据格式一致
- 包含导出元数据（时间戳、版本、节点/连接线数量等）
- 提供数据验证功能确保导出数据的正确性

## 技术实现

### 新增文件

#### `src/components/flow-graph/utils/dataExportUtils.js`
数据导出工具类，提供以下主要功能：

- `exportGraphData()` - 主导出函数
- `convertPositionToOriginal()` - 坐标转换函数
- `downloadAsJson()` - 文件下载函数
- `validateExportData()` - 数据验证函数
- `createExportSummary()` - 导出摘要生成

### 修改文件

#### `src/components/flow-graph/index.vue`
- 导入DataExportUtils工具类
- 修改MenuManager菜单项配置，新增"导出JSON数据"项
- 新增`exportGraphData()`方法实现导出逻辑
- 修改`handleMenuAction()`处理导出操作

#### `src/components/flow-graph/theme/common/_menu.scss`
- 新增图标样式定义
- 添加`.graph-icon-export`和`.graph-icon-reload`样式

## 坐标转换算法

### 原始坐标转屏幕坐标（DataProcessor中的逻辑）
```javascript
// pos: [-1, -1] -> 左上角 (0,0)
// pos: [0,0]   -> 中心 (width/2, height/2)  
// pos: [1,1]   -> 右下角 (width, height)
const scaledPosX = node.pos[0] * posLayoutScale;
const scaledPosY = node.pos[1] * posLayoutScale;
const x = ((scaledPosX + 1) / 2) * width;
const y = ((scaledPosY + 1) / 2) * height;
```

### 屏幕坐标转原始坐标（导出工具中的逆向算法）
```javascript
const scaledPosX = (currentPos.x / canvasWidth) * 2 - 1;
const scaledPosY = (currentPos.y / canvasHeight) * 2 - 1;
const originalPosX = scaledPosX / posLayoutScale;
const originalPosY = scaledPosY / posLayoutScale;
```

## 使用方法

### 基本导出
1. 在图谱中任意位置右键点击
2. 选择"导出JSON数据"
3. 文件会自动下载到浏览器默认下载目录
4. 文件名格式：`flow-graph-data-YYYY-MM-DD-HH-mm-ss.json`

### 编程方式调用
```javascript
// 在Vue组件中
this.$refs.flowGraph.exportGraphData();

// 监听导出事件
<flow-graph @graph-exported="handleGraphExported" />

handleGraphExported(event) {
  console.log('导出数据:', event.data);
  console.log('导出摘要:', event.summary);
}
```

## 导出数据格式

```json
{
  "code": 200,
  "msg": "导出成功",
  "data": {
    "data": [
      {
        "name": "节点名称",
        "des": "节点描述", 
        "category": 0,
        "pos": [-0.5336, -0.7874], // 转换后的原始坐标
        "attribute": { /* 节点属性 */ },
        "symbolSize": 50,
        // ... 其他原始属性
      }
    ],
    "links": [
      {
        "source": "源节点ID",
        "target": "目标节点ID", 
        "name": "连接线名称",
        "des": "连接线描述",
        // ... 其他原始属性
      }
    ]
  },
  "_exportMetadata": {
    "exportTime": "2024-01-01T12:00:00.000Z",
    "exportVersion": "1.0.0",
    "posLayoutScale": 1,
    "nodeCount": 10,
    "linkCount": 15
  }
}
```

## 特性说明

### 1. 坐标精度
- 坐标转换保留4位小数精度，避免浮点数误差
- 适用于各种画布尺寸和缩放比例

### 2. 数据验证
- 导出前会验证数据完整性
- 检查必要字段是否存在
- 验证失败会显示错误信息

### 3. 兼容性
- 导出的数据格式与原始输入格式兼容
- 可以重新导入到图谱组件中使用
- 支持增量更新模式

### 4. 用户体验
- 导出过程显示进度提示
- 成功导出后显示摘要信息
- 错误处理友好，提供详细错误信息

## 注意事项

1. **浏览器兼容性**: 使用了现代浏览器的Blob和URL API
2. **文件大小**: 大型图谱导出的JSON文件可能较大
3. **坐标转换**: 确保posLayoutScale参数设置正确
4. **数据完整性**: 导出前建议先保存当前工作
5. **文件命名**: 导出文件名包含时间戳，避免重名冲突

## 扩展功能

未来可以考虑添加的功能：
- 导出为其他格式（SVG、PNG等）
- 选择性导出（仅导出选中的节点和连接线）
- 导出配置选项（是否包含当前位置信息等）
- 批量导出多个图谱
- 导出数据压缩 