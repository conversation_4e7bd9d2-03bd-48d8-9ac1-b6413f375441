<template>
  <div
    class="c-graphic-panel"
    :class="[closed ? 'c-graphic-panel-closed' : '', right ? 'c-graphic-panel-r' : '', theme]"
    :style="{
      '--my-panel-width': this.width,
      '--my-panel-top': this.top,
      left: right ? undefined : left,
      right: right ? right : undefined
    }"
  >
    <div style="width: 100%; height: 100%; position: relative">
      <div class="graphic-header">
        {{ title }}
      </div>

      <div
        class="graphic-footer"
        :class="closed ? 'footer-icon-open' : 'footer-icon-closed'"
      >
        <div
          v-if="closed"
          class="my-icon my-icon-open"
          @click="togglePanel"
        >
          <i
            v-if="icon"
            :class="icon"
          ></i>
          <span v-else>{{ right ? "↙" : "↘" }}</span>
        </div>
        <div
          v-else
          class="my-icon my-icon-close"
          @click="togglePanel"
        >
          {{ right ? "➡" : "⬅" }}
        </div>
      </div>
      <div class="graphic-body">
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TogglePanel",
  props: {
    width: {
      mustUseProp: false,
      default: "300px",
      type: String
    },
    left: {
      mustUseProp: false,
      default: "10px",
      type: String
    },
    right: {
      mustUseProp: false,
      default: "",
      type: String
    },
    top: {
      mustUseProp: false,
      default: "10px",
      type: String
    },
    title: {
      mustUseProp: false,
      default: "标题",
      type: String
    },
    state: {
      mustUseProp: false,
      default: false,
      type: Boolean
    },
    icon: {
      mustUseProp: false,
      type: String
    },
    theme: {
      mustUseProp: false,
      default: "light",
      type: String
    }
  },
  data() {
    return {
      closed: false
    };
  },
  watch: {
    state(val) {
      this.closed = val;
    }
  },
  mounted() {
    this.closed = this.state;
  },
  methods: {
    togglePanel() {
      this.closed = !this.closed;
    }
  }
};
</script>
<style lang="scss" scoped>
.c-graphic-panel {
  position: absolute;
  border-radius: 5px;
  z-index: 800;
  width: var(--my-panel-width);
  top: var(--my-panel-top);
  // background-color: #ffffff;
  background-color: #0c2353;
  border: #2168ba solid 1px;
  box-shadow: 0 2px 6px rgba(0, 21, 41, 0.3);
  padding: 10px;
  box-sizing: border-box;
  font-size: 14px;
  color: #666666;
  transition: width 0.3s ease-out;
  z-index: 999;

  .graphic-header {
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
    color: #d9d9d9;
  }

  .graphic-footer {
    text-align: right;
    display: flex;
    place-items: end;
    justify-content: end;
    position: absolute;

    &.footer-icon-open {
      right: -11px;
      top: -11px;
    }

    &.footer-icon-closed {
      right: -9px;
      top: -7px;
    }

    .my-icon {
      border-radius: 5px;
      width: 30px;
      height: 30px;
      font-size: 16px;
      color: #ffffff;
      background-color: transparent;
      display: flex;
      place-items: center;
      justify-content: center;
      cursor: pointer;
      &:hover {
        background-color: #666666;
        color: #ffffff;
      }
      svg {
        fill: currentColor;
        width: 100%;
        height: 100%;
      }
    }
  }

  .graphic-body {
    color: #ffffff;
  }

  .c-title {
    color: #333333;
    font-size: 14px;
    line-height: 40px;
    padding-left: 10px;
    padding-right: 10px;
  }
  .c-content {
    color: #666666;
    font-size: 14px;
    line-height: 20px;
    padding: 6px;
  }
  .c-button {
    line-height: 18px;
    display: inline-block;
    background-color: #035a8a;
    color: #ffffff;
    font-size: 12px;
    padding: 5px 15px;
    text-align: center;
    cursor: pointer;
    border-radius: 5px;
    &:hover {
      background-color: rgba(3, 90, 138, 0.68);
    }
  }
  .c-link {
    color: #167fb7;
    cursor: pointer;
    padding: 0px 15px;
    &:hover {
      text-decoration: underline #167fb7;
    }
  }
  .c-my-options {
    text-align: center;
    .c-my-option-item {
      text-align: left;
      color: #1da9f5;
      cursor: pointer;
      border-radius: 5px;
      padding-left: 10px;
      margin-top: 5px;
      line-height: 25px;
      &:hover {
        background-color: rgba(29, 169, 245, 0.2);
      }
    }
  }
}
.c-graphic-panel-closed {
  width: 30px;
  height: 30px;

  .graphic-header {
    opacity: 0;
    display: none;
  }

  .graphic-body {
    opacity: 0;
    display: none;
  }
}
.c-graphic-panel-r {
  .graphic-footer {
    place-items: end;
    justify-content: start;
  }
}

.dark,
.transparent {
  &.c-graphic-panel {
    // background-color: #ffffff;
    background-color: #1f2b3e;
    border: #3d5a80 solid 1px;
    box-shadow: 0 2px 6px rgba(0, 21, 41, 0.3);
    color: #666666;

    .graphic-header {
      color: #d9d9d9;
    }

    .graphic-footer {
      .my-icon {
        color: #ffffff;
        background-color: transparent;
        cursor: pointer;
        &:hover {
          background-color: #5b709b;
          color: #ffffff;
        }
        svg {
          fill: currentColor;
        }
      }
    }

    .graphic-body {
      color: #ffffff;
    }

    .c-title {
      color: #333333;
    }
    .c-content {
      color: #666666;
    }
    .c-button {
      background-color: #035a8a;
      color: #ffffff;
      &:hover {
        background-color: rgba(3, 90, 138, 0.68);
      }
    }
    .c-link {
      color: #167fb7;
      &:hover {
        text-decoration: underline #167fb7;
      }
    }
    .c-my-options {
      .c-my-option-item {
        color: #1da9f5;
        &:hover {
          background-color: rgba(29, 169, 245, 0.2);
        }
      }
    }
  }
}

.light {
  &.c-graphic-panel {
    background-color: #ffffff;
    border: #ffffff solid 1px;
    box-shadow: 0 2px 6px rgba(0, 21, 41, 0.3);
    color: #2e2e2e;

    .graphic-header {
      color: #0c2353;
    }

    .graphic-footer {
      .my-icon {
        color: #7d9afc;
        background-color: transparent;
        cursor: pointer;
        &:hover {
          color: #918585;
        }
        svg {
          fill: currentColor;
        }
      }
    }

    .graphic-body {
      color: #ffffff;
    }

    .c-title {
      color: #333333;
    }
    .c-content {
      color: #666666;
    }
    .c-button {
      background-color: #035a8a;
      color: #ffffff;
      &:hover {
        background-color: rgba(3, 90, 138, 0.68);
      }
    }
    .c-link {
      color: #167fb7;
      &:hover {
        text-decoration: underline #167fb7;
      }
    }
    .c-my-options {
      .c-my-option-item {
        color: #1da9f5;
        &:hover {
          background-color: rgba(29, 169, 245, 0.2);
        }
      }
    }
  }
}
</style>
