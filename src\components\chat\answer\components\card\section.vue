<template>
  <div class="card-section">
    <!-- <i class="el-icon-coordinate"></i> -->
    {{ data.text }}
  </div>
</template>

<script>
export default {
  name: "SectionCard",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  }
};
</script>
<style lang="scss" scoped>
.card-section {
  display: flex;
  justify-content: flex-start;
  font-size: 14px;
  align-items: center;
  font-weight: bold;
  color: aqua;
  i {
    margin-right: 5px;
    color: var(--nari-c-primary);
  }
  &::before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 12px;
    margin-right: 5px;
    background: var(--nari-c-primary);
  }
}
</style>
