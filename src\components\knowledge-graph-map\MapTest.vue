<template>
  <div class="map-test">
    <h2>地图测试</h2>
    <div class="test-buttons">
      <button @click="testBasicMap">测试基础地图</button>
      <button @click="testMapWithData">测试带数据地图</button>
      <button @click="clearMap">清除地图</button>
    </div>
    <div class="map-container" ref="mapContainer"></div>
    <div class="debug-info">
      <h3>调试信息</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import guangdongGeoJson from './assets/geo/guangdong.json'

export default {
  name: 'MapTest',
  data() {
    return {
      chart: null,
      debugInfo: '等待测试...'
    }
  },
  mounted() {
    this.updateDebugInfo('组件已挂载')
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    updateDebugInfo(info) {
      this.debugInfo = `${new Date().toLocaleTimeString()}: ${info}`
      console.log(info)
    },

    testBasicMap() {
      try {
        this.updateDebugInfo('开始测试基础地图...')
        
        // 清除现有图表
        if (this.chart) {
          this.chart.dispose()
        }

        // 检查容器
        if (!this.$refs.mapContainer) {
          throw new Error('地图容器未找到')
        }

        // 初始化ECharts
        this.chart = echarts.init(this.$refs.mapContainer)
        this.updateDebugInfo('ECharts实例已创建')

        // 注册地图数据
        echarts.registerMap('guangdong', guangdongGeoJson)
        this.updateDebugInfo('地图数据已注册')

        // 设置基础地图选项
        const option = {
          backgroundColor: '#1a1a2e',
          geo: {
            map: 'guangdong',
            roam: true,
            zoom: 1.2,
            center: [113.3, 23.0],
            label: {
              show: true,
              fontSize: 12,
              color: '#ffffff'
            },
            itemStyle: {
              areaColor: '#16213e',
              borderColor: '#0f4c75',
              borderWidth: 2
            },
            emphasis: {
              itemStyle: {
                areaColor: '#0f4c75',
                borderColor: '#3282b8',
                borderWidth: 3
              }
            }
          }
        }

        this.chart.setOption(option)
        this.chart.resize()
        
        this.updateDebugInfo('基础地图渲染成功！')

      } catch (error) {
        this.updateDebugInfo(`基础地图测试失败: ${error.message}`)
        console.error('基础地图测试失败:', error)
      }
    },

    testMapWithData() {
      try {
        this.updateDebugInfo('开始测试带数据的地图...')
        
        if (!this.chart) {
          this.testBasicMap()
          setTimeout(() => this.addTestData(), 500)
          return
        }

        this.addTestData()

      } catch (error) {
        this.updateDebugInfo(`带数据地图测试失败: ${error.message}`)
        console.error('带数据地图测试失败:', error)
      }
    },

    addTestData() {
      try {
        // 测试坐标转换
        const testCoords = [113.264385, 23.129112] // 广州坐标
        const screenCoords = this.chart.convertToPixel('geo', testCoords)
        
        this.updateDebugInfo(`坐标转换测试: ${testCoords} -> ${screenCoords}`)

        if (!screenCoords) {
          throw new Error('坐标转换失败')
        }

        this.updateDebugInfo('坐标转换成功！地图功能正常')

      } catch (error) {
        this.updateDebugInfo(`坐标转换测试失败: ${error.message}`)
        console.error('坐标转换测试失败:', error)
      }
    },

    clearMap() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
        this.updateDebugInfo('地图已清除')
      }
    }
  }
}
</script>

<style scoped>
.map-test {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0f0f0f;
  color: white;
}

h2 {
  margin: 0 0 20px 0;
  color: #3282b8;
}

.test-buttons {
  margin-bottom: 20px;
}

.test-buttons button {
  margin-right: 10px;
  padding: 8px 16px;
  background: #3282b8;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-buttons button:hover {
  background: #0f4c75;
}

.map-container {
  flex: 1;
  min-height: 400px;
  border: 1px solid #333;
  background: #1a1a2e;
}

.debug-info {
  margin-top: 20px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.debug-info h3 {
  margin: 0 0 10px 0;
  color: #4ecdc4;
}

.debug-info pre {
  margin: 0;
  color: #ccc;
  font-size: 12px;
  white-space: pre-wrap;
}
</style>
