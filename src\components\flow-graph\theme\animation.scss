@keyframes blink-animation {
  0% {
    opacity: 1;
  }

  30% {
    opacity: 0.6;
  }

  50% {
    opacity: 0.2;
  }

  70% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

@keyframes link-flow-animation-forward {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: -20;
  }
}

.knowledge-graph-container g.node-group.node-blink .node-shape {
  animation: blink-animation 1.5s infinite ease-in-out;
}

.links path.link-blink {
  animation: blink-animation 1.5s infinite ease-in-out;
}

.links path.link-flow-forward {
  stroke-dasharray: 10, 10;
  animation: link-flow-animation-forward 1s linear infinite;
}
