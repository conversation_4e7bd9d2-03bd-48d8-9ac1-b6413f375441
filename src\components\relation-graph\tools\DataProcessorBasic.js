// src/utils/dataProcessor.js

import { Node } from "../model/Node";
import { Link } from "../model/Link";

// 添加操作类型参数和currentData参数
export function initDataBySource(data, colorList, getColorForLabel, dataVersion, theme, operationType = "replace", currentData = null) {
  if (dataVersion === "v1") {
    return initDataBySourceV1(data, colorList, getColorForLabel, theme, operationType, currentData);
  } else {
    return initDataBySourceV2(data, colorList, getColorForLabel, theme, operationType, currentData);
  }
}

function initDataBySourceV1(data, colorList, getColorForLabel, theme, operationType, currentData) {
  if (!data.result) {
    data = { result: { entity: data.nodes ? data.nodes : data.entity, relation: data.relations ? data.relations : data.relation, labelField: data.aliasLabel } };
  }
  const { entity, relation, labelField } = data.result ? data.result : data.data;

  if (labelField) {
    entity.forEach((item) => {
      item.label = item[labelField] ? item[labelField] : "未分类";
    });
  }

  // 转换新的节点数据
  const newNodes = entity.map(
    (item) =>
      new Node({
        id: item.id.toString(),
        text: item.name.length > 10 ? item.name.substring(0, 9) + "..." : item.name,
        color: getColorForLabel(item.label, colorList),
        fontSize: item.fontSize ? item.fontSize : null,
        fontColor: item.fontColor ? item.fontColor : null,
        weight: item.weight ? item.weight : 1,
        data: {
          id: item.id,
          text: item.name,
          label: item.label,
          description: item.description,
          attribute: item.attribute ? item.attribute : {}
        }
      })
  );

  let linkFontColor = "#000";
  if (theme === "dark") {
    linkFontColor = "#fff";
  } else if (theme === "transparent") {
    linkFontColor = "#fff";
  }

  let linkColor = "rgb(136 133 255)";
  if (theme === "dark") {
    linkColor = "#cee8ff";
  } else if (theme === "transparent") {
    linkColor = "#cee8ff";
  }

  // 转换新的关系数据
  const newLinks = relation.map(
    (item) =>
      new Link({
        from: item.startPro.toString(),
        to: item.endPro.toString(),
        text: item.rel,
        fontColor: linkFontColor,
        color: linkColor,
        data: {
          id: item.id,
          startName: item.startName,
          endName: item.endName
        }
      })
  );

  // 根据操作类型处理数据
  if (operationType === "replace" || !currentData) {
    // 替换操作或首次加载，直接返回新数据
    return { nodes: newNodes, links: newLinks };
  } else if (operationType === "add") {
    // 增量操作，合并新旧数据，避免ID重复
    const existingNodeIds = new Set(currentData.nodes.map((node) => node.id));
    const existingLinkIds = new Set(currentData.links.map((link) => `${link.from}-${link.to}-${link.text}`));

    const nodesToAdd = newNodes.filter((node) => !existingNodeIds.has(node.id));
    const linksToAdd = newLinks.filter((link) => !existingLinkIds.has(`${link.from}-${link.to}-${link.text}`));

    return {
      nodes: [...currentData.nodes, ...nodesToAdd],
      links: [...currentData.links, ...linksToAdd]
    };
  } else if (operationType === "remove") {
    // 减量操作，从现有数据中移除指定数据
    const nodeIdsToRemove = new Set(newNodes.map((node) => node.id));
    const linkIdsToRemove = new Set(newLinks.map((link) => `${link.from}-${link.to}-${link.text}`));

    const remainingNodes = currentData.nodes.filter((node) => !nodeIdsToRemove.has(node.id));
    const remainingLinks = currentData.links.filter((link) => !linkIdsToRemove.has(`${link.from}-${link.to}-${link.text}`));

    return {
      nodes: remainingNodes,
      links: remainingLinks
    };
  }
}

function initDataBySourceV2(data, colorList, getColorForLabel, theme, operationType, currentData) {
  if (!data.result) {
    data = { result: { entity: data.nodes ? data.nodes : data.entity, relation: data.relations ? data.relations : data.relation, labelField: data.aliasLabel } };
  }
  const { entity, relation, labelField } = data.result ? data.result : data.data;

  if (labelField) {
    entity.forEach((item) => {
      item.label = item[labelField] ? item[labelField] : "未分类";
    });
  }

  // 转换新的节点数据
  const newNodes = entity.map(
    (item) =>
      new Node({
        id: item.id.toString(),
        text: item.name.length > 10 ? item.name.substring(0, 9) + "..." : item.name,
        color: getColorForLabel(item.label, colorList),
        weight: item.weight ? item.weight : 1,
        fontSize: item.fontSize ? item.fontSize : null,
        fontColor: item.fontColor ? item.fontColor : null,
        data: {
          id: item.id,
          text: item.name,
          label: item.label,
          description: item.description,
          attribute: item.attribute ? item.attribute : {}
        }
      })
  );

  let linkFontColor = "#000";
  if (theme === "dark") {
    linkFontColor = "#fff";
  } else if (theme === "transparent") {
    linkFontColor = "#fff";
  }

  let linkColor = "rgb(136 133 255)";
  if (theme === "dark") {
    linkColor = "#cee8ff";
  } else if (theme === "transparent") {
    linkColor = "#cee8ff";
  }

  // 转换新的关系数据
  const newLinks = relation.map((item) => {
    let linkOption = {
      from: item.from.toString(),
      to: item.to.toString(),
      text: item.text,
      fontColor: linkFontColor,
      color: linkColor
    };

    return new Link(Object.assign(item, linkOption));
  });

  // 根据操作类型处理数据
  if (operationType === "replace" || !currentData) {
    // 替换操作或首次加载，直接返回新数据
    return { nodes: newNodes, links: newLinks };
  } else if (operationType === "add") {
    // 增量操作，合并新旧数据，避免ID重复
    const existingNodeIds = new Set(currentData.nodes.map((node) => node.id));
    const existingLinkIds = new Set(currentData.links.map((link) => `${link.from}-${link.to}-${link.text}`));

    const nodesToAdd = newNodes.filter((node) => !existingNodeIds.has(node.id));
    const linksToAdd = newLinks.filter((link) => !existingLinkIds.has(`${link.from}-${link.to}-${link.text}`));

    return {
      nodes: [...currentData.nodes, ...nodesToAdd],
      links: [...currentData.links, ...linksToAdd]
    };
  } else if (operationType === "remove") {
    // 减量操作，从现有数据中移除指定数据
    const nodeIdsToRemove = new Set(newNodes.map((node) => node.id));
    const linkIdsToRemove = new Set(newLinks.map((link) => `${link.from}-${link.to}-${link.text}`));

    const remainingNodes = currentData.nodes.filter((node) => !nodeIdsToRemove.has(node.id));
    const remainingLinks = currentData.links.filter((link) => !linkIdsToRemove.has(`${link.from}-${link.to}-${link.text}`));

    return {
      nodes: remainingNodes,
      links: remainingLinks
    };
  }
}
