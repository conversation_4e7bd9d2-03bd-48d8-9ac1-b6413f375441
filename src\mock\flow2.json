{"status": true, "code": 200, "data": {"data": [{"state": 0, "id": null, "name": "114560315689012929", "des": "220kV五圣乙线4649开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 100, "id": null, "name": "116530640525984710", "des": "220kV五圣乙线4649线路", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "#000000"}, "attribute": {}}, {"state": 0, "id": null, "name": "114560315689013497", "des": "220kV五圣乙线4649开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 5, "id": null, "name": "114560315689013496", "des": "220kV五圣甲线4648开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 1, "id": null, "name": "114560315689012928", "des": "220kV五圣甲线4648开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 100, "id": null, "name": "116530640525984709", "des": "220kV五圣甲线4648线路", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "#000000"}, "attribute": {}}, {"state": 2, "id": null, "name": "114560315689012927", "des": "220kV圣恩乙线4266开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 100, "id": null, "name": "116530640525984602", "des": "220kV圣恩乙线4266线路", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "#000000"}, "attribute": {}}, {"state": 1, "id": null, "name": "114560315689012926", "des": "220kV圣恩甲线2745开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 2, "id": null, "name": "114560315689012925", "des": "220kV#2主变变高2202开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 1, "id": null, "name": "114560315689012924", "des": "220kV#1主变变高2201开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 0, "id": null, "name": "114560315689012922", "des": "220kV母联2012开关", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "red"}, "attribute": {}}, {"state": 100, "id": null, "name": "116530640525984601", "des": "220kV圣恩甲线2745线路", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "#000000"}, "attribute": {}}, {"state": 100, "id": null, "name": "115404740619142087", "des": "220kV#1母线", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "#000000"}, "attribute": {}}, {"state": 100, "id": null, "name": "115404740619142088", "des": "220kV#2母线", "category": 0, "symbol": "circle", "symbolSize": 50, "label": {"color": "#000000"}, "attribute": {}}, {"state": 100, "id": null, "name": "有源Net_1", "des": "有源Net_1", "category": 2, "symbol": "circle", "symbolSize": 50, "label": {"color": "#000000"}, "attribute": {}}], "links": [{"source": "114560315689012928", "target": "115404740619142088", "name": "Switch(path=TopoPathData(ID=114560315689012928, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000384008023, endConnectivityNode=10000384008024, BusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))ACLineSegment(path=LinePathData(ID=116530640525984709, type=com.nari.gz.cimbasemodule.bean.device.ACLineSegment, startConnectivityNode=10000329008017, EndConnectivityNode=10000384008058, StartST=113997365735588169, EndST=113997365735588224, name=null, StartBusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], EndBusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]), InCircle=true, MaxCurrent=0, ratedCurrent=0.0, length=0.0, r=0.0, x=0.0, bch=0.0, r0=0.0, x0=0.0, b0ch=0.0)", "des": "114560315689012928-115404740619142088", "label": null}, {"source": "114560315689012929", "target": "115404740619142088", "name": "Switch(path=TopoPathData(ID=114560315689012929, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000384008023, endConnectivityNode=10000384008024, BusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))ACLineSegment(path=LinePathData(ID=116530640525984709, type=com.nari.gz.cimbasemodule.bean.device.ACLineSegment, startConnectivityNode=10000329008017, EndConnectivityNode=10000384008058, StartST=113997365735588169, EndST=113997365735588224, name=null, StartBusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], EndBusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]), InCircle=true, MaxCurrent=0, ratedCurrent=0.0, length=0.0, r=0.0, x=0.0, bch=0.0, r0=0.0, x0=0.0, b0ch=0.0)", "des": "114560315689012929-115404740619142088", "label": null}, {"source": "114560315689013496", "target": "116530640525984709", "name": "Switch(path=TopoPathData(ID=114560315689013496, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000384008023, endConnectivityNode=10000384008024, BusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))ACLineSegment(path=LinePathData(ID=116530640525984709, type=com.nari.gz.cimbasemodule.bean.device.ACLineSegment, startConnectivityNode=10000329008017, EndConnectivityNode=10000384008058, StartST=113997365735588169, EndST=113997365735588224, name=null, StartBusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], EndBusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]), InCircle=true, MaxCurrent=0, ratedCurrent=0.0, length=0.0, r=0.0, x=0.0, bch=0.0, r0=0.0, x0=0.0, b0ch=0.0)", "des": "114560315689013496-116530640525984709", "label": null}, {"source": "115404740619142087", "target": "114560315689012922", "name": "SingleNodeEquipment(nodeId=10000329008007)Switch(path=TopoPathData(ID=114560315689012922, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008026, endConnectivityNode=10000329008025, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "115404740619142087-114560315689012922", "label": null}, {"source": "115404740619142087", "target": "114560315689012926", "name": "SingleNodeEquipment(nodeId=10000329008007)Switch(path=TopoPathData(ID=114560315689012926, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008016, endConnectivityNode=10000329008015, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "115404740619142087-114560315689012926", "label": null}, {"source": "115404740619142087", "target": "114560315689012924", "name": "SingleNodeEquipment(nodeId=10000329008007)Switch(path=TopoPathData(ID=114560315689012924, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008008, endConnectivityNode=10000329008027, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "115404740619142087-114560315689012924", "label": null}, {"source": "115404740619142087", "target": "114560315689012928", "name": "SingleNodeEquipment(nodeId=10000329008007)Switch(path=TopoPathData(ID=114560315689012928, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008019, endConnectivityNode=10000329008018, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "115404740619142087-114560315689012928", "label": null}, {"source": "115404740619142087", "target": "115404740619142088", "name": "SingleNodeEquipment(nodeId=10000329008007)SingleNodeEquipment(nodeId=10000329008006)", "des": "115404740619142087-115404740619142088", "label": null}, {"source": "114560315689013497", "target": "116530640525984710", "name": "Switch(path=TopoPathData(ID=114560315689013497, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000384008021, endConnectivityNode=10000384008022, BusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))ACLineSegment(path=LinePathData(ID=116530640525984710, type=com.nari.gz.cimbasemodule.bean.device.ACLineSegment, startConnectivityNode=10000329008011, EndConnectivityNode=10000384008059, StartST=113997365735588169, EndST=113997365735588224, name=null, StartBusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], EndBusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]), InCircle=true, MaxCurrent=0, ratedCurrent=0.0, length=0.0, r=0.0, x=0.0, bch=0.0, r0=0.0, x0=0.0, b0ch=0.0)", "des": "114560315689013497-116530640525984710", "label": null}, {"source": "115404740619142088", "target": "114560315689012922", "name": "SingleNodeEquipment(nodeId=10000329008006)Switch(path=TopoPathData(ID=114560315689012922, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008026, endConnectivityNode=10000329008025, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "115404740619142088-114560315689012922", "label": null}, {"source": "115404740619142088", "target": "114560315689012925", "name": "SingleNodeEquipment(nodeId=10000329008006)Switch(path=TopoPathData(ID=114560315689012925, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008028, endConnectivityNode=10000329008010, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "115404740619142088-114560315689012925", "label": null}, {"source": "115404740619142088", "target": "114560315689012929", "name": "SingleNodeEquipment(nodeId=10000329008006)Switch(path=TopoPathData(ID=114560315689012929, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008012, endConnectivityNode=10000329008013, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "115404740619142088-114560315689012929", "label": null}, {"source": "115404740619142088", "target": "114560315689012927", "name": "SingleNodeEquipment(nodeId=10000329008006)Switch(path=TopoPathData(ID=114560315689012927, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008024, endConnectivityNode=10000329008023, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "115404740619142088-114560315689012927", "label": null}, {"source": "116530640525984602", "target": "114560315689012927", "name": "ACLineSegment(path=LinePathData(ID=116530640525984602, type=com.nari.gz.cimbasemodule.bean.device.ACLineSegment, startConnectivityNode=10000329008022, EndConnectivityNode=10000070008017, StartST=113997365735588169, EndST=113997365735587910, name=null, StartBusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], EndBusNos=[3057, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]), InCircle=true, MaxCurrent=0, ratedCurrent=0.0, length=0.0, r=0.0, x=0.0, bch=0.0, r0=0.0, x0=0.0, b0ch=0.0)Switch(path=TopoPathData(ID=114560315689012927, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008024, endConnectivityNode=10000329008023, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "116530640525984602-114560315689012927", "label": null}, {"source": "114560315689012929", "target": "116530640525984710", "name": "Switch(path=TopoPathData(ID=114560315689012929, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008012, endConnectivityNode=10000329008013, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))ACLineSegment(path=LinePathData(ID=116530640525984710, type=com.nari.gz.cimbasemodule.bean.device.ACLineSegment, startConnectivityNode=10000329008011, EndConnectivityNode=10000384008059, StartST=113997365735588169, EndST=113997365735588224, name=null, StartBusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], EndBusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]), InCircle=true, MaxCurrent=0, ratedCurrent=0.0, length=0.0, r=0.0, x=0.0, bch=0.0, r0=0.0, x0=0.0, b0ch=0.0)", "des": "114560315689012929-116530640525984710", "label": null}, {"source": "114560315689012928", "target": "116530640525984709", "name": "Switch(path=TopoPathData(ID=114560315689012928, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008019, endConnectivityNode=10000329008018, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))ACLineSegment(path=LinePathData(ID=116530640525984709, type=com.nari.gz.cimbasemodule.bean.device.ACLineSegment, startConnectivityNode=10000329008017, EndConnectivityNode=10000384008058, StartST=113997365735588169, EndST=113997365735588224, name=null, StartBusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], EndBusNos=[3985, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]), InCircle=true, MaxCurrent=0, ratedCurrent=0.0, length=0.0, r=0.0, x=0.0, bch=0.0, r0=0.0, x0=0.0, b0ch=0.0)", "des": "114560315689012928-116530640525984709", "label": null}, {"source": "116530640525984601", "target": "114560315689012926", "name": "ACLineSegment(path=LinePathData(ID=116530640525984601, type=com.nari.gz.cimbasemodule.bean.device.ACLineSegment, startConnectivityNode=10000070008021, EndConnectivityNode=10000329008014, StartST=113997365735587910, EndST=113997365735588169, name=null, StartBusNos=[3057, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], EndBusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]), InCircle=true, MaxCurrent=0, ratedCurrent=0.0, length=0.0, r=0.0, x=0.0, bch=0.0, r0=0.0, x0=0.0, b0ch=0.0)Switch(path=TopoPathData(ID=114560315689012926, type=com.nari.gz.cimbasemodule.bean.device.Breaker, startConnectivityNode=10000329008016, endConnectivityNode=10000329008015, BusNos=[204, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], IslandNos=[null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]))", "des": "116530640525984601-114560315689012926", "label": null}, {"source": "有源Net_1", "target": "116530640525984602", "name": "有源Net_1", "des": "有源Net_1-116530640525984602", "label": null}, {"source": "有源Net_1", "target": "114560315689013496", "name": "有源Net_1", "des": "有源Net_1-114560315689013496", "label": null}, {"source": "有源Net_1", "target": "116530640525984601", "name": "有源Net_1", "des": "有源Net_1-116530640525984601", "label": null}, {"source": "有源Net_1", "target": "114560315689013497", "name": "有源Net_1", "des": "有源Net_1-114560315689013497", "label": null}], "categories": [{"name": "220kV", "size": 0}, {"name": "其他", "size": 0}, {"name": "有源主网", "size": 0}]}, "message": null}