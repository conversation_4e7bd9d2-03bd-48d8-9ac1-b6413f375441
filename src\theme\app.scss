/* 初始化样式
------------------------------- */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  color: var(--nari-c-text-1);
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: transparent;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
  border-radius: 10px;
}

.layout-scrollbar:hover::-webkit-scrollbar-thumb,
.layout-breadcrumb-setting .el-drawer__body:hover::-webkit-scrollbar-thumb,
.el-dialog__body:hover::-webkit-scrollbar-thumb {
  background-color: #163d82;
}

::-webkit-scrollbar-thumb {
  background-color: transparent;
  transition: all 0.2s ease-in-out;
  &:hover {
    background-color: var(--nari-c-primary) !important;
  }
}

/* 主布局样式
------------------------------- */
.layout-container {
  $primary: var(--nari-c-primary);
  display: flex;
  width: 100%;
  height: 100%;
  .layout-aside {
    background: var(--nari-bg-aside);
    box-shadow: 2px 0 6px rgb(0 21 41 / 1%);
    height: 100%;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    overflow-x: hidden !important;
    &.isCollapse::before {
      width: 20%;
    }
    .collapse-container {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 100%;
      height: 50px;
      font-size: 24px;
      color: var(--nari-c-primary);
      text-align: right;
      position: absolute;
      bottom: 0;
      box-shadow: 0 0 6px 0 var(--nari-c-primary);
      padding: 0 10px;
      cursor: pointer;
      z-index: 10;
    }
    .el-scrollbar__wrap {
      max-height: calc(100% - 55px);
    }
    .el-scrollbar__view {
      overflow: hidden;
    }
  }
  .layout-main {
    width: 100%;
    overflow: hidden;
  }
  .layout-header {
    padding: 0;
    z-index: 20;
  }
  .el-scrollbar {
    width: 100%;
  }
  .layout-aside-width-default {
    width: 260px !important;
    transition: width 0.3s ease;
  }
  .layout-aside-width-small {
    width: 84px !important;
    transition: width 0.3s ease;
  }
  .layout-aside-width-mini {
    width: 1px !important;
    transition: width 0.3s ease;
  }
}

/* 进度条颜色
------------------------------- */
#nprogress .bar {
  background: var(--nari-c-primary) !important;
}
