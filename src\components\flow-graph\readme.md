# Flow Graph 知识图谱/流图组件

## 简介

Flow Graph 是一个功能强大的力导向图谱可视化组件，用于展示节点和连接线之间的复杂关系。它支持多种节点类型、连接线样式，具有搜索、过滤、高亮等功能。组件可用于电力系统拓扑图、知识图谱、流程图等多种场景。

## 特性

- 基于 D3.js 的力导向图算法，节点位置自动优化
- 支持多种特殊节点类型（串供节点、失压节点、解列节点等）
- 特殊节点类型的精确交互区域处理（解决了 series_power_supply 节点的交互问题）
- 支持增量更新模式，无需重新渲染整个图谱
- 支持节点和连接线的高亮显示、交互反馈
- 内置搜索功能，支持按名称、类别等筛选节点
- 支持面板显示图例和类别统计
- 支持节点拖拽重新布局
- 支持缩放和平移操作
- 节点和连接线支持动画效果（闪烁、流动等）
- 深色/浅色主题切换，统一的主题管理系统
- 支持 categories 中自定义颜色配置，节点颜色优先使用数据中定义的颜色
- 支持延迟加载数据，手动控制渲染时机
- 完善的事件处理系统，确保所有节点类型都能正确响应交互
- 优化的悬停效果，保持连接线文字可见性
- 健壮的加载状态管理，防止界面卡死

## 属性 Props

| 属性名               | 类型           | 默认值  | 说明                                             |
| -------------------- | -------------- | ------- | ------------------------------------------------ |
| dataSource           | String         | 'mock'  | 数据源类型，可选值：'mock'、'local'              |
| data                 | Object         | null    | 本地数据源，当 dataSource 为 'local' 时使用      |
| incrementalUpdate    | Boolean        | false   | 是否启用增量更新模式，适用于数据频繁变化的场景   |
| theme                | String         | 'light' | 主题样式，可选值：'light'、'dark'                |
| showNodeLabels       | Boolean        | true    | 是否显示节点标签                                 |
| showLinkLabels       | Boolean        | true    | 是否显示连接线标签                               |
| initialHighlightNode | String/Number  | null    | 初始高亮的节点 ID 或名称                         |
| toolbar              | Boolean/String | true    | 控制工具栏，可选值：true/false/'legend'/'search' |
| initialScale         | Number         | null    | 初始缩放级别，null 表示自适应                    |
| posLayoutScale       | Number         | 1       | 位置布局缩放系数，用于调整节点间距               |
| autoLoad             | Boolean        | true    | 是否在组件挂载时自动加载数据                     |

## 事件 Events

| 事件名           | 参数 | 说明                             |
| ---------------- | ---- | -------------------------------- |
| graph-loaded     | -    | 图谱加载完成后触发               |
| graph-finished   | -    | 图谱布局计算完成后触发           |
| node-click       | node | 节点被点击时触发，参数为节点数据 |
| node-highlighted | node | 节点被高亮时触发，参数为节点数据 |

## 方法 Methods

以下方法可通过组件引用调用：

| 方法名                    | 参数                                             | 返回值  | 说明                               |
| ------------------------- | ------------------------------------------------ | ------- | ---------------------------------- |
| loadData                  | -                                                | Boolean | 手动加载数据并渲染图谱             |
| highlightNodeByIdentifier | identifier: String/Number                        | Boolean | 通过 ID 或名称高亮节点             |
| highlightLinkById         | linkId: String/Number                            | -       | 通过 ID 高亮连接线                 |
| highlightLinkByNodes      | sourceId: String/Number, targetId: String/Number | -       | 通过源节点和目标节点 ID 高亮连接线 |
| clearHighlight            | -                                                | -       | 清除所有高亮效果                   |
| clearLinkHighlight        | -                                                | -       | 清除连接线高亮效果                 |
| reloadData                | -                                                | -       | 重新加载图谱数据                   |
| setStepChange             | step: Any                                        | -       | 设置步骤变化，用于外部控制图谱显示 |
| setIconsVisibility        | visible: Boolean                                 | -       | 设置所有图标的可见性               |
| getAvailableIcons         | -                                                | Array   | 获取当前可用的所有图标名称列表     |

## 事件总线 API

组件通过事件总线提供了与外部组件通信的能力：

| 事件名            | 参数                               | 说明                                               |
| ----------------- | ---------------------------------- | -------------------------------------------------- |
| flow-graph-locate | {type, id/name/sourceId, targetId} | 定位并高亮节点或连接线，type 可选值：'node'/'link' |

## 使用示例

### 基础用法

```vue
<template>
  <div style="width: 800px; height: 600px;">
    <flow-graph />
  </div>
</template>

<script>
import FlowGraph from "@/components/flow-graph/index.vue";

export default {
  components: {
    FlowGraph
  }
};
</script>
```

### 自定义配置

```vue
<template>
  <div style="width: 100%; height: 800px;">
    <flow-graph
      data-source="local"
      theme="dark"
      :data="graphData"
      :show-node-labels="true"
      :show-link-labels="true"
      initial-highlight-node="node1"
      :initial-scale="1.5"
      toolbar="legend"
      @node-click="handleNodeClick"
      @graph-loaded="handleGraphLoaded"
      ref="flowGraph"
    />
  </div>
</template>

<script>
import FlowGraph from "@/components/flow-graph/index.vue";
import sampleData from "./sample-data.json";
import bus from "@/utils/bus";

export default {
  components: {
    FlowGraph
  },
  data() {
    return {
      graphData: sampleData
    };
  },
  methods: {
    handleNodeClick(node) {
      console.log("节点被点击:", node);
    },
    handleGraphLoaded() {
      console.log("图谱加载完成");
    },
    locateNode(nodeId) {
      // 方法1：直接调用组件方法
      this.$refs.flowGraph.highlightNodeByIdentifier(nodeId);

      // 方法2：通过事件总线
      bus.$emit("flow-graph-locate", {
        type: "node",
        id: nodeId
      });
    }
  }
};
</script>
```

### 增量更新示例

```vue
<template>
  <div style="width: 100%; height: 800px;">
    <flow-graph
      :incremental-update="true"
      :data="graphData"
      @graph-finished="handleGraphFinished"
      ref="flowGraph"
    />
    <button @click="updateData">更新数据</button>
  </div>
</template>

<script>
import FlowGraph from "@/components/flow-graph/index.vue";

export default {
  components: {
    FlowGraph
  },
  data() {
    return {
      graphData: {
        nodes: [
          /* 初始节点数据 */
        ],
        links: [
          /* 初始连接线数据 */
        ],
        categories: [
          /* 类别数据 */
        ]
      }
    };
  },
  methods: {
    updateData() {
      // 修改数据，组件会自动进行增量更新
      this.graphData = {
        nodes: [
          /* 更新后的节点数据 */
        ],
        links: [
          /* 更新后的连接线数据 */
        ],
        categories: [
          /* 类别数据 */
        ]
      };
    },
    handleGraphFinished() {
      console.log("图谱布局完成");
    }
  }
};
</script>
```

### 延迟加载示例

```vue
<template>
  <div style="width: 100%; height: 800px;">
    <flow-graph
      :auto-load="false"
      :data="graphData"
      ref="flowGraph"
    />
    <button @click="loadGraphData">加载图谱</button>
  </div>
</template>

<script>
import FlowGraph from "@/components/flow-graph/index.vue";
import sampleData from "./sample-data.json";

export default {
  components: {
    FlowGraph
  },
  data() {
    return {
      graphData: sampleData
    };
  },
  methods: {
    loadGraphData() {
      // 手动触发数据加载
      this.$refs.flowGraph.loadData();
    }
  }
};
</script>
```

## 数据格式

### 完整数据格式示例

```json
{
  "nodes": [
    {
      "id": "1",
      "name": "节点1",
      "des": "节点1描述",
      "category": 0,
      "symbolSize": 20,
      "state": 1,
      "attribute": {
        "nodestyle": "blink",
        "stationstyle": "series_power_supply",
        "nodetype": "left-half",
        "blink_color": "#ff0000"
      },
      "attrs": [{ "name": "电压", "value": "500kV" }]
    }
  ],
  "links": [
    {
      "id": "link1",
      "source": "1",
      "target": "2",
      "name": "连接线1",
      "attribute": {
        "linestyle": "dash-flow-forward",
        "measurevalue": "100MW"
      }
    }
  ],
  "categories": [
    { "name": "500kV", "color": "#FFA500" },
    { "name": "220kV", "color": "#800080" },
    { "name": "110kV", "color": "#0000FF" },
    { "name": "35kV", "color": "#008000" },
    { "name": "其他", "color": "#808080" }
  ]
}
```

### 特殊节点类型

#### 串供节点 (series_power_supply)

```json
{
  "id": "node1",
  "name": "串供节点",
  "attribute": {
    "stationstyle": "series_power_supply",
    "nodetype": "left-half" // 可选值：'left-half', 'right-half'
  }
}
```

#### 失压节点

```json
{
  "id": "node2",
  "name": "全站失压节点",
  "attribute": {
    "stationstyle": "loss_pressure"
  }
}
```

#### 非全站失压节点

```json
{
  "id": "node3",
  "name": "非全站失压节点",
  "attribute": {
    "stationstyle": "loss_pressure_half"
  }
}
```

#### 解列运行节点

```json
{
  "id": "node4",
  "name": "解列运行节点",
  "attribute": {
    "stationstyle": "disconnection"
  }
}
```

#### 闪烁节点

```json
{
  "id": "node5",
  "name": "闪烁节点",
  "attribute": {
    "nodestyle": "blink",
    "blink_color": "#ff0000"
  }
}
```

## 主题系统

### 主题配置

组件内置了完整的主题管理系统，支持亮色和暗色两种主题：

```javascript
// 主题切换示例
<flow-graph theme="dark" />
```

### 自定义主题

可以通过修改 `ThemeManager.js` 来自定义主题颜色：

```javascript
// 类别颜色配置
category: {
  0: "#1f77b4", // 500kV - 蓝色
  1: "#ff7f0e", // 220kV - 橙色
  2: "#2ca02c", // 110kV - 绿色
  3: "#d62728", // 35kV - 红色
  4: "#9467bd", // 其他 - 紫色
  default: "#999999", // 默认灰色
  loss_pressure: "#808080" // 全站失压 - 灰色
}
```

## 交互优化

### 悬停效果优化

- ✅ 悬停节点时，相关连接线高亮显示，其他连接线半透明但仍可见
- ✅ 不再完全隐藏非相关连接线的文字标签
- ✅ 保持良好的视觉层次和信息可读性

### 搜索界面优化

- ✅ 统一了连接线搜索结果的显示样式
- ✅ 移除了不必要的连接线类型判断逻辑
- ✅ 简化了搜索结果的视觉设计

## 已知问题和解决方案

### loading状态管理优化

**问题描述**：在某些异常情况下，组件的loading状态可能不会正确清除，导致界面一直显示加载中。

**解决方案**：

1. 添加了全面的错误处理机制，确保在任何异常情况下都能清除loading状态
2. 增加了30秒超时保护机制，防止loading状态永远不消失
3. 优化了模拟停止时的状态清理逻辑

**改进效果**：

- ✅ 即使在tick方法出错时，loading状态也会被正确清除
- ✅ 增量更新完成后loading状态会被清除  
- ✅ 30秒超时保护确保loading状态不会永远停留
- ✅ 更健壮的错误处理机制

### series_power_supply 节点交互问题

**问题描述**：在之前的版本中，`series_power_supply` 类型的 `left-half` 节点无法正确触发 hover 和拖动事件。

**解决方案**：

1. 为特殊节点类型添加了精确的交互背景区域
2. 在增量更新模式下，确保重新设置事件处理
3. 优化了节点渲染器中的事件绑定逻辑

**使用建议**：

- 确保使用最新版本的组件
- 在增量更新模式下，组件会自动处理事件重新绑定

### 主题一致性问题

**问题描述**：之前IconRenderer缺少setTheme方法，导致主题切换时图标渲染器无法正确响应。

**解决方案**：

1. 为IconRenderer添加了完整的setTheme方法实现
2. 统一了所有渲染器的主题接口
3. 确保主题切换时所有组件都能正确更新

## 自定义扩展

### 添加新的节点类型

1. 在 `NodeRenderer.js` 中的 `drawNodeShape` 方法中添加新的节点类型处理
2. 在 `addInteractionBackground` 方法中为新节点类型添加相应的交互背景
3. 更新相关的高亮和样式恢复方法

### 添加新的连接线样式

在组件的 `theme` 文件夹中添加新的样式定义，并在 `theme/animation.scss` 中添加相应的动画效果。

### 扩展主题系统

1. 在 `ThemeManager.js` 中添加新的主题配置
2. 更新 `graphUtils.js` 中的颜色获取逻辑
3. 确保所有渲染器都能正确响应新主题

## 性能优化

对于大型图谱（超过 500 个节点），建议：

1. 启用 `incrementalUpdate` 模式进行数据更新
2. 设置 `initialHighlightNode` 来聚焦特定区域
3. 使用 `toolbar="search"` 先通过搜索缩小范围
4. 考虑分级加载策略，先加载主要节点，再按需加载详细连接
5. 使用 `autoLoad="false"` 配合 `loadData()` 方法，在合适的时机手动加载数据
6. 适当调整 `posLayoutScale` 来优化节点间距

## 兼容性

- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 不支持 IE11 及以下版本
- 推荐在桌面环境使用，移动设备上可能需要额外的交互适配

## 依赖

- D3.js v7+
- Vue 2.x

## 更新日志

### v2.2 (当前版本)

- ✅ 新增 categories 颜色配置支持，节点颜色优先使用数据中定义的颜色
- ✅ 优化了颜色管理系统，支持三级颜色优先级（categories > ThemeManager > 默认）
- ✅ 改进了图例显示，自动显示 categories 中定义的自定义颜色
- ✅ 增强了数据处理器的颜色处理逻辑，确保颜色配置的向下兼容性

### v2.1

- ✅ 修复了loading状态偶发性不消失的问题
- ✅ 添加了30秒超时保护机制，防止界面卡死
- ✅ 优化了悬停效果，连接线文字不再完全隐藏，改为半透明显示
- ✅ 完善了主题管理系统，所有渲染器都支持主题切换
- ✅ 为IconRenderer添加了setTheme方法，确保主题一致性
- ✅ 简化了搜索结果显示逻辑，移除了不必要的连接线类型判断
- ✅ 改进了错误处理机制，增强了组件稳定性
- ✅ 统一了颜色管理，支持从ThemeManager读取主题颜色

### v2.0

- ✅ 修复了 `series_power_supply` 节点的交互问题
- ✅ 添加了增量更新模式支持
- ✅ 优化了事件处理系统
- ✅ 添加了精确的交互背景区域
- ✅ 改进了特殊节点类型的渲染逻辑

### v1.x

- 基础图谱渲染功能
- 节点和连接线的基本交互
- 搜索和过滤功能

### Categories 颜色配置

从 v2.2 版本开始，组件支持在 `categories` 中定义自定义颜色。当提供了 `color` 属性时，节点将优先使用这些颜色进行填充，而不是使用主题管理器中的默认颜色。

**颜色优先级规则：**
1. **最高优先级**：`categories` 中定义的 `color` 属性
2. **中等优先级**：`ThemeManager` 中定义的主题颜色  
3. **最低优先级**：系统默认颜色

**使用示例：**

```json
{
  "categories": [
    {
      "id": 0,
      "name": "500kV", 
      "color": "#FF6B35"  // 橙红色
    },
    {
      "id": 1,
      "name": "220kV",
      "color": "#004E89"  // 深蓝色
    },
    {
      "name": "110kV"     // 未指定颜色，使用主题默认颜色
    }
  ]
}
```
