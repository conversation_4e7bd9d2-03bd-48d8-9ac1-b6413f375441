import services from "@/services";
const { http } = services;

const graph_prefix = "/neo4j";

export const GraphicApi = {
  // 获取站图谱
  getStationNeo4j: (params) => http.get(graph_prefix + `/getStationNeo4j`, params),
  // 获取站内设备图谱
  getEquipmentNeo4j: (params) => http.get(graph_prefix + `/getEquipmentNeo4j`, params),
  // 查询设备间隔图谱
  getEquipmentNeo4jBay: (params) => http.get(graph_prefix + `/getEquipmentNeo4jBay`, params),
  // 查询某个厂站与之关连的厂站图谱
  getStationNeo4jByFacId: (params) => http.get(graph_prefix + `/getStationNeo4jByFacId`, params),
  // 查询两个厂站连接图谱
  getStationNeo4jConnected: (params) => http.get(graph_prefix + `/getStationNeo4jConnected`, params)
};
