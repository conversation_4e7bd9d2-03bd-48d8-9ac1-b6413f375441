<template>
  <div class="home-container">
    <div class="left-panel">
      <transition name="slide-left">
        <nari-card
          title="潮流图"
          v-if="isDefult"
        >
          <flow-graph
            ref="flowGraph"
            style="width: 100%; height: 100%"
            :theme="'dark'"
            :toolbar="false"
            :data="graphData_500"
            :autoLoad="true"
            :posLayoutScale="2.5"
            data-source="local"
          ></flow-graph>
        </nari-card>
      </transition>
      <transition name="slide-left">
        <template v-if="!isDefult">
          <div class="top-section panel">
            <nari-card title="">
              <div class="steps-container">
                <el-radio-group
                  v-model="activeStep"
                  size="mini"
                  :disabled="isDisabled"
                  @input="handleStepChange"
                >
                  <el-radio-button label="0">事前</el-radio-button>
                  <el-radio-button label="1">事后</el-radio-button>
                  <el-radio-button label="2">风险分析</el-radio-button>
                  <el-radio-button label="3">处置策略</el-radio-button>
                </el-radio-group>
              </div>
              <nari-tabs
                :tabList="tabList"
                :activeTab.sync="activeTab"
                height="30px"
                theme="dark"
              >
                <template #zhikan-content>
                  <div class="tab-content">
                    <zhikan-map v-if="activeTab === 'zhikan'"></zhikan-map>
                  </div>
                </template>
                <template #station-svg-content>
                  <div
                    class="tab-content flex justify-between gap-3"
                    style="width: 100%; height: 100%"
                  >
                    <flow-graph
                      ref="flowGraph"
                      style="width: 100%; height: 100%"
                      :theme="'dark'"
                      :toolbar="false"
                      :autoLoad="false"
                      :incrementalUpdate="true"
                      data-source="local"
                      :data="graphData"
                      v-if="activeTab === 'station-svg'"
                    ></flow-graph>
                  </div>
                </template>
              </nari-tabs>
            </nari-card>
          </div>
        </template>
      </transition>
      <transition name="slide-left">
        <template v-if="!isDefult">
          <div class="bottom-section panel">
            <nari-card title="故障分析/故障处置">
              <fault-analysis :key="initFaultKey"></fault-analysis>
            </nari-card>
          </div>
        </template>
      </transition>
    </div>
    <div class="right-panel panel">
      <nari-card title="智能对话">
        <Chat
          v-if="showChat"
          ref="chatRef"
          :appId="appId"
          @start-ask="isDefult = false"
          @init-graph="initGraph"
          isAvatar
          :modelType="modelType"
        ></Chat>
      </nari-card>
    </div>
  </div>
</template>

<script>
import FaultAnalysis from "@/views/fault-analysis";
import ZhikanMap from "@/components/zhikan-map";
import FlowGraph from "@/components/flow-graph";
import Chat from "@/components/chat";
import { FlowApi } from "@/api/flow";
import graphData_500kv from "@/mock/500kv_station.json";
const flowGraphMap = {
  0: "beforeGraphData",
  1: "afterGraphData",
  2: "riskGraphData",
  3: "disposeGraphData"
};
export default {
  name: "HomePage",
  components: {
    FaultAnalysis,
    FlowGraph,
    ZhikanMap,
    Chat
  },
  data() {
    return {
      isDefult: true,
      activeStep: "0",
      isDisabled: true,
      activeTab: "station-svg",
      tabList: [
        {
          id: "station-svg",
          label: "潮流图",
          name: "station-svg",
          slotName: "station-svg-content"
        }
        // {
        //   id: "zhikan",
        //   label: "智瞰",
        //   name: "zhikan",
        //   slotName: "zhikan-content"
        // }
      ],
      appId: "",
      showChat: false,
      graphData: {
        code: 200,
        msg: "请求成功",
        data: {},
        links: {},
        categories: {}
      },
      equIds: [],
      stationIds: [],
      isContinue: true,
      graphData_500: graphData_500kv,
      beforeGraphData: {},
      afterGraphData: {},
      riskGraphData: {},
      disposeGraphData: {},
      payload: {
        equIds: [],
        stationIds: [],
        type: "",
        step: ""
      },
      isSuggestion: false,
      initFaultKey: "-1",
      suggestGraph: {},
      demoId: "-1",
      modelType: true,
      steps: []
    };
  },
  created() {
    let { demoStatus } = this.$route.query;
    demoStatus = demoStatus && JSON.parse(demoStatus);
    if (demoStatus === undefined || demoStatus === "") {
      const localDemoStatus = localStorage.getItem("demoStatus");
      demoStatus = localDemoStatus ? JSON.parse(localDemoStatus) : true;
    }
    console.log("demoStatus: ", demoStatus);
    this.modelType = demoStatus;
    this.getAppId();
  },
  mounted() {
    this.$bus.$on("flow-graph-load-data", async (payload) => {
      console.log("payload: ", payload);
      this.payload = payload;
      this.isSuggestion = false;
      if (payload.type !== "-1") {
        this.activeStep = payload.type;
        await this.fetchFactoryAreaFlow();
        // if (payload.type === "3") {
        //   this.isDisabled = false;
        // }
      }
      this.isDisabled = false;
    });
    this.$bus.$on("flow-graph-suggest-data", async (payload) => {
      console.log("payload: ", payload);
      this.isSuggestion = true;
      const suggestId = payload.suggestId;
      const index = payload.index;
      const key = `${suggestId}_${index}`;
      if (key in this.suggestGraph) {
        this.graphData = this.suggestGraph[key];
      } else {
        this.fetchSuggestSvg(suggestId, key);
      }
    });
  },
  beforeDestroy() {
    this.$bus.$off("flow-graph-load-data");
    this.$bus.$off("flow-graph-suggest-data");
  },
  methods: {
    initGraph() {
      console.log("图谱进行初始化！");
      this.steps = [];
      this.$nextTick(() => {
        this.activeStep = "0";
        this.isDisabled = true;
        this.$refs.flowGraph.clearGraph();
        this.initFaultKey = `${Math.random()}`;
      });
    },
    handleStepChange(value) {
      console.log("handleStepChange: ", flowGraphMap[value], value);
      this.graphData = this[flowGraphMap[value]];
    },
    async fetchSuggestSvg(suggestionId, key) {
      this.$refs.flowGraph.clearGraph();
      const { equIds, stationIds, type, demoId, accidentInfoId = "" } = this.payload;
      const params = {
        suggestionId,
        overhaulEquips: equIds,
        type,
        lostPowerStation: stationIds,
        demoId,
        accidentInfoId,
        modelType: this.modelType
      };
      const res = await FlowApi.fetchSuggestSvg(params);
      this.$set(this.suggestGraph, key, res);
      this.graphData = res;
    },
    async fetchFactoryAreaFlow() {
      const { equIds, stationIds, type, demoId, accidentInfoId = "" } = this.payload;
      const params = {
        overhaulEquips: equIds,
        type,
        lostPowerStation: stationIds,
        demoId,
        accidentInfoId,
        modelType: this.modelType
      };
      const res = await FlowApi.fetchFactoryAreaFlow(params);
      const key = flowGraphMap[type];
      if (key) {
        this[key] = res;
      }
      this.graphData = res;
    },
    async getAppId() {
      const params = {
        difyWorkFlowName: "在线事故预案"
      };
      try {
        const res = await FlowApi.fetchWorkFlowId(params);
        if (res.code === 200) {
          this.appId = res.data;
        }
      } finally {
        this.showChat = true;
        // this.$nextTick(() => {
        //   this.$refs.chatRef.fetchChatList();
        // });
      }
    }
  }
};
</script>
<style lang="scss">
.home-container {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: transparent;

  .left-panel {
    width: 60%;
    height: 100%;
    padding: 10px;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    box-sizing: border-box;

    .top-section {
      width: 100%;
      height: 65%;
      padding-bottom: 10px;
      box-sizing: border-box;
      background-color: transparent;
      .nari-card__header {
        display: none;
      }
      .nari-tabs {
        padding: 0;
        .el-tabs--border-card .el-tabs__header {
          margin-top: 0;
        }
      }
    }

    .bottom-section {
      width: 100%;
      height: 35%;
      background-color: transparent;
    }
  }

  .right-panel {
    width: 40%;
    height: 100%;
    padding: 10px 10px 10px 0;
    background-color: transparent;
    box-sizing: border-box;
  }
  .panel {
    .nari-card {
      height: 100%;
    }
  }
}
.steps-container {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 10;
  .is-disabled .el-radio-button__inner,
  .el-radio-button__inner {
    background: transparent;
    border-color: #334c7a;
  }
  .is-disabled.is-active .el-radio-button__inner {
    background: #104275;
  }
  .el-radio-button:first-child .el-radio-button__inner {
    border-left-color: #334c7a;
  }
}
</style>
