<template>
  <div class="flex flex-1 items-center text-white text-sm space-x-2">
    <i
      class="text-base"
      :class="[
        {
          'text-orange-400 el-icon-warning': success === 0,
          'text-green-500 el-icon-success': success === 1 || success === undefined
        }
      ]"
    ></i>
    <div class="title font-bold">{{ title }}</div>
    <div
      class="tip pl-2 hidden"
      :class="{ 'text-red-500': success === 0 }"
    >
      {{ tips }}
    </div>
  </div>
</template>

<script>
export default {
  name: "TitleBar",
  props: {
    title: {
      type: String,
      required: true
    },
    tips: {
      type: String,
      default: ""
    },
    success: {
      type: Number,
      validator: (value) => [0, 1, undefined].includes(value)
    }
  }
};
</script>
