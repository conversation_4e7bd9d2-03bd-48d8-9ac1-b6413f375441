import * as graphUtils from "../utils/graphUtils.js";
import { ThemeManager } from "./ThemeManager.js";

/**
 * 数据处理器类
 * 负责图谱数据的预处理、验证和转换
 */
export class DataProcessor {
  constructor() {
    this.width = 800;
    this.height = 600;
    this.nodeMap = new Map();
    this.categoryNames = null;
    this.colorMapForUtils = null;
    this.categoryColorScale = null;
    this.themeManager = new ThemeManager();
  }

  /**
   * 设置画布尺寸
   * @param {number} width
   * @param {number} height
   */
  setDimensions(width, height) {
    this.width = width;
    this.height = height;
  }

  /**
   * 设置主题
   * @param {string} theme 主题名称 ('light' | 'dark')
   */
  setTheme(theme) {
    this.themeManager.setTheme(theme);
  }

  /**
   * 处理原始图谱数据
   * @param {Object} graphData 原始图谱数据
   * @param {number} posLayoutScale 位置布局缩放系数
   * @param {boolean} isIncrementalUpdate 是否为增量更新
   * @param {Array} existingNodes 现有节点数组（增量更新时使用）
   * @returns {Object} 处理后的数据 {nodes, links, nodeMap}
   */
  processGraphData(graphData, posLayoutScale = 1, isIncrementalUpdate = false, existingNodes = []) {
    if (!graphData) {
      console.error("无图谱数据可用");
      return { nodes: [], links: [], nodeMap: new Map() };
    }

    try {
      // 使用现有工具函数处理原始数据
      const { nodes, links, nodeMap } = graphUtils.processRawGraphData(graphData, this.width, this.height);

      this.nodeMap = nodeMap;

      // 处理节点位置
      this.processNodePositions(nodes, posLayoutScale, isIncrementalUpdate, existingNodes);

      // 计算节点层级
      this.calculateNodeLevels(nodes, links);

      // 创建颜色比例尺
      this.createColorScale(graphData);

      return { nodes, links, nodeMap };
    } catch (error) {
      console.error("数据处理错误:", error);
      return { nodes: [], links: [], nodeMap: new Map() };
    }
  }

  /**
   * 处理节点位置
   * @param {Array} nodes 节点数组
   * @param {number} posLayoutScale 位置缩放系数
   * @param {boolean} isIncrementalUpdate 是否为增量更新
   * @param {Array} existingNodes 现有节点数组（增量更新时使用）
   */
  processNodePositions(nodes, posLayoutScale, isIncrementalUpdate = false, existingNodes = []) {
    // 在增量更新时，创建现有节点的位置映射
    const existingNodePositions = new Map();
    if (isIncrementalUpdate && existingNodes.length > 0) {
      existingNodes.forEach((existingNode) => {
        if (existingNode.id && Number.isFinite(existingNode.x) && Number.isFinite(existingNode.y)) {
          existingNodePositions.set(existingNode.id, {
            x: existingNode.x,
            y: existingNode.y,
            fx: existingNode.fx,
            fy: existingNode.fy,
            _x: existingNode._x,
            _y: existingNode._y
          });
        }
      });
    }

    nodes.forEach((node) => {
      if (node.pos && Array.isArray(node.pos) && node.pos.length === 2) {
        // 验证pos数据的有效性
        if (!Number.isFinite(node.pos[0]) || !Number.isFinite(node.pos[1]) || isNaN(node.pos[0]) || isNaN(node.pos[1])) {
          console.warn(`节点 ${node.id || node.name} 的pos数据无效:`, node.pos);
          this.handleNodeWithoutValidPos(node, isIncrementalUpdate, existingNodePositions);
          return;
        }

        let x, y;

        // 自动检测坐标类型并处理
        if (this.isNormalizedCoordinates(node.pos)) {
          // 归一化坐标处理 (范围通常在-1到1之间)
          const scaledPosX = node.pos[0] * posLayoutScale;
          const scaledPosY = node.pos[1] * posLayoutScale;

          // pos: [-1, -1] -> 左上角 (0,0)
          // pos: [0,0]   -> 中心 (width/2, height/2)
          // pos: [1,1]   -> 右下角 (width, height)
          x = ((scaledPosX + 1) / 2) * this.width;
          y = ((scaledPosY + 1) / 2) * this.height;
        } else {
          // 像素坐标处理 (较大的数值，直接作为像素坐标使用)
          x = node.pos[0] * posLayoutScale;
          y = node.pos[1] * posLayoutScale;
        }

        // 验证计算结果
        if (!Number.isFinite(x) || !Number.isFinite(y) || isNaN(x) || isNaN(y)) {
          console.warn(`节点 ${node.id || node.name} pos坐标计算结果无效`);
          this.handleNodeWithoutValidPos(node, isIncrementalUpdate, existingNodePositions);
        } else {
          node.x = x;
          node.y = y;
        }

        // 确保坐标在合理范围内
        node.x = Math.max(50, Math.min(this.width - 50, node.x));
        node.y = Math.max(50, Math.min(this.height - 50, node.y));

        node.fx = node.x; // 固定节点X轴位置
        node.fy = node.y; // 固定节点Y轴位置

        // 保存备份坐标
        node._x = node.x;
        node._y = node.y;
      } else {
        this.handleNodeWithoutValidPos(node, isIncrementalUpdate, existingNodePositions);
      }
    });
  }

  /**
   * 处理没有有效pos坐标的节点
   * @param {Object} node 节点对象
   * @param {boolean} isIncrementalUpdate 是否为增量更新
   * @param {Map} existingNodePositions 现有节点位置映射
   */
  handleNodeWithoutValidPos(node, isIncrementalUpdate, existingNodePositions) {
    if (isIncrementalUpdate && existingNodePositions.has(node.id)) {
      // 增量更新时，保持已存在节点的位置
      const existingPos = existingNodePositions.get(node.id);
      node.x = existingPos.x;
      node.y = existingPos.y;
      node.fx = existingPos.fx;
      node.fy = existingPos.fy;
      node._x = existingPos._x;
      node._y = existingPos._y;
    } else {
      // 新节点或非增量更新时，设置随机位置
      this.setRandomPosition(node);
    }
  }

  /**
   * 判断坐标是否为归一化坐标
   * @param {Array} pos 位置坐标 [x, y]
   * @returns {boolean} 是否为归一化坐标
   */
  isNormalizedCoordinates(pos) {
    const [x, y] = pos;
    // 如果x和y都在-2到2的范围内，认为是归一化坐标
    // 这个范围稍微放宽一些，以兼容可能的缩放情况
    return Math.abs(x) <= 2 && Math.abs(y) <= 2;
  }

  /**
   * 设置随机位置
   * @param {Object} node 节点对象
   */
  setRandomPosition(node) {
    if (!Number.isFinite(node.x) || node.x === 0) {
      node.x = this.width * 0.2 + Math.random() * this.width * 0.6;
    }
    if (!Number.isFinite(node.y) || node.y === 0) {
      node.y = this.height * 0.2 + Math.random() * this.height * 0.6;
    }

    // 确保坐标在合理范围内
    node.x = Math.max(50, Math.min(this.width - 50, node.x));
    node.y = Math.max(50, Math.min(this.height - 50, node.y));

    // 保存备份坐标
    node._x = node.x;
    node._y = node.y;
  }

  /**
   * 计算节点层级（用于径向布局）
   * @param {Array} nodes 节点数组
   * @param {Array} links 连接线数组
   * @returns {number} 最大层级
   */
  calculateNodeLevels(nodes, links) {
    if (nodes.length === 0) return 0;

    let rootNode = null;
    let maxLevel = 0;

    // 1. 确定根节点 - 查找度数最高的节点
    if (nodes.length > 0) {
      nodes.forEach((n) => {
        n._degree = 0;
      });

      links.forEach((l) => {
        const source = this.nodeMap.get(String(typeof l.source === "object" ? l.source.id : l.source));
        const target = this.nodeMap.get(String(typeof l.target === "object" ? l.target.id : l.target));
        if (source) source._degree = (source._degree || 0) + 1;
        if (target) target._degree = (target._degree || 0) + 1;
      });

      rootNode = nodes.reduce((a, b) => ((a._degree || 0) > (b._degree || 0) ? a : b), nodes[0]);
    }

    // 如果仍然没有根节点，默认使用第一个节点
    if (!rootNode && nodes.length > 0) {
      rootNode = nodes[0];
    }

    // 2. 初始化层级和访问状态
    nodes.forEach((node) => {
      node.level = Infinity;
      node._visited = false;
    });

    if (rootNode) {
      const queue = [];
      rootNode.level = 0;
      rootNode._visited = true;
      queue.push(rootNode);
      let head = 0;

      while (head < queue.length) {
        const u = queue[head++];
        if (u.level > maxLevel) {
          maxLevel = u.level;
        }

        links.forEach((link) => {
          const sourceNode = this.nodeMap.get(String(typeof link.source === "object" ? link.source.id : link.source));
          const targetNode = this.nodeMap.get(String(typeof link.target === "object" ? link.target.id : link.target));

          if (!sourceNode || !targetNode) return;

          let v_node_obj = null;
          if (sourceNode.id === u.id && targetNode && !targetNode._visited) {
            v_node_obj = targetNode;
          } else if (targetNode.id === u.id && sourceNode && !sourceNode._visited) {
            v_node_obj = sourceNode;
          }

          if (v_node_obj) {
            v_node_obj._visited = true;
            v_node_obj.level = u.level + 1;
            queue.push(v_node_obj);
          }
        });
      }
    }

    // 如果只有一个层级0，将maxLevel设为1以便分隔计算
    if (maxLevel === 0 && nodes.some((n) => n.level === 0)) {
      maxLevel = 1;
    }

    // 清理临时属性
    nodes.forEach((node) => {
      delete node._visited;
      if (Object.prototype.hasOwnProperty.call(node, "_degree")) {
        delete node._degree;
      }
    });

    return maxLevel;
  }

  /**
   * 创建颜色比例尺
   * @param {Object} graphData 图谱数据
   */
  createColorScale(graphData) {
    const { categoryColorScale, categoryNames, colorMap } = graphUtils.createCategoryColorScaleAndNames(graphData);

    this.categoryColorScale = categoryColorScale;
    this.categoryNames = categoryNames;
    this.colorMapForUtils = colorMap;

    // console.log("颜色映射已创建:", colorMap);
  }

  /**
   * 统计类别信息
   * @param {Array} nodes 节点数组
   * @returns {Object} 类别统计信息
   */
  countCategoryStats(nodes) {
    // 从工具函数中获取每个类别的节点数量
    const categoryCount = graphUtils.countCategoryNodeStats(nodes, this.categoryNames);

    // 将统计结果转换为图例组件需要的格式
    const categoryStats = {};

    // 处理常规类别（根据categoryNames）
    if (this.categoryNames) {
      Object.entries(this.categoryNames).forEach(([categoryId, categoryName]) => {
        // 只有当类别有节点且数量大于0时才添加到图例统计中
        if (categoryCount[categoryName] !== undefined && categoryCount[categoryName] > 0) {
          categoryStats[categoryId] = {
            name: categoryName,
            count: categoryCount[categoryName]
          };

          // 如果colorMapForUtils中有对应的颜色，添加到categoryStats中
          if (this.colorMapForUtils && this.colorMapForUtils[categoryId] !== undefined) {
            categoryStats[categoryId].color = this.colorMapForUtils[categoryId];
          }
        }
      });
    }

    // 添加特殊状态统计
    const lossPressureCount = nodes.filter((node) => node.attribute && node.attribute.stationstyle === "loss_pressure").length;

    const lossPressureHalfCount = nodes.filter((node) => node.attribute && node.attribute.stationstyle === "loss_pressure_half").length;

    const disconnectionCount = nodes.filter((node) => node.attribute && node.attribute.stationstyle === "disconnection").length;

    // 获取特殊状态的颜色
    const lossPressureColor = this.themeManager.getCategoryColor("loss_pressure");
    const lossPressureHalfColor = this.themeManager.getCategoryColor("loss_pressure_half");

    // 如果有这些特殊状态的节点，添加到图例统计中
    if (lossPressureCount > 0) {
      categoryStats["loss_pressure"] = {
        name: "全站失压",
        count: lossPressureCount,
        color: lossPressureColor
      };
    }

    if (lossPressureHalfCount > 0) {
      categoryStats["loss_pressure_half"] = {
        name: "非全站失压",
        count: lossPressureHalfCount,
        color: lossPressureHalfColor
      };
    }

    if (disconnectionCount > 0) {
      categoryStats["disconnection"] = {
        name: "解列运行",
        count: disconnectionCount
      };
    }

    // console.log("图例统计数据:", categoryStats);
    return categoryStats;
  }

  /**
   * 获取类别颜色
   * @param {*} categoryInput 类别输入
   * @returns {string} 颜色值
   */
  getCategoryColor(categoryInput) {
    // 1. 优先使用主题管理器处理特殊类别
    if (categoryInput === "loss_pressure") {
      return this.themeManager.getCategoryColor("loss_pressure");
    }
    if (categoryInput === "loss_pressure_half") {
      // 对于loss_pressure_half，如果主题管理器没有定义，使用第一个类别颜色
      return this.colorMapForUtils ? this.colorMapForUtils[0] || this.themeManager.getCategoryColor(0) : this.themeManager.getCategoryColor(0);
    }

    // 2. 处理数字ID
    if (typeof categoryInput === "number" || (typeof categoryInput === "string" && !isNaN(parseInt(categoryInput, 10)))) {
      const numericKey = typeof categoryInput === "number" ? categoryInput : parseInt(categoryInput, 10);
      if (this.colorMapForUtils && this.colorMapForUtils[numericKey] !== undefined) {
        return this.colorMapForUtils[numericKey];
      }
      // 如果colorMapForUtils中没有，尝试从主题管理器获取
      return this.themeManager.getCategoryColor(numericKey);
    }

    // 3. 处理类别名称字符串
    if (typeof categoryInput === "string" && this.categoryNames) {
      for (const [originalId, nameVal] of Object.entries(this.categoryNames)) {
        if (nameVal === categoryInput) {
          const numericKey = parseInt(originalId, 10);
          if (!isNaN(numericKey) && this.colorMapForUtils && this.colorMapForUtils[numericKey] !== undefined) {
            return this.colorMapForUtils[numericKey];
          }
          // 如果colorMapForUtils中没有，尝试从主题管理器获取
          return this.themeManager.getCategoryColor(numericKey);
        }
      }
    }

    // 4. 处理默认值
    if (categoryInput === "default" && this.colorMapForUtils && this.colorMapForUtils["default"] !== undefined) {
      return this.colorMapForUtils["default"];
    }

    // 5. 最终默认颜色 - 使用主题管理器的默认颜色
    return this.colorMapForUtils && this.colorMapForUtils["default"] !== undefined ? this.colorMapForUtils["default"] : this.themeManager.getCategoryColor("default");
  }

  /**
   * 获取类别名称
   * @param {*} categoryId 类别ID
   * @returns {string} 类别名称
   */
  getCategoryName(categoryId) {
    // 特殊类别名称处理
    if (categoryId === "loss_pressure") {
      return "全站失压";
    }
    if (categoryId === "loss_pressure_half") {
      return "非全站失压";
    }
    if (categoryId === "disconnection") {
      return "解列运行";
    }

    // 根据类别ID找到对应的类别名称
    if (this.categoryNames && this.categoryNames[categoryId] !== undefined) {
      return this.categoryNames[categoryId];
    } else if (categoryId === "default" || categoryId === undefined || categoryId === null) {
      return "未分类";
    } else {
      return `类别${categoryId}`;
    }
  }
}
