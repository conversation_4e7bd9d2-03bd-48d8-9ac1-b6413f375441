// FlowGraphLoading 主题颜色
$flow-graph-loading-overlay-bg: rgba(240, 242, 245, 0.7);

$flow-graph-loading-container-bg: rgba(255, 255, 255, 0.9);
$flow-graph-loading-container-shadow: rgba(0, 0, 0, 0.1);
$flow-graph-loading-container-border: rgba(200, 200, 200, 0.5);

$flow-graph-loading-spinner-ring-1-color: #409eff;
$flow-graph-loading-spinner-ring-2-color: #67c23a;
$flow-graph-loading-spinner-ring-3-color: #e6a23c;
$flow-graph-loading-spinner-ring-4-color: #f56c6c;

$flow-graph-loading-text-color: #303133;
$flow-graph-loading-text-shadow: rgba(0, 0, 0, 0.05);

$flow-graph-loading-dot-bg: #606266;
$flow-graph-loading-dot-blink-shadow: rgba(0, 0, 0, 0.1);

$flow-graph-loading-error-container-bg: rgba(253, 226, 226, 0.95);
$flow-graph-loading-error-container-shadow: rgba(0, 0, 0, 0.1);

$flow-graph-loading-error-message-text-color: #f56c6c;
$flow-graph-loading-error-button-bg: #f56c6c;
$flow-graph-loading-error-button-text-color: #ffffff;
$flow-graph-loading-error-button-hover-bg: #f78989;

$flow-graph-loading-transparent: transparent;

// 遮罩层
.overlay {
  background-color: $flow-graph-loading-overlay-bg;
  animation: fadeIn 0.3s ease-out;
}

// 加载容器
.loading-container {
  background-color: $flow-graph-loading-container-bg;
  box-shadow: 0 0 30px $flow-graph-loading-container-shadow;
  border: 1px solid $flow-graph-loading-container-border;
}

.spinner-ring {
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid $flow-graph-loading-transparent;
  border-radius: 50%;

  &:nth-child(1) {
    border-top-color: $flow-graph-loading-spinner-ring-1-color;
    animation: spinnerRotate 1s linear infinite;
  }

  &:nth-child(2) {
    border-right-color: $flow-graph-loading-spinner-ring-2-color;
    animation: spinnerRotateReverse 1.2s linear infinite;
  }

  &:nth-child(3) {
    border-bottom-color: $flow-graph-loading-spinner-ring-3-color;
    animation: spinnerRotate 1.5s linear infinite;
  }

  &:nth-child(4) {
    border-left-color: $flow-graph-loading-spinner-ring-4-color;
    animation: spinnerRotateReverse 1.8s linear infinite;
  }
}

// 加载文本
.loading-text {
  color: $flow-graph-loading-text-color;
  text-shadow: 0 0 10px $flow-graph-loading-text-shadow;
}

.dot {
  width: 5px;
  height: 5px;
  margin: 0 2px;
  border-radius: 50%;
  background-color: $flow-graph-loading-dot-bg;
  display: inline-block;
  animation: dotBlink 1.4s infinite;

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

// 错误提示
.error-container {
  background-color: $flow-graph-loading-error-container-bg;
  box-shadow: 0 0 15px $flow-graph-loading-error-container-shadow;
}

.error-message {
  color: $flow-graph-loading-error-message-text-color;

  button {
    background-color: $flow-graph-loading-error-button-bg;
    color: $flow-graph-loading-error-button-text-color;

    &:hover {
      background-color: $flow-graph-loading-error-button-hover-bg;
    }
  }
}

// 动画定义
@keyframes spinnerRotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes spinnerRotateReverse {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(-360deg);
  }
}

@keyframes dotBlink {
  0%,
  100% {
    opacity: 0.2;
    transform: scale(0.8);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
    box-shadow: 0 0 10px $flow-graph-loading-dot-blink-shadow;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
