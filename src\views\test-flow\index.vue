<template>
  <div class="test-flow">
    <div class="toolbar">
      <div class="toolbar-group">
        <span class="group-title">基础控制</span>
        <el-button
          size="small"
          @click="changeTheme"
        >
          切换主题 ({{ theme }})
        </el-button>
        <el-button
          size="small"
          @click="changeIncrementalUpdate"
        >
          {{ incrementalUpdate ? "增量更新" : "完全重载" }}
        </el-button>
        <el-button
          size="small"
          @click="toggleFlowGraph"
        >
          {{ showFlowGraph ? "隐藏图谱" : "显示图谱" }}
        </el-button>
      </div>

      <div class="toolbar-group">
        <span class="group-title">手动切换</span>
        <el-button
          size="small"
          type="primary"
          @click="changeData(1)"
          :disabled="isAutoLoading"
        >
          start
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="changeData(2)"
          :disabled="isAutoLoading"
        >
          fault
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="changeData(3)"
          :disabled="isAutoLoading"
        >
          risk
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="changeData(4)"
          :disabled="isAutoLoading"
        >
          dispose
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="changeData(5)"
          :disabled="isAutoLoading"
        >
          power-supply
        </el-button>
      </div>

      <div class="toolbar-group">
        <span class="group-title">自动测试</span>
        <el-button
          size="small"
          type="success"
          @click="startAutoLoad"
          :disabled="isAutoLoading"
        >
          开始自动加载
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="stopAutoLoad"
          :disabled="!isAutoLoading"
        >
          停止自动加载
        </el-button>
        <el-select
          v-model="autoLoadInterval"
          size="small"
          style="width: 100px"
          :disabled="isAutoLoading"
        >
          <el-option
            label="1秒"
            :value="1000"
          ></el-option>
          <el-option
            label="2秒"
            :value="2000"
          ></el-option>
          <el-option
            label="3秒"
            :value="3000"
          ></el-option>
          <el-option
            label="5秒"
            :value="5000"
          ></el-option>
        </el-select>
      </div>

      <div class="toolbar-group">
        <span class="group-title">其他</span>
        <div class="upload-wrapper">
          <input
            class="file-input"
            ref="fileInput"
            type="file"
            accept=".json"
            @change="handleFileUpload"
          />
          <el-button
            size="small"
            type="primary"
            @click="$refs.fileInput.click()"
            :disabled="isAutoLoading"
          >
            导入数据
          </el-button>
        </div>
      </div>
    </div>

    <div class="status-bar">
      <div class="status-info">
        <span
          >当前数据: <strong>{{ currentDataName }}</strong></span
        >
        <span
          >切换次数: <strong>{{ switchCount }}</strong></span
        >
        <span
          v-if="isAutoLoading"
          class="auto-indicator"
        >
          🔄 自动加载中... ({{ autoLoadInterval / 1000 }}秒间隔)
        </span>
      </div>
    </div>

    <div class="flow-graph">
      <transition name="slide-left">
        <flow-graph
          v-if="showFlowGraph"
          ref="flowGraph"
          style="width: 100%; height: 100%"
          :theme="theme"
          :toolbar="true"
          :data="graphData"
          :incrementalUpdate="incrementalUpdate"
          :autoLoad="true"
          data-source="local"
          @graph-loaded="onGraphLoaded"
          @graph-finished="onGraphFinished"
        ></flow-graph>
      </transition>
    </div>
  </div>
</template>
<script>
import jsonGraphData1 from "@/mock/zh/falt_g-1-start.json";
import jsonGraphData2 from "@/mock/zh/falt_g-2-fault.json";
import jsonGraphData3 from "@/mock/zh/falt_g-3-risk.json";
import jsonGraphData4 from "@/mock/zh/falt_g-4-dispose.json";
import jsonGraphData5 from "@/mock/zh/falt_g-5-power-supply.json";
import FlowGraph from "@/components/flow-graph";
export default {
  name: "TestFlow",
  components: {
    FlowGraph
  },
  data() {
    return {
      graphData: {},
      theme: "dark",
      incrementalUpdate: true,
      showFlowGraph: false,
      // 自动加载数据状态
      isAutoLoading: false,
      autoLoadInterval: 2000,
      autoLoadTimer: null,
      currentDataIndex: 1,
      currentDataName: "start",
      switchCount: 0,
      dataNames: {
        1: "start",
        2: "fault",
        3: "risk",
        4: "dispose",
        5: "power-supply"
      }
    };
  },
  mounted() {
    this.toggleFlowGraph();
  },
  beforeDestroy() {
    // 组件销毁前停止自动加载
    this.stopAutoLoad();
  },
  methods: {
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) {
        return this.$message.warnning("请选择一个 JSON 文件");
      }
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target.result;
          this.graphData = JSON.parse(content);
        } catch (error) {
          console.error("无法解析 JSON 文件：", error);
        }
      };
      reader.readAsText(file);
      this.$refs.fileInput.value = "";
    },
    toggleFlowGraph() {
      this.showFlowGraph = !this.showFlowGraph;
      if (this.showFlowGraph) {
        this.changeData(1);
      }
    },
    changeIncrementalUpdate() {
      this.incrementalUpdate = !this.incrementalUpdate;
    },
    changeTheme() {
      if (this.theme === "dark") {
        this.theme = "light";
      } else {
        this.theme = "dark";
      }
    },
    changeData(index) {
      if (this.isAutoLoading && arguments.length === 0) {
        // 自动切换模式下，只在非手动调用时才禁用
        return;
      }

      switch (index) {
        case 1:
          this.graphData = jsonGraphData1;
          break;
        case 2:
          this.graphData = jsonGraphData2;
          break;
        case 3:
          this.graphData = jsonGraphData3;
          break;
        case 4:
          this.graphData = jsonGraphData4;
          break;
        case 5:
          this.graphData = jsonGraphData5;
          break;
        default:
          this.graphData = jsonGraphData1;
          break;
      }

      this.currentDataIndex = index;
      this.currentDataName = this.dataNames[index];
      this.switchCount++;
    },

    // 开始自动加载
    startAutoLoad() {
      if (this.isAutoLoading) return;

      this.isAutoLoading = true;
      this.switchCount = 0;
      this.currentDataIndex = 1;

      // 立即加载第一个数据
      this.changeData(1);

      // 设置定时器
      this.autoLoadTimer = setInterval(() => {
        this.currentDataIndex = this.currentDataIndex >= 5 ? 1 : this.currentDataIndex + 1;
        this.changeData(this.currentDataIndex);
      }, this.autoLoadInterval);
    },

    // 停止自动加载
    stopAutoLoad() {
      if (!this.isAutoLoading) return;

      this.isAutoLoading = false;

      if (this.autoLoadTimer) {
        clearInterval(this.autoLoadTimer);
        this.autoLoadTimer = null;
      }
    },

    // 图谱加载完成回调
    onGraphLoaded() {
      console.log("图谱加载完成");
    },

    // 图谱渲染完成回调
    onGraphFinished() {
      console.log("图谱渲染完成");
    }
  }
};
</script>
<style lang="scss" scoped>
.test-flow {
  width: 100%;
  height: 100%;

  .toolbar {
    width: 100%;
    height: auto;
    min-height: 50px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;
    padding: 10px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 5px 10px;
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.03);
      border: 1px solid rgba(255, 255, 255, 0.08);

      .group-title {
        font-size: 12px;
        color: #999;
        margin-right: 5px;
        white-space: nowrap;
      }

      .el-button {
        margin: 0;
      }

      .el-select {
        margin-left: 5px;
      }
    }

    .upload-wrapper {
      display: flex;
      align-items: center;
    }

    .file-input {
      display: none;
    }
  }

  .status-bar {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #ccc;

    .status-info {
      display: flex;
      align-items: center;
      gap: 20px;

      .auto-indicator {
        color: #67c23a;
        animation: pulse 2s infinite;
      }
    }
  }

  .flow-graph {
    width: 100%;
    height: calc(100% - 120px);
  }
}

@keyframes pulse {
  0%,
  50% {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0.6;
  }
}
</style>
