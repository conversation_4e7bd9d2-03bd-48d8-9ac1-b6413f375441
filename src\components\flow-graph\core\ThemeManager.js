/**
 * 主题管理器类
 * 负责管理图谱的主题配置，包括连接线颜色等
 */
export class ThemeManager {
  constructor() {
    this.currentTheme = "light";
    this.themes = {
      light: {
        // 节点相关颜色
        node: {
          highlight: {
            selected: "#00ff00", // 选中节点边框 - 绿色
            connected: "#00ff00" // 相连节点边框 - 绿色
          },
          // 节点填充颜色
          fill: {
            grey: "#808080", // 灰色节点（如全站失压）
            transparent: "none" // 透明填充
          },
          // 节点边框颜色
          stroke: {
            green: "#33ff33", // 绿色边框（如解列运行、非全站失压）
            transparent: "rgba(255, 255, 255, 0.3)", // 半透明白色边框（串供节点）
            default: "#000000" // 默认边框颜色
          },
          // 辉光效果颜色
          glow: {
            fill: "#000000" // 辉光遮罩颜色
          }
        },
        // 连接线相关颜色
        link: {
          default: "#2196F3", // 默认连接线颜色 - 蓝色，在白色背景下可见性好
          highlight: {
            selected: "#00ff00", // 选中连接线颜色 - 绿色
            related: "#ff9800" // 相关连接线颜色 - 橙色
          }
        },
        // 标签相关颜色
        label: {
          node: {
            fill: "#000000", // 节点标签文字颜色
            stroke: "#ffffff", // 节点标签描边颜色（用于文字阴影）
            highlight: "#00FFFF" // 节点标签高亮颜色 - 青色
          },
          link: {
            fill: "#ffffff", // 连接线标签文字颜色
            stroke: "#000000", // 连接线标签描边颜色（用于文字阴影）
            highlight: "#ffff00" // 连接线标签高亮颜色 - 黄色
          }
        },
        // 图标相关颜色
        icon: {
          arrow: "#666666", // 箭头图标颜色
          highlight: "#ffd700" // 图标高亮颜色 - 金色
        },
        // 类别颜色映射
        category: {
          0: "#1f77b4", // 500kV - 蓝色
          1: "#ff7f0e", // 220kV - 橙色
          2: "#2ca02c", // 110kV - 绿色
          3: "#d62728", // 35kV - 红色
          4: "#9467bd", // 其他 - 紫色
          default: "#999999", // 默认灰色
          loss_pressure: "#808080" // 全站失压 - 灰色
        },
        // 透明度配置 - 用于控制不同状态下图元的可见性
        opacity: {
          normal: 0.8, // 正常状态透明度 - 用于相关/高亮的连接线，保持良好可见性
          dimmed: 0.3, // 变暗状态透明度 - 用于非相关连接线，降低干扰但仍可见
          hidden: 0.1 // 隐藏状态透明度 - 用于几乎完全隐藏的元素，如选中特定连接线时的其他连接线
        }
      },
      dark: {
        // 节点相关颜色
        node: {
          highlight: {
            selected: "#ffffff", // 选中节点边框 - 白色
            connected: "#ffd700" // 相连节点边框 - 金色
          },
          // 节点填充颜色
          fill: {
            grey: "#606060", // 深色主题下的灰色节点
            transparent: "none" // 透明填充
          },
          // 节点边框颜色
          stroke: {
            green: "#66ff66", // 深色主题下的绿色边框
            transparent: "rgba(255, 255, 255, 0.2)", // 深色主题下的半透明边框
            default: "#ffffff" // 深色主题默认边框颜色
          },
          // 辉光效果颜色
          glow: {
            fill: "#ffffff" // 深色主题下的辉光遮罩颜色
          }
        },
        // 连接线相关颜色
        link: {
          default: "#9E9E9E", // 默认连接线颜色 - 灰色，在深色背景下可见性好
          highlight: {
            selected: "#66ff66", // 深色主题下选中连接线颜色 - 较亮的绿色
            related: "#ffcc02" // 深色主题下相关连接线颜色 - 较亮的黄色
          }
        },
        // 标签相关颜色
        label: {
          node: {
            fill: "#ffffff", // 深色主题下节点标签文字颜色
            stroke: "#000000", // 深色主题下节点标签描边颜色
            highlight: "#00FFFF" // 深色主题下节点标签高亮颜色
          },
          link: {
            fill: "#ffffff", // 深色主题下连接线标签文字颜色
            stroke: "#000000", // 深色主题下连接线标签描边颜色
            highlight: "#ffff00" // 深色主题下连接线标签高亮颜色
          }
        },
        // 图标相关颜色
        icon: {
          arrow: "#cccccc", // 深色主题下箭头图标颜色
          highlight: "#ffd700" // 深色主题下图标高亮颜色
        },
        // 类别颜色映射（深色主题下可能需要调整亮度）
        category: {
          0: "#3498db", // 500kV - 较亮的蓝色
          1: "#f39c12", // 220kV - 较亮的橙色
          2: "#2ecc71", // 110kV - 较亮的绿色
          3: "#e74c3c", // 35kV - 较亮的红色
          4: "#9b59b6", // 其他 - 较亮的紫色
          default: "#bdc3c7", // 默认较亮的灰色
          loss_pressure: "#95a5a6" // 全站失压 - 较亮的灰色
        },
        // 透明度配置 - 用于控制不同状态下图元的可见性
        opacity: {
          normal: 0.8, // 正常状态透明度 - 用于相关/高亮的连接线，保持良好可见性
          dimmed: 0.3, // 变暗状态透明度 - 用于非相关连接线，降低干扰但仍可见
          hidden: 0.1 // 隐藏状态透明度 - 用于几乎完全隐藏的元素，如选中特定连接线时的其他连接线
        }
      }
    };
  }

  /**
   * 设置当前主题
   * @param {string} theme 主题名称 ('light' | 'dark')
   */
  setTheme(theme) {
    if (this.themes[theme]) {
      this.currentTheme = theme;
    } else {
      console.warn(`Unknown theme: ${theme}, using default 'light' theme`);
      this.currentTheme = "light";
    }
  }

  /**
   * 获取当前主题名称
   * @returns {string} 当前主题名称
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * 获取连接线默认颜色
   * @returns {string} 连接线颜色
   */
  getLinkDefaultColor() {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.link.default;
  }

  /**
   * 获取连接线描边颜色（兼容旧方法）
   * @param {Object} link 连接线数据
   * @returns {string} 颜色值
   */
  getLinkStrokeColor(link) {
    // 优先使用连接线的 blink_color，如果没有则使用默认颜色
    if (link && link.label && link.label.blink_color) {
      return link.label.blink_color;
    }
    return this.getLinkDefaultColor();
  }

  /**
   * 获取节点高亮边框颜色
   * @param {string} type 高亮类型 ('selected' | 'connected')
   * @returns {string} 颜色值
   */
  getNodeHighlightColor(type) {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.node.highlight[type] || currentThemeConfig.node.highlight.selected;
  }

  /**
   * 获取节点填充颜色
   * @param {string} type 填充类型 ('grey' | 'transparent')
   * @returns {string} 颜色值
   */
  getNodeFillColor(type = "grey") {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.node.fill[type] || currentThemeConfig.node.fill.grey;
  }

  /**
   * 获取节点边框颜色
   * @param {string} type 边框类型 ('green' | 'transparent' | 'default')
   * @returns {string} 颜色值
   */
  getNodeStrokeColor(type = "default") {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.node.stroke[type] || currentThemeConfig.node.stroke.default;
  }

  /**
   * 获取节点辉光颜色
   * @param {string} type 辉光类型 ('fill')
   * @returns {string} 颜色值
   */
  getNodeGlowColor(type = "fill") {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.node.glow[type] || currentThemeConfig.node.glow.fill;
  }

  /**
   * 获取标签颜色
   * @param {string} labelType 标签类型 ('node' | 'link')
   * @param {string} colorType 颜色类型 ('fill' | 'stroke' | 'highlight')
   * @returns {string} 颜色值
   */
  getLabelColor(labelType, colorType = "fill") {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.label[labelType]?.[colorType] || currentThemeConfig.label.node.fill;
  }

  /**
   * 获取图标颜色
   * @param {string} type 图标类型 ('arrow' | 'highlight')
   * @returns {string} 颜色值
   */
  getIconColor(type = "arrow") {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.icon[type] || currentThemeConfig.icon.arrow;
  }

  /**
   * 获取类别颜色
   * @param {string|number} category 类别标识
   * @returns {string} 颜色值
   */
  getCategoryColor(category) {
    const currentThemeConfig = this.themes[this.currentTheme];

    // 特殊状态处理
    if (category === "loss_pressure") {
      return currentThemeConfig.category.loss_pressure;
    }

    // 数字类别处理
    if (currentThemeConfig.category[category] !== undefined) {
      return currentThemeConfig.category[category];
    }

    return currentThemeConfig.category.default;
  }

  /**
   * 获取透明度配置
   * @param {string} state 状态类型
   *   - 'normal': 正常状态透明度(0.8) - 用于高亮/相关的连接线，保持良好可见性
   *   - 'dimmed': 变暗状态透明度(0.3) - 用于非相关连接线，hover时降低干扰但仍可见
   *   - 'hidden': 隐藏状态透明度(0.1) - 用于几乎完全隐藏的元素，如选中特定连接线时让其他连接线消失
   * @returns {number} 透明度值 (0-1之间的小数)
   */
  getOpacity(state = "normal") {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.opacity[state] || currentThemeConfig.opacity.normal;
  }

  /**
   * 检查是否为浅色主题
   * @returns {boolean} 是否为浅色主题
   */
  isLightTheme() {
    return this.currentTheme === "light";
  }

  /**
   * 检查是否为深色主题
   * @returns {boolean} 是否为深色主题
   */
  isDarkTheme() {
    return this.currentTheme === "dark";
  }

  /**
   * 获取当前主题的完整配置
   * @returns {object} 当前主题配置
   */
  getCurrentThemeConfig() {
    return this.themes[this.currentTheme];
  }

  /**
   * 获取连接线高亮颜色
   * @param {string} type 高亮类型 ('selected' | 'related')
   * @returns {string} 颜色值
   */
  getLinkHighlightColor(type = "selected") {
    const currentThemeConfig = this.themes[this.currentTheme];
    return currentThemeConfig.link.highlight[type] || currentThemeConfig.link.highlight.selected;
  }
}
