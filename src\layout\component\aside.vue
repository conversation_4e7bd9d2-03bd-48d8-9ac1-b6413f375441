<template>
  <el-aside
    class="layout-aside"
    :width="getThemeConfig.isCollapse ? '64px' : '210px'"
    v-show="menuList.length"
  >
    <Logo v-if="setShowLogo" />
    <el-scrollbar class="flex-1">
      <Vertical :menuList="menuList" />
      <div class="collapse-container">
        <i
          :class="getThemeConfig.isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
          @click="toggleSidebar"
        ></i>
      </div>
    </el-scrollbar>
  </el-aside>
</template>

<script>
import Vertical from "@/layout/navMenu/vertical.vue";
import Logo from "@/layout/logo/index.vue";
import { Local } from "@/utils/storage.js";
export default {
  name: "layoutAside",
  components: { Vertical, Logo },
  data() {
    return {
      menuList: [],
      clientWidth: ""
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    // 设置左侧菜单的具体宽度
    setCollapseWidth() {
      const { layout, isCollapse } = this.getThemeConfig;
      let asideBrColor = "";
      if (layout === "vertical") {
        asideBrColor = "layout-el-aside-br-color";
      }
      // 其它布局给 64px
      if (isCollapse) {
        return ["layout-aside-width-small", asideBrColor];
      } else {
        return ["layout-aside-width-default", asideBrColor];
      }
    },
    // 设置 logo 是否显示
    setShowLogo() {
      const { layout, isShowLogo } = this.getThemeConfig;
      return isShowLogo && layout === "defaults";
    }
  },
  created() {
    this.setFilterRoutes();
    this.$bus.$on("setSendMixChildren", (res) => {
      this.menuList = res.children;
    });
  },
  methods: {
    toggleSidebar() {
      if (this.getThemeConfig.layout === "horizontal") return false;
      this.getThemeConfig.isCollapse = !this.getThemeConfig.isCollapse;
      // 存储布局配置
      Local.remove("themeConfigPrev");
      Local.set("themeConfigPrev", this.getThemeConfig);
    },
    // 设置/过滤路由（非静态路由/是否显示在菜单中）
    setFilterRoutes() {
      this.menuList = this.$store.getters["routesList/showMenuListGet"];
    }
  }
};
</script>
