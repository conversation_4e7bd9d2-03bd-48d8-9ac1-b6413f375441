/* 深色模式样式
------------------------------- */
html[data-theme="dark"] {
  // switch
  .el-switch:not(.is-checked) {
    .el-switch__core {
      border-color: var(--nari-c-border-base);
      background-color: var(--nari-c-border-base);
    }
  }

  // TimePicker
  .el-time-spinner__item.active:not(.disabled) {
    color: var(--nari-c-primary);
  }

  // loading
  .el-loading-mask {
    background-color: var(--nari-c-bg);
  }

  // dropdown
  .el-dropdown-menu__item:focus,
  .el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: var(--nari-c-hover);
  }
}
