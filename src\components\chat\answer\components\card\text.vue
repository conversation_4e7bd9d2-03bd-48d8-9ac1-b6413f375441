<template>
  <div class="text-container">
    <template v-if="isHTMLString">
      <span v-html="data.text"></span>
    </template>
    <template v-else>
      <el-checkbox
        class="checkbox"
        :checked="checked"
        disabled
        v-if="data.checkbox"
      />
      <pre>{{ data.text }}</pre>
    </template>
  </div>
</template>

<script>
import { isHTMLTag } from "@/utils/libs";
export default {
  name: "TextCard",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    isHTMLString() {
      return isHTMLTag(this.data.text);
    },
    checked() {
      return this.data.checkbox;
    }
  }
};
</script>
<style lang="scss" scoped>
pre {
  font-size: 14px;
  font-family: Arial, Helvetica, sans-serif;
  white-space: break-spaces;
}
.checkbox {
  ::v-deep .el-checkbox__input.is-disabled {
    &.is-checked .el-checkbox__inner {
      background-color: #409eff;
      border-color: #409eff;
      &::after {
        border-color: #fff;
      }
    }
  }
}
</style>
