<template>
  <div
    class="p-2 mb-2 rounded-xl border-[0.5px] border-black/8"
    :class="[collapse ? 'py-[7px]' : hideInfo ? 'pt-2 pb-1' : 'py-2', collapse ? 'bg-white' : 'bg-gray-50', hideInfo ? 'mx-[-8px] px-1' : 'w-full px-3']"
    :style="background"
  >
    <div
      class="flex items-center h-[18px] cursor-pointer"
      :class="[{ 'px-[6px]': hideInfo }]"
      @click="setCollapse"
    >
      <svg-icon
        class="shrink-0 mr-1 w-3 h-3"
        :class="[
          {
            'text-[#667085] animate-spin': data.status === 'running',
            'text-[#12B76A]': data.status === 'succeeded',
            'text-[#F04438]': data.status === 'failed',
            'text-[#F79009]': data.status === 'stopped'
          }
        ]"
        :icon-name="getIcon(data.status)"
      ></svg-icon>
      <div class="grow text-xs font-medium text-gray-700">工作流</div>
      <i
        class="el-icon-arrow-right text-gray-500 text-xs"
        :class="collapse ? '' : 'rotate-90'"
      ></i>
    </div>
    <template v-if="!collapse">
      <div class="mt-1.5">
        <div
          class="mb-1 last-of-type:mb-0"
          v-for="node in data.tracing"
          :key="node.id"
        >
          <div
            class="px-4 py-1"
            :class="[{ '!p-0': hideInfo }]"
          >
            <div
              class="group transition-all bg-white border border-gray-100 rounded-2xl shadow-xs hover:shadow-md"
              :class="[{ '!rounded-lg': hideInfo }]"
            >
              <div
                class="flex items-center pl-[6px] pr-3 cursor-pointer"
                :class="hideInfo ? 'py-2' : 'py-3'"
              >
                <div
                  class="flex items-center justify-center border-[0.5px] border-white/2 text-white w-4 h-4 rounded-[5px] shadow-xs shrink-0 mr-2"
                  :class="setIconColor(node.node_type)"
                >
                  <svg-icon :icon-name="getIcon(node.node_type)"></svg-icon>
                </div>
                <div
                  class="grow text-gray-700 text-[13px] leading-[16px] font-semibold truncate"
                  :title="node.title"
                  :class="hideInfo ? '!text-xs' : ''"
                >
                  {{ node.title }}
                </div>
                <div
                  v-if="node.status !== 'running' && !hideInfo"
                  class="shrink-0 text-gray-500 text-xs leading-[18px]"
                >
                  {{ `${getTime(node.elapsed_time || 0)} · ${getTokenCount(node.execution_metadata?.total_tokens || 0)} tokens` }}
                </div>
                <svg-icon
                  v-if="node.status !== 'running'"
                  class="shrink-0 ml-2 w-3.5 h-3.5"
                  :class="[
                    {
                      'text-[#12B76A]': node.status === 'succeeded',
                      'text-[#F04438]': node.status === 'failed',
                      'text-[#F79009]': node.status === 'stopped'
                    }
                  ]"
                  :icon-name="getIcon(node.status)"
                ></svg-icon>
                <div
                  v-if="node.status === 'running'"
                  class="shrink-0 flex items-center text-primary-600 text-[13px] leading-[16px] font-medium"
                >
                  <span class="mr-2 text-xs font-normal">Running</span>
                  <svg-icon
                    class="w-3.5 h-3.5 animate-spin"
                    icon-name="loader"
                  ></svg-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div
      v-if="data.workflowData?.status === 'stopped' || data.workflowData?.status === 'failed'"
      class="px-3 py-[10px] rounded-lg border-[0.5px] border-[rbga(0,0,0,0.05)] text-xs leading-[18px] shadow-xs"
      :class="[{ 'bg-[#fffaeb] text-[#dc6803]': data.workflowData?.status === 'stopped', 'bg-[#fef3f2] text-[#d92d20]': data.workflowData?.status === 'failed' }]"
    >
      {{ data.workflowData?.error }}
    </div>
  </div>
</template>

<script>
import { WORKFLOW_RUNNING_STATUS, ICON_CONTAINER_BG_COLOR_MAP, getIconName } from "@/utils/constants";
export default {
  name: "WorkflowProcess",
  props: {
    taskId: {
      type: String,
      default: ""
    },
    item: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    hideInfo: {
      type: Boolean,
      default: false
    },
    hideProcessDetail: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    background() {
      const running = this.data.status === WORKFLOW_RUNNING_STATUS.Running;
      const succeeded = this.data.status === WORKFLOW_RUNNING_STATUS.Succeeded;
      const failed = this.data.status === WORKFLOW_RUNNING_STATUS.Failed || this.data.status === WORKFLOW_RUNNING_STATUS.Stopped;
      let background = "";
      if (running && !this.collapse) background = "linear-gradient(180deg, #E1E4EA 0%, #EAECF0 100%)";
      if (succeeded && !this.collapse) background = "linear-gradient(180deg, #ECFDF3 0%, #F6FEF9 100%)";
      if (failed && !this.collapse) background = "linear-gradient(180deg, #FEE4E2 0%, #FEF3F2 100%)";
      return {
        background
      };
    },
    getIcon() {
      return (status) => getIconName(status) || "";
    },
    setIconColor() {
      return (status) => {
        const bg = ICON_CONTAINER_BG_COLOR_MAP[status];
        return {
          "!mr-1": this.hideInfo,
          [bg]: true
        };
      };
    },
    hasDispose() {
      if (!this.data.tracing.length) return false;
      const lastNode = this.data.tracing[this.data.tracing.length - 1];
      return lastNode.status === "running" && lastNode.node_type === "if-else";
    }
  },
  data() {
    return {
      collapse: true
    };
  },
  methods: {
    setCollapse() {
      this.collapse = !this.collapse;
    },
    getTime(time) {
      if (time < 1) return `${(time * 1000).toFixed(3)} ms`;
      if (time > 60) return `${parseInt(Math.round(time / 60).toString())} m ${(time % 60).toFixed(3)} s`;
      return `${time.toFixed(3)} s`;
    },
    getTokenCount(tokens) {
      if (tokens < 1000) return tokens;
      if (tokens >= 1000 && tokens < 1000000) return `${parseFloat((tokens / 1000).toFixed(3))}K`;
      if (tokens >= 1000000) return `${parseFloat((tokens / 1000000).toFixed(3))}M`;
    }
  }
};
</script>

<style scoped lang="scss"></style>
