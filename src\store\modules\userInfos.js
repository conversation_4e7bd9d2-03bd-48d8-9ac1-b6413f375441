const userInfosModule = {
  namespaced: true,
  state: {
    token: "",
    userInfos: {
      username: "admin",
      photo: ""
    }
  },
  mutations: {
    setToken(state, data) {
      state.token = data;
    },
    getUserInfos(state, data) {
      state.userInfos = data;
    }
  },
  actions: {
    // 设置用户信息
    async setUserInfos({ commit }, data) {
      commit("getUserInfos", data);
    }
  }
};

export default userInfosModule;
