import services from "@/services";
const { http } = services;

const { BASE_URL_SPEECH, BASE_URL_TTS } = window.$global;

const prefix = `${BASE_URL_TTS}`;
const prefix_speech = `${BASE_URL_SPEECH}`;

export const voiceApi = {
  audioToText: (params) => http.post(prefix_speech + `/speech/asr/base`, params, { headers: { "Content-Type": "multipart/form-data" } }),
  textToAudio: (params) => http.post(prefix + `/tts/offline`, params)
};
