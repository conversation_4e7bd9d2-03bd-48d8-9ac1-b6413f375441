/**
 * 状态管理器类
 * 负责管理图谱组件的各种状态
 */
export class StateManager {
  constructor() {
    this.loading = false;
    this.loadError = null;
    this.selectedNode = null;
    this.selectedLink = null;
    this.hoveredNode = null;
    this.highlightedNodeIds = new Set();
    this.simulationStopped = false;
    this.pendingLoadDueToZeroDimensions = false;
    this.maxLevel = 0;

    // Tooltip 状态
    this.tooltipData = null;
    this.tooltipStyle = {
      display: "none",
      left: "0px",
      top: "0px"
    };
    this.tooltipHeight = 150;

    // 搜索状态
    this.searchQuery = "";

    // 显示设置
    this.showNodeLabels = true;
    this.showLinkLabels = true;
  }

  /**
   * 设置加载状态
   * @param {boolean} loading 是否正在加载
   * @param {string|null} error 错误信息
   */
  setLoadingState(loading, error = null) {
    this.loading = loading;
    this.loadError = error;
  }

  /**
   * 设置选中的节点
   * @param {Object|null} node 节点对象
   */
  setSelectedNode(node) {
    this.selectedNode = node;
    if (node) {
      this.selectedLink = null; // 清除连接线选择
    }
  }

  /**
   * 设置选中的连接线
   * @param {Object|null} link 连接线对象
   */
  setSelectedLink(link) {
    this.selectedLink = link;
    if (link) {
      this.selectedNode = null; // 清除节点选择
    }
  }

  /**
   * 设置悬停的节点
   * @param {Object|null} node 节点对象
   */
  setHoveredNode(node) {
    this.hoveredNode = node;
  }

  /**
   * 添加高亮节点
   * @param {string|number} nodeId 节点ID
   */
  addHighlightedNode(nodeId) {
    this.highlightedNodeIds.add(nodeId);
  }

  /**
   * 清除所有高亮节点
   */
  clearHighlightedNodes() {
    this.highlightedNodeIds.clear();
  }

  /**
   * 检查节点是否高亮
   * @param {string|number} nodeId 节点ID
   * @returns {boolean}
   */
  isNodeHighlighted(nodeId) {
    return this.highlightedNodeIds.has(nodeId);
  }

  /**
   * 设置模拟状态
   * @param {boolean} stopped 是否停止
   */
  setSimulationStopped(stopped) {
    this.simulationStopped = stopped;
  }

  /**
   * 设置待加载状态（由于零尺寸）
   * @param {boolean} pending 是否待加载
   */
  setPendingLoadDueToZeroDimensions(pending) {
    this.pendingLoadDueToZeroDimensions = pending;
  }

  /**
   * 设置最大层级
   * @param {number} level 最大层级
   */
  setMaxLevel(level) {
    this.maxLevel = level;
  }

  /**
   * 设置Tooltip数据
   * @param {Object|null} data Tooltip数据
   */
  setTooltipData(data) {
    this.tooltipData = data;
  }

  /**
   * 设置Tooltip样式
   * @param {Object} style 样式对象
   */
  setTooltipStyle(style) {
    this.tooltipStyle = { ...this.tooltipStyle, ...style };
  }

  /**
   * 隐藏Tooltip
   */
  hideTooltip() {
    this.tooltipStyle = {
      ...this.tooltipStyle,
      display: "none"
    };
    this.tooltipData = null;
  }

  /**
   * 设置搜索查询
   * @param {string} query 搜索查询字符串
   */
  setSearchQuery(query) {
    this.searchQuery = query;
  }

  /**
   * 设置节点标签显示状态
   * @param {boolean} show 是否显示
   */
  setShowNodeLabels(show) {
    this.showNodeLabels = show;
  }

  /**
   * 设置连接线标签显示状态
   * @param {boolean} show 是否显示
   */
  setShowLinkLabels(show) {
    this.showLinkLabels = show;
  }

  /**
   * 清除所有选择状态
   */
  clearSelection() {
    this.selectedNode = null;
    this.selectedLink = null;
    this.clearHighlightedNodes();
  }

  /**
   * 重置所有状态
   */
  reset() {
    this.loading = false;
    this.loadError = null;
    this.selectedNode = null;
    this.selectedLink = null;
    this.hoveredNode = null;
    this.highlightedNodeIds.clear();
    this.simulationStopped = false;
    this.pendingLoadDueToZeroDimensions = false;
    this.maxLevel = 0;
    this.tooltipData = null;
    this.tooltipStyle = {
      display: "none",
      left: "0px",
      top: "0px"
    };
    this.searchQuery = "";
  }

  /**
   * 获取当前状态快照
   * @returns {Object} 状态快照
   */
  getSnapshot() {
    return {
      loading: this.loading,
      loadError: this.loadError,
      hasSelectedNode: !!this.selectedNode,
      hasSelectedLink: !!this.selectedLink,
      hasHoveredNode: !!this.hoveredNode,
      highlightedNodesCount: this.highlightedNodeIds.size,
      simulationStopped: this.simulationStopped,
      searchQuery: this.searchQuery,
      showNodeLabels: this.showNodeLabels,
      showLinkLabels: this.showLinkLabels
    };
  }
}
