<template>
  <div
    class="legend-panel"
    :class="theme"
  >
    <button
      v-if="showLeftArrow"
      class="scroll-btn left-btn"
      @click="scrollContent('left')"
    >
      ‹
    </button>
    <div
      v-else
      class="scroll-btn"
    ></div>
    <div class="legend-panel-content-wrapper">
      <div
        class="legend-panel-content"
        ref="legendContent"
      >
        <div
          class="legend-item"
          style="cursor: pointer"
        >
          <div>总数:</div>
          <div
            class="legend-item-color"
            :style="{ backgroundColor: 'rgb(188 188 188)' }"
            @click="doFilter('all')"
          ></div>
          <div>{{ total }}</div>
        </div>
        <div
          v-for="(value, key, index) in legend"
          class="legend-item"
          :key="index"
        >
          <div>{{ key }}:</div>
          <div
            class="legend-item-color"
            :style="{ backgroundColor: value }"
            @click="doFilter(key)"
          ></div>
          <div>{{ legendCountMap[key] }}</div>
        </div>
      </div>
    </div>
    <button
      v-if="showRightArrow"
      class="scroll-btn right-btn"
      @click="scrollContent('right')"
    >
      ›
    </button>
    <div
      v-else
      class="scroll-btn"
    ></div>
  </div>
</template>
<script>
export default {
  name: "LegendPanel",
  props: {
    legendMap: {
      type: Object,
      default() {
        return {};
      }
    },
    labelCountMap: {
      type: Object,
      default() {
        return {};
      }
    },
    theme: {
      type: String,
      default: "light"
    }
  },
  data() {
    return {
      legend: null,
      legendCountMap: {},
      total: 0,
      showLeftArrow: false,
      showRightArrow: false
    };
  },
  watch: {
    legendMap: {
      deep: true,
      handler() {
        this.initLegend();
      }
    },
    legendCountMap: {
      deep: true,
      handler() {
        this.initLegend();
      }
    }
  },
  mounted() {
    this.initLegend();
    this.updateArrowVisibility();
    window.addEventListener("resize", this.updateArrowVisibility);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateArrowVisibility);
  },
  methods: {
    initLegend() {
      this.legend = this.legendMap;
      this.legendCountMap = this.labelCountMap;
      this.total = 0;
      for (let key in this.legendCountMap) {
        this.total += this.legendCountMap[key];
      }
      this.$nextTick(() => {
        this.updateArrowVisibility();
      });
    },
    updateArrowVisibility() {
      const content = this.$refs.legendContent;
      if (content) {
        this.showLeftArrow = content.scrollLeft > 0;
        this.showRightArrow = content.scrollWidth > content.clientWidth && content.scrollLeft + content.clientWidth < content.scrollWidth;
      }
    },
    scrollContent(direction) {
      const content = this.$refs.legendContent;
      const scrollAmount = 100;
      if (direction === "left") {
        content.scrollLeft -= scrollAmount;
      } else if (direction === "right") {
        content.scrollLeft += scrollAmount;
      }
      this.$nextTick(() => {
        this.updateArrowVisibility();
      });
    },
    doFilter(label) {
      this.$emit("doFilter", label);
    }
  }
};
</script>
<style lang="scss" scoped>
.legend-panel {
  width: 100%;
  height: 40px; /* 高度为 40px */
  color: #ffffff;
  background-color: transparent;
  box-sizing: border-box;
  display: flex;
  align-items: center;

  .scroll-btn {
    width: 25px; /* 减小箭头按钮宽度 */
    height: 100%;
    background: none; /* 移除背景 */
    border: none;
    color: #666666; /* 设定箭头颜色 */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px; /* 箭头大小 */
    outline: none;

    &:hover {
      color: #333333; /* 鼠标悬停时加深箭头颜色 */
    }

    &.left-btn {
      margin-right: 5px;
    }

    &.right-btn {
      margin-left: 5px;
    }
  }

  .legend-panel-content-wrapper {
    overflow: hidden; /* 隐藏溢出的内容 */
    flex: 1;

    .legend-panel-content {
      display: flex;
      align-items: center;
      transition: scroll-behavior 0.3s ease; /* 平滑滚动 */
      scroll-behavior: smooth; /* 平滑滚动 */
      overflow-x: auto; /* 水平滚动 */

      /* 强制隐藏滚动条 */
      -ms-overflow-style: none; /* IE 和 Edge 浏览器 */
      scrollbar-width: none; /* Firefox */
    }
    .legend-panel-content::-webkit-scrollbar {
      display: none; /* Chrome 和 Safari */
    }

    .legend-item {
      margin-right: 10px;
      display: flex;
      align-items: center;
      font-size: 12px;
      white-space: nowrap; /* 防止内容换行 */
      height: 100%; /* 内容高度与父容器一致 */

      .legend-item-color {
        width: 12px; /* 调整小圆点大小 */
        height: 12px; /* 调整小圆点大小 */
        border-radius: 50%;
        margin: 0 3px;
        cursor: pointer;
      }
    }
  }
}

.dark,
.transparent {
  &.legend-panel {
    color: #ffffff;

    .legend-panel-content {
      background-color: transparent;
    }
  }
}

.light {
  &.legend-panel {
    color: #000000;

    .legend-panel-content {
      background-color: transparent;
    }
  }
}
</style>
