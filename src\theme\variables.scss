/* Button 按钮
------------------------------- */
.el-button {
  // text
  &--text {
    color: var(--nari-c-primary);
    &:focus,
    &:hover {
      color: var(--nari-c-primary-light-3);
    }
  }
  &--text:active {
    color: var(--nari-c-primary-light-3);
  }
  // default
  &--default {
    color: var(--nari-c-text-1);
    background: var(--nari-c-bg);
    &:hover,
    &:focus {
      color: var(--nari-c-primary);
      background: var(--nari-c-primary-light-8);
      border-color: var(--nari-c-primary-light-6);
    }
    &:active {
      color: var(--nari-c-primary-light-6);
      border-color: var(--nari-c-primary-light-6);
    }
    &.is-plain:hover,
    &.is-plain:focus {
      color: var(--nari-c-primary);
      border-color: var(--nari-c-primary-light-1);
    }
  }
  &.is-disabled {
    &.is-plain {
      &,
      &:hover,
      &:focus {
        background-color: var(--nari-c-bg);
        border-color: var(--nari-c-border-base);
        color: var(--nari-c-border-hover);
      }
    }
  }
  // primary
  &--primary {
    color: var(--nari-c-white);
    background: var(--nari-c-primary);
    border-color: var(--nari-c-primary);
    &:hover,
    &:focus {
      color: var(--nari-c-white);
      background: var(--nari-c-primary-light-3);
      border-color: var(--nari-c-primary-light-3);
    }
    &.is-active,
    &:active {
      color: var(--nari-c-white);
      background: var(--nari-c-primary);
      border-color: var(--nari-c-primary);
    }
    &.is-plain {
      color: var(--nari-c-primary);
      background: var(--nari-c-primary-light-8);
      border-color: var(--nari-c-primary-light-6);
      &:hover,
      &:focus {
        color: var(--nari-c-white);
        background: var(--nari-c-primary);
        border-color: var(--nari-c-primary);
      }
      &.is-disabled {
        &,
        &:hover,
        &:focus,
        &:active {
          color: var(--nari-c-primary);
          background: var(--nari-c-primary-light-8);
          border-color: var(--nari-c-primary-light-6);
        }
      }
    }
    &.is-disabled {
      &,
      &:active,
      &:focus,
      &:hover {
        color: var(--nari-c-white);
        background: var(--nari-c-primary-light-7);
        border-color: var(--nari-c-primary-light-7);
      }
    }
  }
}

/* Link 文字链接
------------------------------- */
.el-link {
  &.is-underline {
    &:hover:after {
      border-color: var(--nari-c-primary);
    }
  }
  // default
  &.el-link--default {
    &:hover {
      color: var(--nari-c-primary-light-3);
    }
    &:after {
      border-color: var(--nari-c-primary);
    }
    &.is-disabled {
      color: var(--nari-c-border-base);
    }
  }
  // primary
  .el-link.el-link--primary {
    color: var(--nari-c-primary);
    &:hover {
      color: var(--nari-c-primary-light-3);
    }
    &:after {
      border-color: var(--nari-c-primary);
    }
    &.is-disabled {
      color: var(--nari-c-primary-light-6);
    }
    &.is-underline {
      &:hover:after {
        border-color: var(--nari-c-primary);
      }
    }
  }
}

/* Radio 单选框
------------------------------- */
.el-radio {
  color: var(--nari-c-text-1);
  &.is-bordered {
    border-color: var(--nari-c-border-base);
    &.is-checked {
      border-color: var(--nari-c-primary);
    }
    &.is-disabled {
      border-color: var(--nari-c-border-base);
    }
  }
  &__input {
    &.is-checked {
      .el-radio__inner {
        border-color: var(--nari-c-primary);
        background: var(--nari-c-primary);
      }
      & + .el-radio__label {
        color: var(--nari-c-primary);
      }
    }
  }
  &__inner {
    &:hover {
      border-color: var(--nari-c-primary);
    }
    &::after {
      background-color: var(--nari-c-white);
    }
  }
}
.el-radio-button {
  &__inner {
    background-color: var(--nari-c-bg);
    color: var(--nari-c-text-1);
    border-color: var(--nari-c-border-base);
    &:hover {
      color: var(--nari-c-primary);
    }
  }
  &:first-child {
    .el-radio-button__inner {
      border-left-color: var(--nari-c-border-base);
    }
  }
}

/* Checkbox 多选框
------------------------------- */
.el-checkbox {
  color: var(--nari-c-text-1);
  &.is-bordered {
    border-color: var(--nari-c-border-base);
    &.is-checked {
      border-color: var(--nari-c-primary);
    }
    &.is-disabled {
      border-color: var(--nari-c-border-base);
    }
  }
  &__input {
    &.is-checked {
      .el-checkbox__inner {
        background-color: var(--nari-c-primary);
        border-color: var(--nari-c-primary);
      }
      & + .el-checkbox__label {
        color: var(--nari-c-primary);
      }
    }
    &.is-focus {
      .el-checkbox__inner {
        border-color: var(--nari-c-primary);
      }
    }
    &.is-indeterminate {
      .el-checkbox__inner {
        background-color: var(--nari-c-primary);
        border-color: var(--nari-c-primary);
        &::before {
          background-color: var(--nari-c-white);
        }
      }
    }
    &.is-disabled {
      & + span.el-checkbox__label {
        color: var(--nari-c-text-inverse-3);
      }
    }
  }
  &__inner {
    &:hover {
      border-color: var(--nari-c-primary);
    }
    &::after {
      border-color: var(--nari-c-white);
    }
  }
}
.el-checkbox-button {
  &__inner {
    background-color: var(--nari-c-bg);
    color: var(--nari-c-text-1);
    border-color: var(--nari-c-border-base);
    &:hover {
      color: var(--nari-c-primary);
    }
  }
  &.is-checked {
    & .el-checkbox-button__inner {
      color: var(--nari-c-text-inverse-1);
      background-color: var(--nari-c-primary);
      border-color: var(--nari-c-primary);
      box-shadow: -1px 0 0 0 var(--nari-c-primary);
    }
    &:first-child .el-checkbox-button__inner {
      border-left-color: var(--nari-c-primary);
    }
  }
  &.is-disabled {
    & .el-checkbox-button__inner {
      background-color: var(--nari-c-bg);
      border-color: var(--nari-c-border-base);
    }
  }
  &:first-child {
    .el-checkbox-button__inner {
      border-left-color: var(--nari-c-border-base);
    }
  }
  &.is-focus {
    & .el-checkbox-button__inner {
      border-color: var(--nari-c-primary);
    }
  }
}

/* Input 输入框、InputNumber 计数器
------------------------------- */
// input-number
.el-input-number {
  &__decrease,
  &__increase {
    background-color: var(--nari-c-bg);
    border-color: var(--nari-c-border-base);
    &:hover {
      color: var(--nari-c-primary);
      &:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled) {
        border-color: var(--nari-c-primary) !important;
      }
    }
  }
}
// input
.el-input,
.el-textarea {
  &__inner {
    background-color: var(--nari-c-bg);
    border-color: var(--nari-c-border-base);
    color: var(--nari-c-text-3);
  }
  &:hover {
    border-color: var(--nari-c-primary);
  }
  &:focus {
    border-color: var(--nari-c-primary) !important;
  }
}
.el-input {
  &.is-disabled {
    .el-input__inner {
      background-color: var(--nari-c-bg);
      border-color: var(--nari-c-border-base);
    }
  }
  &__count {
    &-inner {
      background-color: var(--nari-c-bg);
    }
  }
  &-group {
    &__append,
    &__prepend {
      background-color: var(--nari-c-text-inverse-5);
      color: var(--nari-c-text-1);
      border-color: var(--nari-c-border-base);
    }
  }
}
.el-textarea {
  &.is-disabled {
    .el-textarea__inner {
      background-color: var(--nari-c-bg);
      border-color: var(--nari-c-border-base);
    }
  }
}
// autocomplete
.el-autocomplete-suggestion {
  background-color: var(--nari-c-bg);
  border-color: var(--nari-c-border-base);
  &__wrap {
    max-height: 280px;
  }
  li {
    color: var(--nari-c-text-1);
    &.highlighted,
    &:hover {
      background-color: var(--nari-c-hover);
    }
  }
}
// scss 循环
$positions: "top", "right", "bottom", "left";
@each $i in $positions {
  .el-popper[x-placement^="#{$i}"] .popper__arrow {
    border-#{$i}-color: var(--nari-c-border-base);
    &::after {
      border-#{$i}-color: var(--nari-c-bg);
    }
  }
}

/* Select 选择器
------------------------------- */
.el-select {
  &:hover {
    .el-input__inner {
      border-color: var(--nari-c-primary);
    }
  }
  .el-input {
    &__inner {
      &:focus {
        border-color: var(--nari-c-primary);
      }
    }
    &.is-disabled {
      & .el-input__inner {
        &:hover {
          border-color: var(--nari-c-border);
        }
      }
    }
    &.is-focus .el-input__inner {
      border-color: var(--nari-c-primary);
    }
  }
  &-dropdown {
    background-color: var(--nari-c-bg);
    border-color: var(--nari-c-border);
    &__item {
      color: var(--nari-c-text-1);
      &.selected {
        color: var(--nari-c-primary);
      }
      &.hover,
      &:hover {
        background-color: var(--nari-c-hover);
      }
      &.is-disabled {
        &:hover {
          background-color: var(--nari-c-bg);
        }
      }
    }
    &.is-multiple {
      .el-select-dropdown__item.selected {
        background-color: var(--nari-c-bg);
        &.hover {
          background-color: var(--nari-c-hover);
        }
      }
    }
  }
  &-group__wrap:not(:last-of-type)::after {
    background: var(--nari-c-border);
  }
}
.el-range-editor.is-active,
.el-range-editor.is-active:hover {
  border-color: var(--nari-c-primary);
}

/* Cascader 级联选择器
------------------------------- */
.el-cascader {
  &:not(.is-disabled):hover {
    .el-input__inner {
      border-color: var(--nari-c-primary);
    }
  }
  .el-input {
    .el-input__inner {
      &:focus {
        border-color: var(--nari-c-primary);
      }
    }
    &.is-focus {
      .el-input__inner {
        border-color: var(--nari-c-primary);
      }
    }
  }
  &__dropdown {
    background-color: var(--nari-c-bg);
    border-color: var(--nari-c-border);
  }
  &-menu {
    border-color: var(--nari-c-border);
    color: var(--nari-c-text-1);
  }
  &-node {
    &.in-active-path,
    &.is-selectable.in-checked-path,
    &.is-active {
      color: var(--nari-c-primary);
    }
    &:not(.is-disabled) {
      &:hover,
      &:focus {
        background-color: var(--nari-c-hover);
      }
    }
  }
}

/* Switch 开关
------------------------------- */
.el-switch {
  &.is-checked {
    .el-switch__core {
      border-color: var(--nari-c-primary);
      background-color: var(--nari-c-primary);
    }
  }
  &__label.is-active {
    color: var(--nari-c-primary);
  }
}

/* Slider 滑块
------------------------------- */
.el-slider {
  &__bar {
    background-color: var(--nari-c-primary);
  }
  &__button {
    border-color: var(--nari-c-primary);
  }
  &__runway {
    background-color: var(--nari-c-border);
  }
  &__marks-text {
    color: var(--nari-c-text-3);
  }
}

/* TimePicker 时间选择器
------------------------------- */
.el-time-panel__btn.confirm,
.el-time-spinner__arrow:hover,
.time-select-item.selected:not(.disabled) {
  color: var(--nari-c-primary);
}
.el-picker-panel {
  border-color: var(--nari-c-border);
  background-color: var(--nari-c-bg);
  color: var(--nari-c-text-1);
}
.time-select-item:hover,
.el-time-spinner__item:hover:not(.disabled):not(.active),
.el-time-spinner__wrapper.is-arrow .el-time-spinner__item:hover:not(.disabled):not(.active) {
  background-color: var(--nari-c-hover);
}
.el-time-panel {
  border-color: var(--nari-c-border);
  background-color: var(--nari-c-bg);
}
.el-time-panel__footer,
.el-time-panel__content::after,
.el-time-panel__content::before,
.el-time-range-picker__body {
  border-color: var(--nari-c-border);
}
.el-time-panel__btn,
.el-date-editor .el-range-separator {
  color: var(--nari-c-text-1);
}
.el-date-editor .el-range-input {
  background-color: var(--nari-c-bg);
  color: var(--nari-c-text-1);
}

/* DatePicker 日期选择器
------------------------------- */
.el-date-table td.today span,
.el-date-table td.available:hover,
.el-date-picker__header-label.active,
.el-date-picker__header-label:hover,
.el-picker-panel__icon-btn:hover,
.el-year-table td.today .cell,
.el-year-table td .cell:hover,
.el-year-table td.current:not(.disabled) .cell,
.el-month-table td .cell:hover,
.el-month-table td.today .cell,
.el-month-table td.current:not(.disabled) .cell,
.el-picker-panel__shortcut:hover {
  color: var(--nari-c-primary);
}
.el-date-table td.current:not(.disabled) span,
.el-date-table td.selected span {
  background-color: var(--nari-c-primary);
}
.el-date-table td.end-date span,
.el-date-table td.start-date span,
.el-month-table td.end-date .cell,
.el-month-table td.start-date .cell {
  background-color: var(--nari-c-primary);
}
.el-date-table td.in-range div,
.el-date-table td.in-range div:hover,
.el-month-table td.in-range div,
.el-month-table td.in-range div:hover,
.el-date-table.is-week-mode .el-date-table__row.current div,
.el-date-table.is-week-mode .el-date-table__row:hover div,
.el-date-table td.selected div {
  background-color: var(--nari-c-bg-mute);
}
.el-date-table th,
.el-date-picker__header--bordered,
.el-date-range-picker__content.is-left,
.el-date-picker__time-header,
.el-date-range-picker__time-header {
  border-color: var(--nari-c-border-base);
}
.el-date-table th,
.el-date-picker__header-label,
.el-picker-panel__shortcut,
.el-month-table td .cell,
.el-year-table td .cell {
  color: var(--nari-c-text-1);
}
.el-date-table td.next-month,
.el-date-table td.prev-month {
  color: var(--nari-c-text-inverse-3);
}
.el-picker-panel__icon-btn {
  color: var(--nari-c-text-1);
}
.el-date-table td.disabled div {
  background-color: var(--nari-c-bg);
}
.el-picker-panel [slot="sidebar"],
.el-picker-panel__sidebar,
.el-picker-panel__footer {
  border-color: var(--nari-c-border);
  background-color: var(--nari-c-bg);
}
.el-month-table td.end-date .cell,
.el-month-table td.start-date .cell {
  color: var(--nari-c-white);
  background-color: var(--nari-c-primary);
}

/* Upload 上传
------------------------------- */
.el-upload-list__item.is-success .el-upload-list__item-name:focus,
.el-upload-list__item.is-success .el-upload-list__item-name:hover,
.el-upload-list__item .el-icon-close-tip,
.el-upload-dragger .el-upload__text em {
  color: var(--nari-c-primary);
}
.el-upload--picture-card,
.el-upload-dragger {
  background-color: var(--nari-c-bg);
  border-color: var(--nari-c-border);
  i {
    color: var(--nari-c-text-1);
  }
}
.el-upload--picture-card:hover,
.el-upload:focus {
  color: var(--nari-c-primary);
  border-color: var(--nari-c-primary);
}
.el-upload-dragger:hover,
.el-upload:focus .el-upload-dragger {
  border-color: var(--nari-c-primary);
}
.el-upload__tip,
.el-upload-list__item,
.el-upload-dragger .el-upload__text,
.el-upload-list__item-name,
.el-upload-list__item .el-icon-close {
  color: var(--nari-c-text-1);
}
.el-upload-list__item:hover {
  background-color: var(--nari-c-bg);
}

/* ColorPicker 颜色选择器
------------------------------- */
.el-color-picker__trigger {
  border-color: var(--nari-c-border);
}

/* Transfer 穿梭框
------------------------------- */
.el-transfer-panel {
  &,
  .el-transfer-panel__header {
    background-color: var(--nari-c-bg);
    border-color: var(--nari-c-border-base);
  }
  .el-transfer-panel__item:hover {
    color: var(--nari-c-primary);
  }
  .el-transfer-panel__footer {
    border-color: var(--nari-c-border-base);
    background-color: var(--nari-c-bg);
  }
  .el-transfer-panel__item.el-checkbox,
  .el-transfer-panel__header .el-checkbox .el-checkbox__label {
    color: var(--nari-c-text-1);
  }
}

/* Form 表单
------------------------------- */
.el-form {
  &-item {
    &__label {
      color: var(--nari-c-text-1);
    }
  }
}

/* Table 表格
------------------------------- */
.el-table {
  color: var(--nari-c-table-body-text);
  background: var(--nari-c-table-bg);
  thead {
    color: var(--nari-c-table-head-text);
    &.is-group {
      th.el-table__cell {
        background: var(--nari-c-table-header-bg);
      }
    }
  }
  tr {
    background-color: var(--nari-c-table-bg);
  }
  th.el-table__cell {
    background: var(--nari-c-table-header-bg);
  }
  .descending .sort-caret.descending {
    border-top-color: var(--nari-c-primary);
  }
  .ascending .sort-caret.ascending {
    border-bottom-color: var(--nari-c-primary);
  }
  &--striped .el-table__body {
    & tr.el-table__row--striped {
      td.el-table__cell {
        background-color: var(--nari-c-table-row-stripe-bg);
      }
      &.current-row td.el-table__cell {
        background-color: var(--nari-c-table-row-highlight-bg);
      }
    }
  }
  &__body {
    tr.hover-row {
      &,
      &.el-table__row--striped {
        &,
        &.current-row {
          > td.el-table__cell {
            background-color: var(--nari-c-table-row-hover-bg);
          }
        }
      }
    }
    tr.current-row > td.el-table__cell {
      background-color: var(--nari-c-table-row-highlight-bg);
    }
  }
  &--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: var(--nari-c-table-row-hover-bg);
  }
  &--border::after,
  &--group::after,
  &::before {
    background-color: var(--nari-c-border-base);
  }
  & td.el-table__cell,
  & th.el-table__cell.is-leaf,
  &--border,
  &--border th.el-table__cell,
  &__fixed-right-patch,
  &__fixed-footer-wrapper tbody td.el-table__cell {
    border-color: var(--nari-c-border-base);
  }
  &--border th.el-table__cell {
    &.gutter:last-of-type {
      border-bottom: transparent;
    }
  }
  &__footer-wrapper tbody td.el-table__cell,
  &__header-wrapper tbody td.el-table__cell,
  &__fixed-footer-wrapper tbody td.el-table__cell {
    color: var(--nari-c-table-body-text);
    background: var(--nari-c-table-header-bg);
  }
}

/* Tag 标签
------------------------------- */
// primary
.el-tag {
  color: var(--nari-c-primary);
  background-color: var(--nari-c-primary-light-8);
  border-color: var(--nari-c-primary-light-6);
  .el-tag__close {
    color: var(--nari-c-primary);
    &:hover {
      color: var(--nari-c-text-inverse-1);
      background-color: var(--nari-c-primary);
    }
  }
  &--dark {
    color: var(--nari-c-text-inverse-1);
    background-color: var(--nari-c-primary);
    .el-tag__close {
      color: var(--nari-c-text-inverse-1);
      &:hover {
        background-color: var(--nari-c-primary-light-3);
      }
    }
  }
  &--plain {
    color: var(--nari-c-primary);
    background-color: var(--nari-c-bg);
    border-color: var(--nari-c-primary-light-3);
  }
}

/* Progress 进度条
------------------------------- */
// primary
.el-progress {
  &-bar {
    &__inner {
      background-color: var(--nari-c-primary);
    }
    &__outer {
      background-color: var(--nari-c-border-base) !important;
    }
  }
  &__text {
    color: var(--nari-c-text-1) !important;
  }
}

/* Tree 树形控件
------------------------------- */
.el-tree {
  background-color: var(--nari-c-bg);
  color: var(--nari-c-text-1);
  &-node__content:hover,
  &-node:focus > .el-tree-node__content {
    background-color: var(--nari-c-bg);
  }
}

/* Pagination 分页
------------------------------- */
.el-pagination {
  display: flex;
  color: var(--nari-c-primary);
  button {
    &:hover {
      color: var(--nari-c-primary);
    }
    &:disabled {
      background-color: var(--nari-c-bg);
    }
  }
  .btn-prev,
  .btn-next {
    background-color: var(--nari-c-bg);
    color: var(--nari-c-text-1);
  }
  &__sizes {
    .el-input .el-input__inner {
      &:hover {
        border-color: var(--nari-c-primary);
      }
    }
  }
  &__total,
  &__jump {
    color: var(--nari-c-text-3);
  }
  &__jump {
    .el-input .el-input__inner {
      &:hover {
        border-color: var(--nari-c-primary);
      }
    }
  }
  &.is-background {
    .btn-prev,
    .btn-next {
      background-color: var(--nari-c-bg);
      color: var(--nari-c-text-1);
    }

    .el-pager li:not(.disabled) {
      &:hover {
        color: var(--nari-c-primary);
      }
      &.active {
        background-color: var(--nari-c-primary);
      }
    }
  }
}
.el-pager {
  li {
    background-color: var(--nari-c-bg);
    color: var(--nari-c-text-1);
    &:hover,
    &.active {
      color: var(--nari-c-primary);
    }
  }
}

/* Badge 标记
------------------------------- */
// primary
.el-badge__content--primary {
  background-color: var(--nari-c-primary);
}

/* Loading 加载
------------------------------- */
.el-loading {
  &-spinner {
    .el-loading-text,
    i {
      color: var(--nari-c-primary);
    }
    .path {
      stroke: var(--nari-c-primary);
    }
  }
}

/* Message 消息提示
------------------------------- */
// default/info
.el-message {
  min-width: unset;
  padding: 15px;
}

/* MessageBox 弹框
------------------------------- */
.el-message-box {
  background-color: var(--nari-c-bg);
  border-color: var(--nari-c-border-base);
  &__headerbtn {
    &:focus,
    &:hover {
      .el-message-box__close {
        color: var(--nari-c-primary);
      }
    }
  }
  &__title {
    color: var(--nari-c-text-1);
  }
  &__content {
    color: var(--nari-c-text-1);
  }
}

/* Notification 通知
------------------------------- */
.el-notification {
  background-color: var(--nari-c-bg);
  border-color: var(--nari-c-border-base);
  &__title {
    color: var(--nari-c-text-1);
  }
  &__content {
    color: var(--nari-c-text-1);
  }
}

/* Tabs 标签页
------------------------------- */
.el-tabs {
  &__active-bar {
    background-color: var(--nari-c-primary);
  }
  &__nav-wrap::after {
    height: 1px;
    background-color: var(--nari-c-border-base);
  }
  &__item {
    color: var(--nari-c-text-1);
    &:focus.is-active.is-focus:not(:active) {
      box-shadow: 0 0 2px 2px var(--nari-c-primary) inset;
    }
    & .el-icon-close {
      &:hover {
        background-color: var(--nari-c-primary);
        color: var(--nari-c-white);
      }
    }
    &.is-active,
    &:hover {
      color: var(--nari-c-primary);
    }
  }
  .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active,
  .el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
    color: var(--nari-c-primary);
  }
  &--card {
    > .el-tabs__header,
    > .el-tabs__header .el-tabs__nav,
    > .el-tabs__header .el-tabs__item,
    > .el-tabs__header .el-tabs__item.is-active {
      border-color: var(--nari-c-border-base);
    }
  }
  &--border-card {
    background-color: var(--nari-c-bg);
    border-color: var(--nari-c-border-base);
    > .el-tabs__header {
      background-color: var(--nari-c-bg);
      border-color: var(--nari-c-border);
      .el-tabs__item {
        color: var(--nari-c-text-1);
      }
    }
    > .el-tabs__header .el-tabs__item {
      &.is-active {
        color: var(--nari-c-primary);
        background-color: var(--nari-c-bg);
        border-color: var(--nari-c-border-base);
      }
      &:not(.is-disabled):hover {
        color: var(--nari-c-primary);
      }
    }
  }
}

/* Breadcrumb 面包屑
------------------------------- */
.el-breadcrumb {
  &__separator {
    color: var(--nari-c-border-hover);
  }
  &__inner {
    &.is-link,
    & a {
      color: var(--nari-c-white);
      &:hover {
        color: var(--nari-c-primary);
      }
    }
  }
}

/* PageHeader 页头
------------------------------- */
.el-page-header {
  display: flex;
  &__left {
    color: var(--nari-c-text-inverse-1);
    &::after {
      background-color: var(--nari-c-border-base);
    }
  }
  &__content {
    color: var(--nari-c-text-1);
  }
}

/* Dropdown 下拉菜单
------------------------------- */
.el-dropdown {
  &-menu {
    background-color: var(--nari-c-bg);
    border-color: var(--nari-c-border-base);
    &__item {
      color: var(--nari-c-text-1);
      &:not(.is-disabled):hover,
      &:focus {
        color: var(--nari-c-primary);
        background-color: var(--nari-c-primary-light-9);
      }
      &--divided {
        border-color: var(--nari-c-border-base);
        &:before {
          background-color: var(--nari-c-bg);
        }
      }
    }
  }
}

/* Steps 步骤条
------------------------------- */
// default
.el-step {
  &__title.is-finish,
  &__description.is-finish,
  &__head.is-finish {
    color: var(--nari-c-primary);
  }
  &__head.is-finish {
    border-color: var(--nari-c-primary);
  }
}

/* Dialog 对话框
------------------------------- */
.el-dialog {
  background-color: var(--nari-c-bg);
  &__headerbtn {
    &:focus,
    &:hover {
      .el-dialog__close {
        color: var(--nari-c-primary);
      }
    }
  }
  &__title {
    color: var(--nari-c-text-1);
  }
  &__body {
    color: var(--nari-c-text-1);
  }
}

/* Popover 弹出框
------------------------------- */
.el-popover {
  background-color: var(--nari-c-bg);
  border-color: var(--nari-c-border-base);
  color: var(--nari-c-text-1);
  &__title {
    color: var(--nari-c-text-1);
  }
}

/* Card 卡片
------------------------------- */
.el-card,
.nari-card {
  color: var(--nari-c-text-1);
  background-color: var(--nari-c-bg);
  border-color: var(--nari-c-border-base);
  &__header {
    border-bottom-color: var(--nari-c-border-base);
  }
}
.nari-card {
  border: none;
  &__container {
    background: transparent;
  }
  &__title-text {
    color: var(--nari-c-card-title);
  }
}

/* Collapse 折叠面板
------------------------------- */
.el-collapse {
  border-color: var(--nari-c-border-base);
  &-item {
    &__header {
      color: var(--nari-c-text-1);
      background-color: var(--nari-c-bg);
      border-color: var(--nari-c-border-base);
    }
    &__wrap {
      background-color: var(--nari-c-bg);
      border-color: var(--nari-c-border-base);
    }
    &__content {
      color: var(--nari-c-text-1);
    }
  }
}

/* Timeline 时间线
------------------------------- */
// primary
.el-timeline {
  &-item {
    &__node--primary {
      background-color: var(--nari-c-primary);
    }
    &__content {
      color: var(--nari-c-text-1);
    }
  }
}

/* Divider 分割线
------------------------------- */
.el-divider {
  background-color: var(--nari-c-border-base);
  &__text {
    color: var(--nari-c-text-1);
    background-color: var(--nari-c-bg);
  }
}

/* Calendar 日历
------------------------------- */
.el-calendar {
  background-color: var(--nari-c-bg);
  &__title {
    color: var(--nari-c-text-inverse-1);
  }
  &__header {
    border-color: var(--nari-c-border-base);
  }
  &-table {
    thead th {
      color: var(--nari-c-text-1);
    }
    &:not(.is-range) {
      td.prev,
      td.next {
        color: var(--nari-c-text-4);
      }
    }
    td {
      color: var(--nari-c-text-inverse-1);
      border-color: var(--nari-c-border-base);
      &.is-selected {
        background-color: var(--nari-c-primary-light-9);
        color: var(--nari-c-primary);
      }
      &.is-today {
        color: var(--nari-c-primary);
        background-color: var(--nari-c-primary-light-9);
      }
    }
    tr:first-child td {
      border-color: var(--nari-c-border-base);
    }
    tr td:first-child {
      border-color: var(--nari-c-border-base);
    }
    .el-calendar-day {
      &:hover {
        background-color: var(--nari-c-primary-light-9);
        color: var(--nari-c-primary);
      }
    }
  }
  &__button-group {
    .el-button {
      color: var(--nari-c-text-1);
      background-color: var(--nari-c-bg);
      border-color: var(--nari-c-border-base);
      &:focus,
      &:hover {
        color: var(--nari-c-primary);
        background: var(--nari-c-primary-light-8);
        border-color: var(--nari-c-primary-light-6);
      }
      &:active {
        color: var(--nari-c-primary-light-3);
      }
    }
  }
}

/* Backtop 回到顶部
------------------------------- */
.el-backtop {
  color: var(--nari-c-primary);
  z-index: 99;
  &:hover {
    background-color: var(--nari-c-primary-light-9);
  }
}

/* Drawer 抽屉
------------------------------- */
.el-drawer {
  background-color: var(--nari-c-bg);
  &__header {
    padding: 0 15px;
    height: 50px;
    margin-bottom: 0;
    border-bottom: 1px solid var(--nari-c-border-base);
    color: var(--nari-c-text-1);
  }
  &__close-btn:hover {
    color: var(--nari-c-primary);
  }
  &__body {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
