<template>
  <div class="el-menu-horizontal-warp">
    <el-scrollbar
      @wheel.native.prevent="onElMenuHorizontalScroll"
      ref="elMenuHorizontalScrollRef"
    >
      <el-menu
        router
        :default-active="defaultActive"
        background-color="transparent"
        mode="horizontal"
        @select="onHorizontalSelect"
      >
        <template v-for="val in menuList">
          <el-submenu
            :index="val.path"
            v-if="val.children && val.children.length > 0 && getThemeConfig.layout === 'horizontal'"
            :key="val.path"
          >
            <template slot="title">
              <span>
                <i :class="val.meta.icon ? val.meta.icon : ''"></i>
                <span>{{ val.meta.title }}</span>
              </span>
            </template>
            <SubItem :chil="val.children" />
          </el-submenu>
          <template v-else>
            <el-menu-item
              :index="val.path"
              :key="val.path"
            >
              <template
                slot="title"
                v-if="!val.meta.isLink || (val.meta.isLink && val.meta.isIframe)"
              >
                <span>
                  <i :class="val.meta.icon ? val.meta.icon : ''"></i>
                  <span>{{ val.meta.title }}</span>
                </span>
              </template>
              <template
                slot="title"
                v-else
              >
                <a
                  class="text-white no-underline"
                  :href="val.meta.isLink"
                  target="_blank"
                >
                  <i :class="val.meta.icon ? val.meta.icon : ''"></i>
                  <span>{{ val.meta.title }}</span>
                </a>
              </template>
            </el-menu-item>
          </template>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import SubItem from "@/layout/navMenu/subItem.vue";
export default {
  name: "navMenuHorizontal",
  components: { SubItem },
  props: {
    menuList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultActive: null
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    }
  },
  mounted() {
    this.initElMenuOffsetLeft();
    this.setCurrentRouterHighlight(this.$route.path);
  },
  methods: {
    // 设置横向滚动条可以鼠标滚轮滚动
    onElMenuHorizontalScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40;
      this.$refs.elMenuHorizontalScrollRef.$refs.wrap.scrollLeft = this.$refs.elMenuHorizontalScrollRef.$refs.wrap.scrollLeft + eventDelta / 4;
    },
    // 初始化数据，页面刷新时，滚动条滚动到对应位置
    initElMenuOffsetLeft() {
      this.$nextTick(() => {
        const els = document.querySelector(".el-menu.el-menu--horizontal li.is-active");
        if (!els) return false;
        this.$refs.elMenuHorizontalScrollRef.$refs.wrap.scrollLeft = els.offsetLeft;
      });
    },
    // 路由过滤递归函数
    filterRoutesFun(arr) {
      return arr
        .filter((item) => !item.meta.isHide)
        .map((item) => {
          item = Object.assign({}, item);
          if (item.children) {
            item.children = this.filterRoutesFun(item.children);
          }
          return item;
        });
    },
    // 传送当前子级数据到菜单中
    setSendVerticalChildren(path) {
      const currentPathSplit = path.split("/");
      const currentData = {};
      this.filterRoutesFun(this.$store.state.routesList.routesList).map((v, k) => {
        if (v.path === `/${currentPathSplit[1]}`) {
          v.k = k;
          currentData.item = [{ ...v }];
          currentData.children = this.getThemeConfig.layout !== "mix" ? [{ ...v }] : [];
          if (v.children) currentData.children = v.children;
        }
      });
      return currentData;
    },
    // 菜单激活回调
    onHorizontalSelect(path) {
      this.getThemeConfig === "horizontal" ? this.$bus.$emit("setSendVerticalChildren", this.setSendVerticalChildren(path)) : this.$bus.$emit("setSendMixChildren", this.setSendVerticalChildren(path));
    },
    // 设置页面当前路由高亮
    setCurrentRouterHighlight(path) {
      const currentPathSplit = path.split("/");
      if (["vertical", "mix"].includes(this.getThemeConfig.layout)) {
        this.defaultActive = `/${currentPathSplit[1]}`;
        this.getThemeConfig.layout === "mix" && this.$bus.$emit("setSendMixChildren", this.setSendVerticalChildren(path));
      } else {
        this.defaultActive = path;
      }
    }
  },
  watch: {
    // 监听路由的变化
    $route: {
      handler(to) {
        this.setCurrentRouterHighlight(to.path);
      },
      deep: true
    }
  }
};
</script>

<style scoped lang="scss">
.el-menu-horizontal-warp {
  flex: 1;
  overflow: hidden;
  ::v-deep .el-scrollbar__bar.is-vertical {
    display: none;
  }
  ::v-deep .el-scrollbar {
    height: 60px;
    &__wrap {
      overflow-y: hidden !important;
    }
  }
  ::v-deep a {
    width: 100%;
  }
  .el-menu.el-menu--horizontal {
    display: flex;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
  }
}
</style>
