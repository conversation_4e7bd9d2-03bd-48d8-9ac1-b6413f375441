import Vue from "vue";
import App from "./App";
import "./config/http";
import router from "./router";
import store from "./store";
import Element from "element-ui";
import NariUI from "nari-ui";
import useComponent from "@/utils/useComponent.js";
import * as echarts from "echarts";
import "virtual:svg-icons-register";
import "@/assets/fonts/font.css";
import "element-ui/lib/theme-chalk/index.css";
import "nari-ui/lib/theme-chalk/index.css";
import "@/theme/index.scss";
Vue.use(Element, { size: "small" });
Vue.use(NariUI);
Vue.prototype.$bus = new Vue();
Vue.prototype.$echarts = echarts;
Vue.use(useComponent);

Vue.config.productionTip = false;

new Vue({
  el: "#app",
  router,
  store,
  render: (h) => h(App)
}).$mount();
