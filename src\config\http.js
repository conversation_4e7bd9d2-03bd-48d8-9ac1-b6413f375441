// 开发环境的HTTP配置
export const HTTP_DEV = {
  TIMEOUT: 60000,
  BASE_URL: "http://172.29.252.31:10097/zd-assistance-decision", // 基础服务
  // BASE_URL: "http://10.100.1.19:10097/zd-assistance-decision", // 基础服务
  BASE_URL_LLM_TOOLS: "http://10.100.1.110:9000", // LLM 工具 /llm-tools
  BASE_URL_SPEECH: "http://10.100.1.110:11000", // 语音识别 /speech
  BASE_URL_TTS: "http://***************:8010" // 语音合成 /tts
};

// 测试本地环境的HTTP配置
export const HTTP_LOCAL_TEST = {
  TIMEOUT: 60000,
  BASE_URL: "/zd-assistance-decision", // 基础服务
  BASE_URL_LLM_TOOLS: "http://127.0.0.1:9000", // LLM 工具 /llm-tools
  BASE_URL_SPEECH: "http://127.0.0.1:11000", // 语音识别 /speech
  BASE_URL_TTS: "http://***************:8010" // 语音合成 /tts
};

// 测试生产环境的HTTP配置
export const HTTP_PROD_TEST = {
  TIMEOUT: 60000,
  BASE_URL: "/zd-assistance-decision", // 基础服务
  BASE_URL_LLM_TOOLS: "http://10.100.1.110:9000", // LLM 工具 /llm-tools
  BASE_URL_SPEECH: "http://10.100.1.110:11000", // 语音识别 /speech
  BASE_URL_TTS: "http://***************:8010" // 语音合成 /tts
};

// 生产环境的HTTP配置
export const HTTP_PROD = {
  TIMEOUT: 60000,
  BASE_URL: "/zd-assistance-decision", // 基础服务
  BASE_URL_LLM_TOOLS: "", // LLM 工具 /llm-tools
  BASE_URL_SPEECH: "", // 语音识别 /speech
  BASE_URL_TTS: "" // 语音合成 /tts
};

const MODE = import.meta.env.MODE;
const HTTP = {
  development: HTTP_DEV,
  production: HTTP_PROD,
  test: HTTP_PROD_TEST,
  localhost: HTTP_LOCAL_TEST
};
window.$global = HTTP[MODE];
console.log("当前环境", MODE, window.$global);
