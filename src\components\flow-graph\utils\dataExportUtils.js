/**
 * 数据导出工具类
 * 用于将图谱数据导出为JSON格式，支持坐标转换
 */
export class DataExportUtils {
  /**
   * 导出当前图谱数据为JSON格式
   * @param {Array} nodes - 当前的节点数组
   * @param {Array} links - 当前的连接线数组
   * @param {Number} posLayoutScale - 位置布局缩放比例，用于坐标转换
   * @param {Object} originalData - 原始数据，用于保持其他属性不变
   * @param {Number} canvasWidth - 画布宽度
   * @param {Number} canvasHeight - 画布高度
   * @returns {Object} 导出的JSON数据
   */
  static exportGraphData(nodes, links, posLayoutScale = 1, originalData = null, canvasWidth = 800, canvasHeight = 600) {
    try {
      // 创建导出数据的基础结构，保持原始数据格式
      const exportData = {
        code: originalData?.code || 200,
        msg: originalData?.msg || "导出成功",
        data: {
          data: [], // 节点数据
          links: [] // 连接线数据
        }
      };

      // 如果原始数据有其他字段，也保留
      if (originalData) {
        Object.keys(originalData).forEach((key) => {
          if (key !== "data" && key !== "code" && key !== "msg") {
            exportData[key] = originalData[key];
          }
        });

        // 保持data字段中除了data和links之外的其他属性
        if (originalData.data) {
          Object.keys(originalData.data).forEach((key) => {
            if (key !== "data" && key !== "links") {
              exportData.data[key] = originalData.data[key];
            }
          });
        }
      }

      // 创建节点ID到原始数据的映射
      const originalNodeMap = new Map();
      const originalLinkMap = new Map();

      // 正确访问原始数据结构
      let originalNodes = [];
      let originalLinks = [];

      if (originalData) {
        // 处理嵌套的数据结构 (如mock数据中的 data.data)
        if (originalData.data && originalData.data.data) {
          originalNodes = originalData.data.data;
          originalLinks = originalData.data.links || [];
        } else if (originalData.nodes) {
          originalNodes = originalData.nodes;
          originalLinks = originalData.links || [];
        }
      }

      originalNodes.forEach((node) => {
        originalNodeMap.set(node.name || node.id, node); // 使用name或id作为key
      });

      originalLinks.forEach((link) => {
        const linkKey = this.getLinkKey(link);
        originalLinkMap.set(linkKey, link);
      });

      // 处理节点数据
      exportData.data.data = nodes.map((node) => {
        const originalNode = originalNodeMap.get(node.id) || originalNodeMap.get(node.name) || {};

        // 计算转换后的pos坐标
        const convertedPos = this.convertPositionToOriginal({ x: node.x, y: node.y }, posLayoutScale, canvasWidth, canvasHeight);

        return {
          ...originalNode, // 保持原始节点的所有属性
          id: node.id,
          name: node.name,
          des: node.des,
          category: node.category,
          pos: convertedPos,
          // 保持其他可能的属性
          attribute: node.attribute,
          symbolSize: node.symbolSize
        };
      });

      // 处理连接线数据
      exportData.data.links = links.map((link) => {
        const linkKey = this.getLinkKey(link);
        const originalLink = originalLinkMap.get(linkKey) || {};

        // 构建导出的连接线数据，优先使用当前连接线的重要属性
        const exportedLink = {
          // 首先设置原始属性作为基础
          ...originalLink,
          // 然后覆盖当前运行时的关键属性
          id: link.id,
          source: link.source.id || link.source,
          target: link.target.id || link.target,
          name: link.name,
          des: link.des,
          // 确保label属性使用当前的值，不被原始数据覆盖
          label: link.label || originalLink.label,
          // 保持其他可能的属性
          attribute: link.attribute || originalLink.attribute,
          lineStyle: link.lineStyle || originalLink.lineStyle
        };

        return exportedLink;
      });

      // 添加导出元数据
      exportData._exportMetadata = {
        exportTime: new Date().toISOString(),
        exportVersion: "1.0.0",
        posLayoutScale: posLayoutScale,
        nodeCount: exportData.data.data.length,
        linkCount: exportData.data.links.length
      };

      return exportData;
    } catch (error) {
      console.error("导出图谱数据时出错:", error);
      throw new Error("导出数据失败: " + error.message);
    }
  }

  /**
   * 将当前位置坐标转换为原始坐标
   * @param {Object} currentPos - 当前位置 {x, y}
   * @param {Number} posLayoutScale - 位置布局缩放比例
   * @param {Number} canvasWidth - 画布宽度
   * @param {Number} canvasHeight - 画布高度
   * @returns {Array} 原始位置坐标数组 [x, y]
   */
  static convertPositionToOriginal(currentPos, posLayoutScale = 1, canvasWidth = 800, canvasHeight = 600) {
    if (!currentPos || typeof currentPos.x !== "number" || typeof currentPos.y !== "number") {
      return [0, 0];
    }

    // 反向转换DataProcessor中的坐标计算
    // 原始转换公式:
    // const x = ((scaledPosX + 1) / 2) * this.width;
    // const y = ((scaledPosY + 1) / 2) * this.height;
    // scaledPosX = node.pos[0] * posLayoutScale;
    // scaledPosY = node.pos[1] * posLayoutScale;

    // 反向计算:
    // scaledPosX = (currentPos.x / canvasWidth) * 2 - 1
    // scaledPosY = (currentPos.y / canvasHeight) * 2 - 1
    // originalPosX = scaledPosX / posLayoutScale
    // originalPosY = scaledPosY / posLayoutScale

    const scaledPosX = (currentPos.x / canvasWidth) * 2 - 1;
    const scaledPosY = (currentPos.y / canvasHeight) * 2 - 1;

    const originalPosX = scaledPosX / posLayoutScale;
    const originalPosY = scaledPosY / posLayoutScale;

    // 保留适当的精度，避免浮点数精度问题
    return [Math.round(originalPosX * 10000) / 10000, Math.round(originalPosY * 10000) / 10000];
  }

  /**
   * 生成连接线的唯一键
   * @param {Object} link - 连接线对象
   * @returns {String} 连接线的唯一键
   */
  static getLinkKey(link) {
    const sourceId = link.source?.id || link.source;
    const targetId = link.target?.id || link.target;
    const name = link.name || "";
    // 包含连接线名称以确保具有相同源-目标节点对的连接线的唯一性
    return `${sourceId}-${targetId}-${name}`;
  }

  /**
   * 下载JSON数据为文件
   * @param {Object} data - 要下载的数据
   * @param {String} filename - 文件名（不包含扩展名）
   */
  static downloadAsJson(data, filename = "graph-data") {
    try {
      const jsonStr = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonStr], { type: "application/json" });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `${filename}-${this.getFormattedDateTime()}.json`;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      URL.revokeObjectURL(url);

      console.log(`图谱数据已导出为: ${link.download}`);
    } catch (error) {
      console.error("下载JSON文件时出错:", error);
      throw new Error("下载文件失败: " + error.message);
    }
  }

  /**
   * 获取格式化的当前日期时间字符串
   * @returns {String} 格式化的日期时间字符串 (YYYY-MM-DD-HH-mm-ss)
   */
  static getFormattedDateTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;
  }

  /**
   * 验证导出数据的完整性
   * @param {Object} data - 要验证的数据
   * @returns {Object} 验证结果 {isValid: boolean, errors: Array}
   */
  static validateExportData(data) {
    const errors = [];

    if (!data) {
      errors.push("数据不能为空");
      return { isValid: false, errors };
    }

    if (!Array.isArray(data.data.data)) {
      errors.push("data.data必须是数组");
    } else if (data.data.data.length === 0) {
      errors.push("data.data数组不能为空");
    }

    if (!Array.isArray(data.data.links)) {
      errors.push("data.links必须是数组");
    }

    // 验证节点数据
    data.data.data?.forEach((node, index) => {
      if (!node.id) {
        errors.push(`节点${index}: 缺少id属性`);
      }
      if (!node.name) {
        errors.push(`节点${index}: 缺少name属性`);
      }
      if (!Array.isArray(node.pos) || node.pos.length !== 2) {
        errors.push(`节点${index}: pos属性必须是长度为2的数组`);
      }
    });

    // 验证连接线数据
    data.data.links?.forEach((link, index) => {
      if (!link.source) {
        errors.push(`连接线${index}: 缺少source属性`);
      }
      if (!link.target) {
        errors.push(`连接线${index}: 缺少target属性`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 创建数据导出摘要
   * @param {Object} data - 导出的数据
   * @returns {Object} 数据摘要
   */
  static createExportSummary(data) {
    const summary = {
      totalNodes: data.data.data?.length || 0,
      totalLinks: data.data.links?.length || 0,
      categories: {},
      exportTime: data._exportMetadata?.exportTime || new Date().toISOString()
    };

    // 统计各类别节点数量
    data.data.data?.forEach((node) => {
      const category = node.category || "unknown";
      summary.categories[category] = (summary.categories[category] || 0) + 1;
    });

    return summary;
  }
}
