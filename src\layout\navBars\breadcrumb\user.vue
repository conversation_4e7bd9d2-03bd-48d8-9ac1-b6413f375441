<template>
  <div
    class="layout-navbars-breadcrumb-user"
    :style="{ flex: layoutUserFlexNum }"
  >
    <div
      class="layout-navbars-breadcrumb-user-icon"
      v-if="getThemeConfig.isNotice"
    >
      <el-popover
        placement="bottom"
        trigger="click"
        v-model="isShowUserNewsPopover"
        :width="300"
        popper-class="el-popover-pupop-user-news"
      >
        <el-badge
          :value="getNewsList.length"
          :max="20"
          :hidden="!getNewsList.length"
          @click.stop="isShowUserNewsPopover = !isShowUserNewsPopover"
          slot="reference"
        >
          <i
            class="el-icon-bell"
            title="消息"
          ></i>
        </el-badge>
        <transition name="el-zoom-in-top">
          <UserNews v-show="isShowUserNewsPopover" />
        </transition>
      </el-popover>
    </div>
    <div
      class="layout-navbars-breadcrumb-user-icon"
      @click="onLayoutSettingClick"
    >
      <i
        class="el-icon-setting"
        title="布局配置"
      ></i>
    </div>
    <el-dropdown
      :show-timeout="70"
      :hide-timeout="50"
      @command="onDropdownCommand"
      v-if="getThemeConfig.isUserInfo"
    >
      <span class="layout-navbars-breadcrumb-user-link">
        <el-avatar
          class="layout-navbars-breadcrumb-user-link-photo mr-1"
          :size="25"
          icon="el-icon-user-solid"
        ></el-avatar>
        {{ getUserInfos.username === "" ? "test" : getUserInfos.username }}
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="fullScreen">{{ isFullScreen ? "退出全屏" : "全屏" }}</el-dropdown-item>
        <el-dropdown-item
          command="contentFullScreen"
          v-show="!isFullScreen"
          >内容区全屏</el-dropdown-item
        >
        <el-dropdown-item
          divided
          command="logOut"
          >退出登录</el-dropdown-item
        >
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { fullScreen, contentFullScreen } from "@/utils/libs.js";
import { Local, Session } from "@/utils/storage.js";
import UserNews from "@/layout/navBars/breadcrumb/userNews.vue";
export default {
  name: "layoutBreadcrumbUser",
  components: { UserNews },
  data() {
    return {
      isShowUserNewsPopover: false,
      isFullScreen: false
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    // 获取用户信息
    getUserInfos() {
      return this.$store.state.userInfos.userInfos;
    },
    // 获取用户信息
    getNewsList() {
      return this.$store.state.newsList.newsList;
    },
    // 设置弹性盒子布局 flex
    layoutUserFlexNum() {
      const { layout } = this.$store.state.themeConfig.themeConfig;
      let num = "";
      if (layout === "defaults" || layout === "vertical") num = 1;
      else num = null;
      return num;
    }
  },
  mounted() {
    fullScreen.listen(() => {
      this.isFullScreen = fullScreen.enable();
    });
    contentFullScreen.listen();
  },
  methods: {
    // 全屏
    onScreenClick(type) {
      type === "fullScreen" ? fullScreen.toggle() : contentFullScreen.toggle();
    },
    // 布局配置点击
    onLayoutSettingClick() {
      this.$bus.$emit("openSettingsDrawer");
    },
    // `dropdown 下拉菜单` 当前项点击
    onDropdownCommand(path) {
      switch (path) {
        case "logOut":
          setTimeout(() => {
            this.$msgbox({
              closeOnClickModal: false,
              closeOnPressEscape: false,
              title: "提示",
              message: "此操作将退出登录, 是否继续?",
              showCancelButton: true,
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              beforeClose: (action, instance, done) => {
                if (action === "confirm") {
                  instance.confirmButtonLoading = true;
                  instance.confirmButtonText = "退出中";
                  setTimeout(() => {
                    done();
                    setTimeout(() => {
                      instance.confirmButtonLoading = false;
                    }, 300);
                  }, 700);
                } else {
                  done();
                }
              }
            })
              .then(() => {
                // 清除缓存/token等
                this.$store.commit("userInfos/setToken", "");
                Local.clear();
                Session.clear();
                // 使用 reload 时，不需要调用 resetRoute() 重置路由
                window.location.reload();
              })
              .catch(() => {});
          }, 150);
          break;
        case "fullScreen":
          this.onScreenClick("fullScreen");
          break;
        case "contentFullScreen":
          this.onScreenClick("contentFullScreen");
          break;
        default:
          this.$router.push(path);
      }
    }
  }
};
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-user {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  &-link {
    height: 100%;
    display: flex;
    align-items: center;
    white-space: nowrap;
    &-photo {
      width: 25px;
      height: 25px;
      border-radius: 100%;
    }
  }
  > div:not(:last-child) {
    padding-right: 10px;
  }
  &-icon {
    display: flex;
    align-items: center;
    color: var(--nari-c-header);
    font-size: 20px;
    cursor: pointer;
    &:hover {
      i,
      .svg-icon {
        display: inline-block;
        animation: logoAnimation 0.3s ease-in-out;
      }
    }
    [class*=" el-icon-"],
    [class^="el-icon-"] {
      font-size: inherit;
    }
  }
  & ::v-deep .el-dropdown {
    color: var(--nari-c-header);
  }
  & ::v-deep .el-badge {
    display: flex;
    align-items: center;
  }
  & ::v-deep .el-badge__content {
    height: 18px;
    line-height: 14px;
  }
}
</style>
