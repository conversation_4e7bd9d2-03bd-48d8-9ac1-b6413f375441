<template>
  <div class="relative rounded-lg">
    <template v-if="data?.messageTag === 'GD_ASSISANCE_PLAN'">
      <el-collapse
        class="card-collapse"
        v-model="activeName"
        accordion
      >
        <el-collapse-item :name="data.title">
          <template slot="title">
            <TitleBar
              :title="data.title"
              :tips="data.tips"
              :success="data.success"
            />
          </template>
          <div class="collapse-content px-4 pb-2">
            <div class="reminder hidden">
              <ReminderItem
                v-for="(item, index) in data.reminder"
                :key="index"
                :text="item"
              />
            </div>
            <ContentItem
              class="pt-2"
              v-for="(item, index) in data.content"
              :key="index"
              :type="item.type"
              :data="item.data"
            />
          </div>
        </el-collapse-item>
      </el-collapse>
      <div
        class="reminder hidden"
        v-if="!activeName"
      >
        <ReminderItem
          v-for="(item, index) in data.reminder"
          :key="index"
          :text="item"
          class="px-2 pt-2"
        />
      </div>
    </template>
    <template v-else>
      <template v-for="(item, index) in iframes">
        <div
          v-if="item.type === 'text'"
          :key="index"
          class="answer-text-content mb-2"
        >
          {{ item.data.text }}
        </div>
        <div
          class="iframe-container relative w-full h-[300px] mt-2 shadow-lg"
          :key="`else_${index}`"
          v-else
        >
          <template v-if="showModal">
            <div
              class="flex justify-center items-center absolute"
              :class="item.type === 'ppt_play' ? 'w-full h-full bg-transparent top-0' : 'bottom-[10px] right-[10px]'"
            >
              <div
                class="text-white leading-none text-center bg-gray-500 p-2 rounded-md cursor-pointer opacity-50 hover:opacity-100"
                :class="item.type === 'ppt_play' ? 'text-sm' : 'text-lg'"
                @click="showDialog(item)"
              >
                <template v-if="item.type === 'ppt_play'"> 点击打开 </template>
                <template v-else>
                  <i
                    class="el-icon-zoom-in"
                    title="查看完整内容"
                  ></i>
                </template>
              </div>
            </div>
          </template>

          <iframe
            :ref="`iframeRef_${item.id}`"
            :src="item.src"
            :key="item.id"
            width="100%"
            height="100%"
            scrolling="no"
            frameborder="0"
            @load="handleIframeLoaded(item)"
          />
        </div>
      </template>
    </template>
    <el-dialog
      class="chat-dialog"
      title="预览"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      width="98%"
      top="0vh"
      custom-class="h-[95vh]"
    >
      <iframe
        ref="dialogIframeRef"
        :src="iframeURL"
        width="100%"
        height="100%"
        frameborder="0"
      />
    </el-dialog>
  </div>
</template>

<script>
import { objectToURLParams } from "@/utils/libs";
import TitleBar from "./components/TitleBar";
import ReminderItem from "./components/ReminderItem";
import ContentItem from "./components/ContentItem";
export default {
  name: "CustomContent",
  components: {
    TitleBar,
    ReminderItem,
    ContentItem
  },
  props: {
    data: {
      type: [Array, Object],
      default: () => []
    },
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      iframeURL: "",
      showModal: false,
      loadedIframes: 0, // 记录加载完成的iframe数量
      dialogVisible: false,
      activeName: ""
    };
  },
  computed: {
    iframes() {
      if (!this.data.length) return [];
      return this.data.map((item, index) => {
        const { type, data = {} } = item;
        const { DATA_REPORT_URL_PREFIX, KG_URL_PREFIX, KGDATASOURCE_URL_PREFIX, SVG_URL_PREFIX, PPT_URL_PREFIX } = window.$global;
        let iframeURL = "";
        if (type !== "text") {
          iframeURL = `${DATA_REPORT_URL_PREFIX}/${type}`;
          if (type === "kg") {
            const query = {
              version: "v1",
              query: true,
              equId: data?.source?.equId
            };
            iframeURL = `${KG_URL_PREFIX}?${objectToURLParams(query)}`;
          }
          if (type === "kgDataSource") {
            iframeURL = `${KGDATASOURCE_URL_PREFIX}`;
          }
          if (type === "svg") {
            iframeURL = `${SVG_URL_PREFIX}?${objectToURLParams(data?.source)}`;
          }
          if (type === "ppt_play") {
            iframeURL = `${PPT_URL_PREFIX}?full=true&preview=true`;
          }
        }
        return {
          ...item,
          id: `${this.item.id}_${index}`,
          src: iframeURL
        };
      });
    },
    isLast() {
      return (item) => item.id === this.chatList[this.chatList.length - 1]?.id;
    }
  },
  mounted() {
    this.activeName = this.data.title;
    window.addEventListener("message", this.handleMessage, false);
  },
  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
  },
  methods: {
    showDialog(item) {
      // window.parent.postMessage({ type: "chat", value: JSON.stringify(item) }, "*");
      this.dialogVisible = true;
      const { type, data } = item;
      this.iframeURL = item.src;
      this.$nextTick(() => {
        setTimeout(() => {
          const iframeWindow = this.$refs["dialogIframeRef"].contentWindow;
          iframeWindow?.postMessage(JSON.stringify({ type, value: data }), "*");
        }, 1000);
      });
    },
    handleIframeLoaded(item) {
      setTimeout(() => {
        this.showModal = true;
        const { id, type, data } = item;
        const iframeWindow = this.$refs[`iframeRef_${id}`][0].contentWindow;
        iframeWindow?.postMessage(JSON.stringify({ type, value: data }), "*");
      }, 2000);
    },
    sendDataToIframes() {
      const iframes = this.iframes.filter((item) => item.type !== "text");
      iframes.forEach((item) => {
        const { id, type, data } = item;
        const iframeWindow = this.$refs[`iframeRef_${id}`][0].contentWindow;
        iframeWindow?.postMessage(JSON.stringify({ type, value: data }), "*");
      });
    },
    handleMessage(event) {
      if (event.origin !== "https://your-iframe-content.com") return;
      console.log("Received message:", event.data);
    }
  }
};
</script>

<style lang="scss" scoped>
.chat-dialog {
  z-index: 99999999;
  ::v-deep .el-dialog__body {
    height: calc(100% - 54px);
    max-height: 100%;
    overflow: hidden;
  }
}
.card-collapse {
  border: none !important;

  ::v-deep .el-collapse-item {
    &__header,
    &__wrap {
      background-color: transparent;
    }
    &__header {
      border: none;
    }
  }
}
</style>
