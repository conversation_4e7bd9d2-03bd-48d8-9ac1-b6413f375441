/* 防止页面切换时，滚动条高度不变的问题（滚动条高度非滚动条滚动高度）
------------------------------- */
.el-scrollbar {
  overflow: hidden;
  position: relative;
  height: 100%;
}
.el-scrollbar__wrap {
  overflow: auto;
  overflow-x: hidden;
  max-height: 100%; /*防止页面切换时，滚动条高度不变的问题（滚动条高度非滚动条滚动高度）*/
  &.el-select-dropdown__wrap {
    max-height: 274px; /*修复Select 选择器高度问题*/
  }
  &.el-autocomplete-suggestion__wrap,
  &.el-table-filter__wrap {
    max-height: 280px;
  }
}

/* Button 按钮
------------------------------- */

/* Dialog 对话框
------------------------------- */
.el-overlay,
.el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-dialog {
    font-size: 18px;
  }
}
.el-dialog__body {
  min-height: 10vh;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Alert 警告
------------------------------- */
.el-alert--warning.is-light {
  border: 1px solid rgba(230, 162, 60, 0.3);
}
.el-alert--success.is-light {
  border: 1px solid rgba(103, 194, 58, 0.3);
}
.el-alert--info.is-light {
  border: 1px solid rgba(144, 147, 153, 0.3);
}
.el-alert--error.is-light {
  border: 1px solid rgba(245, 108, 108, 0.3);
}

/* Table 表格
------------------------------- */
.el-table-column--selection {
  .el-checkbox {
    margin-right: unset;
  }
}
.el-table::before,
.el-table--group::after,
.el-table--border::after {
  z-index: 99;
}

/* 下拉选择器/时间选择器滚动条
------------------------------- */
.el-picker-panel .el-scrollbar__wrap {
  overflow-x: scroll;
}

/* Alert 警告
------------------------------- */
.el-alert__title {
  word-break: break-all;
}

.content-full-screen-message {
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 0;
  padding: 5px;
  .el-message__icon {
    display: none;
  }
  .el-message__content {
    color: var(--nari-c-white);
  }
}

.el-tabs {
  font-size: 20px;
}

.el-drawer {
  font-size: 16px;
}
