import simpleGit from "simple-git";
import fs from "fs";
import path from "path";

const git = simpleGit();

/**
 * 拉取模板仓库到指定目录
 * @param {string} tempRepoDir 临时存储目录
 */
export async function fetchTemplateRepo(tempRepoDir) {
  const repoUrl = "http://10.100.1.74/public-web-resources/nari-views-template-repo.git";

  try {
    console.log("开始拉取远程模板仓库...");

    // 删除已有的临时目录（如果存在的话）
    if (fs.existsSync(tempRepoDir)) {
      fs.rmSync(tempRepoDir, { recursive: true });
    }

    // 克隆模板仓库到临时目录
    await git.clone(repoUrl, tempRepoDir);

    console.log("模板仓库拉取成功！");
  } catch (err) {
    console.error("拉取模板仓库失败：", err.message);
    throw err; // 抛出错误，通知调用者终止操作
  }
}

/**
 * 拉取模板并生成目标文件
 * @param {string} type - 模板类型
 * @param {string} targetPath - 目标路径（基于 src/views 的项目路径）
 * @param {string} tempRepoDir - 临时拉取的模板仓库路径
 */
export async function fetchTemplate(type, targetPath, tempRepoDir) {
  const templateDir = path.resolve(tempRepoDir, "src/template-repo", type); // 模板的根目录路径

  try {
    console.log(`准备生成模板 ${type} 到 ${targetPath}`);

    // 检查模板目录是否存在
    if (!fs.existsSync(templateDir)) {
      throw new Error(`模板 ${type} 不存在，请检查类型是否正确。\n期待的路径: ${templateDir}`);
    }

    // 确保目标目录存在（递归方式创建）
    fs.mkdirSync(path.dirname(targetPath), { recursive: true });

    // 执行完整模板目录的递归复制
    copyDirectory(templateDir, path.dirname(targetPath));

    console.log(`模板成功生成到 ${path.dirname(targetPath)}`);
  } catch (error) {
    console.error("模板生成失败：", error.message);
    throw error; // 抛出错误，通知调用者终止操作
  }
}

/**
 * 递归复制目录内容
 * @param {string} sourceDir - 源目录路径（需要复制的目录）
 * @param {string} destinationDir - 目标目录路径（复制到的位置）
 */
function copyDirectory(sourceDir, destinationDir) {
  // 确保目标目录存在
  fs.mkdirSync(destinationDir, { recursive: true });

  // 读取源目录内容
  const entries = fs.readdirSync(sourceDir, { withFileTypes: true }); // 获取目录中的所有文件和子目录

  for (const entry of entries) {
    const sourcePath = path.join(sourceDir, entry.name); // 源文件或目录路径
    const destinationPath = path.join(destinationDir, entry.name); // 目标文件或目录路径

    if (entry.isDirectory()) {
      // 如果是目录，则递归复制
      copyDirectory(sourcePath, destinationPath);
    } else if (entry.isFile()) {
      // 如果是文件，则复制文件
      fs.copyFileSync(sourcePath, destinationPath);
    }
  }
}
