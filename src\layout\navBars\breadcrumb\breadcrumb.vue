<template>
  <div
    class="layout-navbars-breadcrumb"
    :style="{ display: isShowBreadcrumb }"
  >
    <div class="layout-logo-title">{{ getThemeConfig.globalTitle }}</div>
    <el-breadcrumb
      class="layout-navbars-breadcrumb-hide"
      :style="{ display: isShowBreadcrumb }"
    >
      <transition-group
        name="breadcrumb"
        mode="out-in"
      >
        <el-breadcrumb-item
          v-for="(v, k) in breadcrumbList"
          :key="v.path"
        >
          <span
            v-if="k === breadcrumbList.length - 1"
            class="layout-navbars-breadcrumb-span"
          >
            <i
              :class="v.meta.icon"
              class="layout-navbars-breadcrumb-iconfont"
              v-if="getThemeConfig.isBreadcrumbIcon"
            ></i
            >{{ v.meta.title }}
          </span>
          <a
            v-else
            @click.prevent="onBreadcrumbClick(v)"
          >
            <i
              :class="v.meta.icon"
              class="layout-navbars-breadcrumb-iconfont"
              v-if="getThemeConfig.isBreadcrumbIcon"
            ></i
            >{{ v.meta.title }}
          </a>
        </el-breadcrumb-item>
      </transition-group>
    </el-breadcrumb>
  </div>
</template>

<script>
export default {
  name: "layoutBreadcrumb",
  data() {
    return {
      breadcrumbList: []
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    // 动态设置顶部、横向布局不显示
    isShowBreadcrumb() {
      const { layout, isBreadcrumb } = this.getThemeConfig;
      if (layout !== "defaults") {
        return "none";
      } else {
        return isBreadcrumb ? "" : "none";
      }
    }
  },
  mounted() {
    this.initRouteSplit(this.$route.path);
  },
  methods: {
    // breadcrumb 当前项点击时
    onBreadcrumbClick(v) {
      const { redirect, path } = v;
      if (redirect) this.$router.push(redirect);
      else this.$router.push(path);
    },
    // 当前路由分割处理
    initRouteSplit(path) {
      const breadcrumbListGet = this.$store.getters["routesList/breadcrumbListGet"];
      this.breadcrumbList = breadcrumbListGet[path];
    }
  },
  // 监听路由的变化
  watch: {
    $route: {
      handler(newVal) {
        this.initRouteSplit(newVal.path);
      },
      deep: true
    }
  }
};
</script>

<style scoped lang="scss">
.layout-logo-title {
  font-size: 22px;
  line-height: 1;
  color: var(--nari-c-white);
  padding-right: 20px;
}
.layout-navbars-breadcrumb {
  display: flex;
  align-items: center;
  flex: 1;
  .layout-navbars-breadcrumb-icon {
    margin-right: 15px;
    color: var(--nari-c-header);
    opacity: 0.8;
    cursor: pointer;
    &:hover {
      opacity: 1;
    }
  }
  .layout-navbars-breadcrumb-span {
    color: var(--nari-c-header);
    opacity: 0.7;
  }
  .layout-navbars-breadcrumb-iconfont {
    font-size: 14px;
    margin-right: 5px;
  }
}
</style>
