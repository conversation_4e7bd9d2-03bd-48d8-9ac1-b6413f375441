@import "variable.scss";

// 工具栏
.toolbar {
  height: 40px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  background-color: $bg-dark-accent;
  border-bottom: 1px solid $border-dark;

  button {
    margin-right: 10px;
    padding: 5px 10px;
    border: 1px solid $border-dark;
    background-color: $bg-dark; // 使用浅色背景
    color: $text-primary;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background-color: $bg-dark-hover; // 悬停时更浅
    }
  }
}

// 图例
.legend-container {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  margin: 0 10px;
  padding: 0 5px;
  background-color: rgba($bg-dark, 0.9); // 使用浅色背景
  border-radius: 4px;
  border: 1px solid $border-dark;
  height: 30px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 12px;
  white-space: nowrap;
}

.legend-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.legend-text {
  color: $text-primary; // 主要文字颜色
  font-size: 12px;
}

// 搜索
.search-container {
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 28px;

  .search-input-wrapper {
    position: relative;
    width: 240px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    height: 28px;
  }

  .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: $text-placeholder; // 图标使用占位文字颜色
    z-index: 1;
    font-size: 14px;
    height: 14px;
    line-height: 1;
    display: flex;
    align-items: center;
  }

  input {
    width: 100%;
    height: 28px;
    padding: 0 10px 0 36px;
    border: 1px solid $border-dark;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    outline: none;
    font-size: 13px;
    transition: all 0.3s;
    background-color: $bg-dark; // 输入框背景
    color: $text-primary; // 输入文字颜色
    box-sizing: border-box;
    line-height: 28px;

    &::placeholder {
      color: $text-placeholder;
    }

    &:focus {
      border-color: $highlight-primary;
      box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
    }
  }

  button {
    // 这个button是搜索按钮
    height: 28px;
    min-width: 60px;
    background-color: $highlight-primary;
    color: $text-on-highlight;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    padding: 0 12px;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    line-height: 28px;

    &:hover {
      background-color: $highlight-primary-hover;
    }
  }

  // 搜索结果
  .search-results {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    background-color: $bg-dark; // 搜索结果背景
    border: 1px solid $border-dark;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 $shadow-dark; // 浅色阴影
    z-index: 999;
    padding: 6px 0;
  }

  .search-result-item {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    border-bottom: 1px solid $bg-dark-hover; // 分割线
    color: $text-primary;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: $bg-dark-hover; // 悬停背景
    }
  }

  .search-result-id {
    color: $text-secondary; // 次要文字颜色
    font-size: 12px;
    margin-right: 10px;
  }

  .search-result-name {
    font-weight: 500;
    flex-grow: 1;
    color: $text-primary;
  }

  .search-result-color {
    width: 10px;
    height: 10px;
    min-width: 10px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .highlight {
    background-color: #ffdd00; // 高亮颜色可以保持黄色，或者选择一个浅色系强调色
    padding: 0 2px;
    border-radius: 2px;
    font-weight: bold;
    color: #333; // 黄色背景下深色文字更清晰
    text-shadow: none; // 浅色背景下通常不需要文字阴影
  }

  .no-results {
    padding: 10px;
    text-align: center;
    color: $text-placeholder; // 无结果提示文字
  }

  .search-result-section {
    border-bottom: 1px solid rgba($border-dark, 0.5); // 分区底部边框
    padding-bottom: 4px;
    margin-bottom: 4px;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }

  .search-result-section-title {
    padding: 4px 12px;
    font-size: 12px;
    color: $text-secondary; // 分区标题文字
    background-color: rgba($bg-dark-accent, 0.5); // 分区标题背景
    font-weight: 500;
  }

  .search-result-line {
    width: 16px;
    height: 3px;
    border-radius: 0;
    margin-top: 8px;
  }

  .search-result-endpoints {
    display: block;
    font-size: 10px;
    opacity: 0.8;
    margin-top: 2px;
    color: $text-secondary; // 端点信息文字颜色
  }
}
