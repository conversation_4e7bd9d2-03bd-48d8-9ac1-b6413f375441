// Guangdong Province major cities geo coordinates and knowledge graph test data
export const mockGraphData = {
  nodes: [
    {
      id: "guangzhou",
      name: "Guangzhou",
      geo: [113.264385, 23.129112],
      size: 20,
      color: "#ff6b6b",
      type: "city",
      info: "Provincial capital, economic center"
    },
    {
      id: "shenzhen",
      name: "Shenzhen",
      geo: [114.057868, 22.543099],
      size: 18,
      color: "#4ecdc4",
      type: "city",
      info: "Special Economic Zone, Tech innovation center"
    },
    {
      id: "dongguan",
      name: "Dongguan",
      geo: [113.746262, 23.046237],
      size: 15,
      color: "#45b7d1",
      type: "city",
      info: "Manufacturing base"
    },
    {
      id: "foshan",
      name: "<PERSON>osh<PERSON>",
      geo: [113.122717, 23.028762],
      size: 15,
      color: "#96ceb4",
      type: "city",
      info: "Manufacturing hub"
    },
    {
      id: "zhuhai",
      name: "Zhuhai",
      geo: [113.553986, 22.224979],
      size: 14,
      color: "#feca57",
      type: "city",
      info: "Special Economic Zone, Tourist city"
    },
    {
      id: "zhongshan",
      name: "Zhongshan",
      geo: [113.382391, 22.521113],
      size: 14,
      color: "#ff9ff3",
      type: "city",
      info: "Historical and cultural city"
    },
    {
      id: "huizhou",
      name: "Huizhou",
      geo: [114.412599, 23.079404],
      size: 13,
      color: "#54a0ff",
      type: "city",
      info: "Hakka cultural city"
    },
    {
      id: "jiangmen",
      name: "Jiangmen",
      geo: [113.094942, 22.590431],
      size: 13,
      color: "#48dbfb",
      type: "city",
      info: "Overseas Chinese hometown"
    },
    {
      id: "zhaoqing",
      name: "Zhaoqing",
      geo: [112.472529, 23.051546],
      size: 12,
      color: "#a29bfe",
      type: "city",
      info: "Historical and cultural city"
    },
    {
      id: "shantou",
      name: "Shantou",
      geo: [116.708463, 23.370784],
      size: 13,
      color: "#fd79a8",
      type: "city",
      info: "Special Economic Zone, Chaoshan culture center"
    },
    {
      id: "meizhou",
      name: "Meizhou",
      geo: [116.117582, 24.299112],
      size: 11,
      color: "#e17055",
      type: "city",
      info: "Capital of Hakka culture"
    },
    {
      id: "shaoguan",
      name: "Shaoguan",
      geo: [113.591544, 24.801322],
      size: 11,
      color: "#fdcb6e",
      type: "city",
      info: "Northern Guangdong gateway"
    },
    {
      id: "qingyuan",
      name: "Qingyuan",
      geo: [113.051227, 23.685022],
      size: 11,
      color: "#00d2d3",
      type: "city",
      info: "Eco-tourism city"
    },
    {
      id: "zhanjiang",
      name: "Zhanjiang",
      geo: [110.358977, 21.274898],
      size: 12,
      color: "#6c5ce7",
      type: "city",
      info: "Harbor city"
    },
    {
      id: "maoming",
      name: "Maoming",
      geo: [110.919229, 21.659751],
      size: 11,
      color: "#00b894",
      type: "city",
      info: "Petrochemical industrial city"
    },
    {
      id: "yangjiang",
      name: "Yangjiang",
      geo: [111.975107, 21.859222],
      size: 10,
      color: "#0984e3",
      type: "city",
      info: "Coastal city"
    },
    // Industry nodes
    {
      id: "tech-industry",
      name: "Tech Industry",
      geo: [113.9, 22.8],
      size: 16,
      color: "#e74c3c",
      type: "industry"
    },
    {
      id: "manufacturing",
      name: "Manufacturing",
      geo: [113.5, 23.0],
      size: 16,
      color: "#3498db",
      type: "industry"
    },
    {
      id: "finance",
      name: "Finance",
      geo: [114.0, 22.6],
      size: 15,
      color: "#f39c12",
      type: "industry"
    },
    {
      id: "tourism",
      name: "Tourism",
      geo: [113.2, 22.3],
      size: 14,
      color: "#27ae60",
      type: "industry"
    }
  ],
  links: [
    // Economic connections between cities
    { source: "guangzhou", target: "shenzhen", value: 10, type: "economic" },
    { source: "guangzhou", target: "foshan", value: 9, type: "economic" },
    { source: "guangzhou", target: "dongguan", value: 8, type: "economic" },
    { source: "shenzhen", target: "dongguan", value: 8, type: "economic" },
    { source: "shenzhen", target: "huizhou", value: 6, type: "economic" },
    { source: "foshan", target: "zhongshan", value: 6, type: "economic" },
    { source: "zhuhai", target: "zhongshan", value: 5, type: "economic" },
    { source: "zhuhai", target: "jiangmen", value: 5, type: "economic" },
    { source: "guangzhou", target: "qingyuan", value: 5, type: "economic" },
    { source: "guangzhou", target: "zhaoqing", value: 4, type: "economic" },
    { source: "shantou", target: "meizhou", value: 4, type: "cultural" },
    { source: "shaoguan", target: "qingyuan", value: 3, type: "economic" },
    { source: "zhanjiang", target: "maoming", value: 4, type: "economic" },
    { source: "maoming", target: "yangjiang", value: 3, type: "economic" },

    // 产业联系
    { source: "guangzhou", target: "tech-industry", value: 8, type: "industry" },
    { source: "shenzhen", target: "tech-industry", value: 10, type: "industry" },
    { source: "dongguan", target: "manufacturing", value: 9, type: "industry" },
    { source: "foshan", target: "manufacturing", value: 8, type: "industry" },
    { source: "guangzhou", target: "finance", value: 9, type: "industry" },
    { source: "shenzhen", target: "finance", value: 10, type: "industry" },
    { source: "zhuhai", target: "tourism", value: 7, type: "industry" },
    { source: "zhaoqing", target: "tourism", value: 6, type: "industry" },
    { source: "qingyuan", target: "tourism", value: 6, type: "industry" },

    // 产业之间的关联
    { source: "tech-industry", target: "manufacturing", value: 7, type: "correlation" },
    { source: "tech-industry", target: "finance", value: 8, type: "correlation" },
    { source: "finance", target: "tourism", value: 5, type: "correlation" }
  ]
};

// Map configuration options
export const mapOptions = {
  title: {
    text: "Guangdong Province Knowledge Graph",
    left: "center",
    top: 20,
    textStyle: {
      fontSize: 24,
      fontWeight: "bold",
      color: "#ffffff",
      textShadowColor: "rgba(0, 0, 0, 0.8)",
      textShadowBlur: 5,
      textShadowOffsetX: 2,
      textShadowOffsetY: 2
    }
  },
  tooltip: {
    show: true,
    trigger: "item",
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    borderColor: "#3282b8",
    borderWidth: 1,
    textStyle: {
      color: "#ffffff"
    },
    formatter: (params) => {
      return params.name;
    }
  }
};
