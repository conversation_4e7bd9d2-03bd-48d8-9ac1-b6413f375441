<template>
  <div
    class="knowledge-graph-map"
    ref="containerRef"
  >
    <div
      class="map-container"
      ref="mapRef"
    ></div>
    <svg
      class="graph-container"
      ref="graphRef"
    ></svg>
  </div>
</template>

<script>
import * as echarts from "echarts";
import * as d3 from "d3";
import guangdongGeoJson from "./assets/geo/guangdong.json";
import { debugKnowledgeGraphMap } from "./test-debug.js";

export default {
  name: "KnowledgeGraphMap",
  props: {
    graphData: {
      type: Object,
      default: () => ({
        nodes: [],
        links: []
      })
    },
    mapOptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null,
      svg: null,
      simulation: null,
      minNodeDistance: 30, // 节点间最小距离（像素）
      nodePositions: new Map(), // 缓存节点位置
      lastMapTransform: null // 缓存地图变换状态
    };
  },
  watch: {
    graphData: {
      deep: true,
      handler() {
        this.updateGraph();
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap();
      // 图谱初始化现在在 initMap 中的地图渲染完成后调用
    });
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    if (this.simulation) {
      this.simulation.stop();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    initMap() {
      try {
        console.log("Initializing map...");

        // 确保容器存在
        if (!this.$refs.mapRef) {
          throw new Error("Map container not found");
        }

        this.chart = echarts.init(this.$refs.mapRef);
        console.log("ECharts instance created:", this.chart);

        // Register Guangdong map with imported GeoJSON data
        console.log("Registering map data...");
        echarts.registerMap("guangdong", guangdongGeoJson);

        const option = {
          backgroundColor: "#1a1a2e",
          tooltip: {
            trigger: "item",
            formatter: "{b}"
          },
          geo: {
            map: "guangdong",
            roam: true,
            zoom: 1.2,
            center: [113.3, 23.0],
            label: {
              show: true,
              fontSize: 12,
              color: "#ffffff"
            },
            itemStyle: {
              areaColor: "#16213e",
              borderColor: "#0f4c75",
              borderWidth: 2,
              shadowBlur: 10,
              shadowColor: "rgba(15, 76, 117, 0.5)"
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 12,
                color: "#000"
              },
              itemStyle: {
                areaColor: "#0f4c75",
                borderColor: "#3282b8",
                borderWidth: 3,
                shadowBlur: 20,
                shadowColor: "rgba(50, 130, 184, 0.8)"
              }
            },
            select: {
              itemStyle: {
                areaColor: "#bdd7e7"
              }
            }
          },
          ...this.mapOptions
        };

        console.log("Setting map option:", option);
        this.chart.setOption(option);

        // 强制重绘
        this.chart.resize();

        console.log("Map option set successfully");

        // Listen to map zoom and pan events
        this.chart.on("georoam", this.onMapTransform);

        // Listen to window resize
        window.addEventListener("resize", this.handleResize);

        // 等待地图渲染完成
        setTimeout(() => {
          console.log("Map initialization completed, initializing graph...");
          this.initGraph();
        }, 500);
      } catch (error) {
        console.error("Map initialization failed:", error);
        this.$message && this.$message.error("Map initialization failed: " + error.message);
      }
    },

    initGraph() {
      if (!this.graphData || !this.graphData.nodes || this.graphData.nodes.length === 0) {
        console.log("No graph data available, skipping graph initialization");
        return;
      }

      const width = this.$refs.containerRef.clientWidth;
      const height = this.$refs.containerRef.clientHeight;

      this.svg = d3.select(this.$refs.graphRef).attr("width", width).attr("height", height);

      // Create zoom behavior
      const zoom = d3
        .zoom()
        .scaleExtent([0.1, 10])
        .on("zoom", (event) => {
          this.svg.select(".graph-group").attr("transform", event.transform);
        });

      this.svg.call(zoom);

      const g = this.svg.append("g").attr("class", "graph-group");

      // Create links
      g.append("g")
        .attr("class", "links")
        .selectAll("line")
        .data(this.graphData.links || [])
        .enter()
        .append("line")
        .attr("stroke", "#999")
        .attr("stroke-opacity", 0.6)
        .attr("stroke-width", (d) => Math.sqrt(d.value || 1));

      // 预处理节点位置，避免重叠
      const processedNodes = this.processNodePositions(this.graphData.nodes);

      console.log("Processed nodes:", processedNodes.length);

      // Create nodes
      const node = g
        .append("g")
        .attr("class", "nodes")
        .selectAll("g")
        .data(processedNodes)
        .enter()
        .append("g")
        .call(d3.drag().on("start", this.dragstarted).on("drag", this.dragged).on("end", this.dragended));

      // Add node circles
      node
        .append("circle")
        .attr("r", (d) => d.size || 10)
        .attr("fill", (d) => d.color || "#69b3a2")
        .attr("stroke", "#fff")
        .attr("stroke-width", 2)
        .style("filter", "drop-shadow(0 0 5px rgba(255, 255, 255, 0.5))");

      // Add node labels
      node
        .append("text")
        .text((d) => d.name)
        .attr("x", 0)
        .attr("y", -15)
        .attr("text-anchor", "middle")
        .attr("font-size", "12px")
        .attr("fill", "#ffffff")
        .style("text-shadow", "1px 1px 2px rgba(0, 0, 0, 0.8)");

      // Create force simulation
      this.simulation = d3
        .forceSimulation(processedNodes)
        .force(
          "link",
          d3
            .forceLink(this.graphData.links || [])
            .id((d) => d.id)
            .distance(50)
        )
        .force("charge", d3.forceManyBody().strength(-300))
        .force("center", d3.forceCenter(width / 2, height / 2))
        .force(
          "collision",
          d3.forceCollide().radius((d) => (d.size || 10) + 5)
        ); // 添加碰撞检测

      this.simulation.on("tick", () => {
        this.updateGraphPosition();
      });

      // 初始化完成后立即更新一次位置
      setTimeout(() => {
        this.updateGraphPosition();
      }, 100);
    },

    // 处理地图变换事件
    onMapTransform() {
      // 获取当前地图变换状态
      const currentTransform = this.getMapTransform();
      this.lastMapTransform = currentTransform;

      // 更新图谱位置
      this.updateGraphPosition();
    },

    // 获取地图变换状态
    getMapTransform() {
      if (!this.chart) return null;

      try {
        // 尝试获取当前地图的实际变换状态
        const model = this.chart.getModel();
        const geoModel = model.getComponent("geo", 0);

        if (geoModel) {
          const zoom = geoModel.get("zoom") || 1;
          const center = geoModel.get("center") || [113.3, 23.0];

          return { zoom, center };
        }

        // 备用方案：使用选项中的值
        const option = this.chart.getOption();
        const geo = option.geo && option.geo[0];
        if (geo) {
          return {
            zoom: geo.zoom || 1,
            center: geo.center || [113.3, 23.0]
          };
        }
      } catch (error) {
        console.warn("Failed to get map transform:", error);
      }

      return { zoom: 1, center: [113.3, 23.0] };
    },

    // 处理节点位置，避免重叠
    processNodePositions(nodes) {
      if (!this.chart || !nodes || nodes.length === 0) {
        console.log("Chart not ready or no nodes, returning original nodes");
        return nodes || [];
      }

      const processedNodes = nodes.map((node) => ({ ...node }));
      const positions = new Map();

      // 第一遍：转换地理坐标到屏幕坐标
      processedNodes.forEach((node) => {
        if (node.geo && Array.isArray(node.geo) && node.geo.length === 2) {
          try {
            const screenCoord = this.chart.convertToPixel("geo", node.geo);
            if (screenCoord && Array.isArray(screenCoord) && screenCoord.length === 2) {
              positions.set(node.id, {
                x: screenCoord[0],
                y: screenCoord[1],
                original: true
              });
              console.log(`Node ${node.name} positioned at:`, screenCoord);
            } else {
              console.warn(`Failed to convert coordinates for node ${node.name}:`, node.geo);
            }
          } catch (error) {
            console.error(`Error converting coordinates for node ${node.name}:`, error);
          }
        }
      });

      console.log(`Converted ${positions.size} nodes to screen coordinates`);

      // 第二遍：检测并解决重叠
      const resolvedPositions = this.resolveOverlaps(positions);

      // 第三遍：应用处理后的位置
      processedNodes.forEach((node) => {
        const pos = resolvedPositions.get(node.id);
        if (pos) {
          node.fx = pos.x;
          node.fy = pos.y;
          // 缓存位置信息
          this.nodePositions.set(node.id, pos);
        }
      });

      return processedNodes;
    },

    // 解决节点重叠问题
    resolveOverlaps(positions) {
      const resolved = new Map(positions);
      const posArray = Array.from(resolved.entries());

      for (let i = 0; i < posArray.length; i++) {
        for (let j = i + 1; j < posArray.length; j++) {
          const [id1, pos1] = posArray[i];
          const [id2, pos2] = posArray[j];

          const dx = pos1.x - pos2.x;
          const dy = pos1.y - pos2.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < this.minNodeDistance) {
            // 计算需要移动的距离
            const moveDistance = (this.minNodeDistance - distance) / 2;
            const angle = Math.atan2(dy, dx);

            // 移动两个节点
            const moveX = Math.cos(angle) * moveDistance;
            const moveY = Math.sin(angle) * moveDistance;

            pos1.x += moveX;
            pos1.y += moveY;
            pos2.x -= moveX;
            pos2.y -= moveY;

            resolved.set(id1, pos1);
            resolved.set(id2, pos2);
          }
        }
      }

      return resolved;
    },

    updateGraphPosition() {
      if (!this.chart || !this.svg) return;

      const nodes = this.svg.selectAll(".nodes g");
      const links = this.svg.selectAll(".links line");

      // 检查地图是否发生了变换
      const currentTransform = this.getMapTransform();
      const transformChanged =
        !this.lastMapTransform ||
        currentTransform.zoom !== this.lastMapTransform.zoom ||
        currentTransform.center[0] !== this.lastMapTransform.center[0] ||
        currentTransform.center[1] !== this.lastMapTransform.center[1];

      // 如果地图变换了，重新计算所有有地理坐标的节点位置
      if (transformChanged) {
        const self = this;
        nodes.each(function (d) {
          if (d.geo) {
            const screenCoord = self.chart.convertToPixel("geo", d.geo);
            if (screenCoord) {
              // 检查是否需要避免重叠
              const adjustedCoord = self.adjustPositionForOverlap(screenCoord, d.id);
              d.fx = adjustedCoord.x;
              d.fy = adjustedCoord.y;

              // 更新缓存
              self.nodePositions.set(d.id, adjustedCoord);
            }
          }
        });

        this.lastMapTransform = currentTransform;
      }

      // Update node positions
      nodes.attr("transform", (d) => `translate(${d.x || d.fx || 0},${d.y || d.fy || 0})`);

      // Update link positions
      links
        .attr("x1", (d) => d.source.x || d.source.fx || 0)
        .attr("y1", (d) => d.source.y || d.source.fy || 0)
        .attr("x2", (d) => d.target.x || d.target.fx || 0)
        .attr("y2", (d) => d.target.y || d.target.fy || 0);
    },

    // 调整位置以避免重叠
    adjustPositionForOverlap(screenCoord, currentNodeId) {
      let adjustedX = screenCoord[0];
      let adjustedY = screenCoord[1];

      // 检查与其他节点的距离
      for (const [nodeId, pos] of this.nodePositions.entries()) {
        if (nodeId === currentNodeId) continue;

        const dx = adjustedX - pos.x;
        const dy = adjustedY - pos.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < this.minNodeDistance) {
          // 计算新位置
          const angle = Math.atan2(dy, dx);
          const newDistance = this.minNodeDistance + 5; // 额外的缓冲距离

          adjustedX = pos.x + Math.cos(angle) * newDistance;
          adjustedY = pos.y + Math.sin(angle) * newDistance;
        }
      }

      return { x: adjustedX, y: adjustedY };
    },

    dragstarted(event, d) {
      if (!event.active) this.simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    },

    dragged(event, d) {
      d.fx = event.x;
      d.fy = event.y;
    },

    dragended(event, d) {
      if (!event.active) this.simulation.alphaTarget(0);
      if (!d.geo) {
        d.fx = null;
        d.fy = null;
      }
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
      if (this.svg) {
        const width = this.$refs.containerRef.clientWidth;
        const height = this.$refs.containerRef.clientHeight;
        this.svg.attr("width", width).attr("height", height);
        if (this.simulation) {
          this.simulation.force("center", d3.forceCenter(width / 2, height / 2));
          this.simulation.alpha(0.3).restart();
        }
      }
    },

    updateGraph() {
      if (!this.svg) return;

      // Clear existing content
      this.svg.selectAll(".graph-group").remove();

      // 清除缓存
      this.nodePositions.clear();
      this.lastMapTransform = null;

      // Reinitialize graph
      this.initGraph();
    },

    refresh() {
      this.handleResize();
      this.updateGraphPosition();
    },

    // 调试方法
    debug() {
      debugKnowledgeGraphMap.checkComponentState(this);
    },

    testCoordinates() {
      debugKnowledgeGraphMap.testAllNodesConversion(this);
    }
  }
};
</script>

<style scoped lang="scss">
.knowledge-graph-map {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .map-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  .graph-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;

    ::v-deep .nodes {
      pointer-events: all;
      cursor: pointer;

      g:hover {
        circle {
          filter: brightness(1.2);
        }
        text {
          font-weight: bold;
        }
      }
    }

    ::v-deep .links {
      pointer-events: none;
    }
  }
}
</style>
