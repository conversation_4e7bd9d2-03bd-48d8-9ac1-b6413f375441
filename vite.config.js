import { resolve } from "path";
import { createVuePlugin } from "vite-plugin-vue2";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import { defineConfig, loadEnv } from "vite";
// import legacy from "@vitejs/plugin-legacy";
import { wrapperEnv } from "./build/getEnv";
import { createProxy } from "./build/proxy";

export default defineConfig(({ mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
  const env = loadEnv(mode, process.cwd(), "");
  const viteEnv = wrapperEnv(env);
  return {
    // 项目部署在主域名的子文件使用,例如http://localhost:3000/myvite/。不填默认就是/
    base: viteEnv.VITE_APP_BASE_URL || "/",
    plugins: [
      createVuePlugin({
        jsx: true
      }),
      createSvgIconsPlugin({
        // 指定要缓存的文件夹
        iconDirs: [resolve(__dirname, "src/assets/svg")],
        // 指定symbolId格式
        symbolId: "icon-[name]"
      })
      // legacy({
      //   targets: ["chrome < 87"],
      //   renderLegacyChunks: true,
      //   additionalLegacyPolyfills: ["regenerator-runtime/runtime"],
      //   polyfills: [
      //     "es.symbol",
      //     "es.promise",
      //     "es.promise.finally",
      //     "es/map",
      //     "es/set",
      //     "es.array.filter",
      //     "es.array.for-each",
      //     "es.array.flat-map",
      //     "es.object.define-properties",
      //     "es.object.define-property",
      //     "es.object.get-own-property-descriptor",
      //     "es.object.get-own-property-descriptors",
      //     "es.object.keys",
      //     "es.object.to-string",
      //     "web.dom-collections.for-each",
      //     "esnext.global-this",
      //     "esnext.string.match-all"
      //   ],
      //   exclude: ["**/mock/*.json"]
      // })
    ],
    server: {
      port: viteEnv.VITE_PORT,
      host: "0.0.0.0",
      open: viteEnv.VITE_OPEN,
      proxy: createProxy(viteEnv.VITE_PROXY)
    },
    resolve: {
      alias: {
        "@": resolve(__dirname, "src")
      },
      extensions: [".mjs", ".js", ".mts", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    build: {
      outDir: mode === "production" ? "dist-prod" : mode === "test" ? "dist-test" : "dist-local",
      rollupOptions: {
        output: {
          chunkFileNames: "assets/js/[name]-[hash].js", // 引入文件名的名称
          entryFileNames: "assets/js/[name]-[hash].js", // 包的入口文件名称
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]", // 资源文件像 字体，图片等
          manualChunks(id) {
            if (id.includes("node_modules")) {
              return "vendor";
            }
          }
        }
      }
    }
  };
});
