const keepAliveNamesModule = {
  namespaced: true,
  state: {
    keepAliveNames: []
  },
  mutations: {
    // Add KeepAliveName
    addKeepAliveName(state, name) {
      !state.keepAliveNames.includes(name) && state.keepAliveNames.push(name);
    },
    // Remove KeepAliveName
    removeKeepAliveName(state, name) {
      state.keepAliveNames = state.keepAliveNames.filter((item) => item !== name);
    },
    // Set KeepAliveName
    setKeepAliveName(state, keepAliveName = []) {
      state.keepAliveNames = keepAliveName;
    }
  }
};

export default keepAliveNamesModule;
