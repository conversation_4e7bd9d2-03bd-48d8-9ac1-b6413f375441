// 深色主题
$bg-dark: #0c1633; // 背景
$bg-dark-accent: #1a2a4a; // 辅助背景
$bg-dark-hover: #0f2441; // 悬停背景
$border-dark: #334c7a; // 边框
$highlight-primary: #409eff; // 主高亮/按钮背景
$highlight-primary-hover: #66b1ff; // 主高亮悬停
$text-primary: #8ecaff; // 主要文字
$text-secondary: #e7e7e7; // 次要文字
$text-placeholder: #c0c4cc; // 占位文字/图标
$text-on-highlight: #ffffff; // 高亮背景上的文字
$shadow-dark: rgba(0, 0, 0, 0.1); // 阴影

// 图谱连接线颜色
$link-color-default: #9e9e9e; // 默认连接线颜色 - 保持灰色，在深色背景下效果好
$link-color-primary: #4caf50; // 主要连接线颜色 - 绿色
$link-color-secondary: #2196f3; // 次要连接线颜色 - 蓝色
$link-color-backup: #ff9800; // 备用连接线颜色 - 橙色
