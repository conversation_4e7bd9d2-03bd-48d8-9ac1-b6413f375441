<template>
  <div class="tabs-container">
    <el-tabs
      v-model="activeTab"
      type="card"
    >
      <el-tab-pane
        v-for="item in data.tabs"
        :key="item.name"
        :label="item.title"
        :name="item.name"
      >
        <span
          slot="label"
          v-if="item.icon"
        >
          <i :class="item.icon"></i>
          {{ item.title }}
        </span>
        {{ item.content }}
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: "TabsCard",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeTab: ""
    };
  },
  mounted() {
    this.activeTab = this.data.active || this.data.tabs[0].name;
  }
};
</script>
<style lang="scss" scoped></style>
