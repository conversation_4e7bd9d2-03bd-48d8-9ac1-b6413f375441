export class GraphicTools {
  nodes;
  edges;
  loadDataFromRelationGraph(graphInstance) {
    this.nodes = graphInstance.getNodes().map((n) => {
      return {
        id: n.id,
        childs: [],
        indexed: false,
        parentNode: null
      };
    });
    this.edges = graphInstance.getLinks().map((link) => {
      return {
        from: link.fromNode.id,
        to: link.toNode.id
      };
    });
  }
  buildIndexes(thisLevelNodes) {
    const nextLevelNodes = [];
    thisLevelNodes.forEach((node) => {
      this.edges.forEach((edge) => {
        if (edge.from === node.id) {
          const targetNode = this.getNodeById(edge.to);
          if (targetNode && !targetNode.indexed) {
            targetNode.indexed = true;
            targetNode.parentNode = node;
            node.childs.push(targetNode);
            nextLevelNodes.push(targetNode);
          }
        }
        if (edge.to === node.id) {
          const targetNode = this.getNodeById(edge.from);
          if (targetNode && !targetNode.indexed) {
            targetNode.indexed = true;
            targetNode.parentNode = node;
            node.childs.push(targetNode);
            nextLevelNodes.push(targetNode);
          }
        }
      });
    });
    if (nextLevelNodes.length > 0) this.buildIndexes(nextLevelNodes);
  }
  getNodeById(id) {
    return this.nodes.find((n) => n.id === id);
  }
  findMinPath(fromId, toId) {
    const fromNode = this.getNodeById(fromId);
    if (!fromNode) throw new Error("找不到起点!");
    const toNode = this.getNodeById(toId);
    if (!toNode) throw new Error("找不到终点!");
    this.buildIndexes([toNode]);
    const nodesInMinPath = this.getPath(fromNode, toNode, [fromNode]);
    if (nodesInMinPath) return nodesInMinPath.map((n) => n.id);
  }
  getPath(currentNode, targetNode, nodesInPath) {
    if (currentNode === targetNode) return nodesInPath.concat(targetNode);
    if (currentNode.parentNode) return this.getPath(currentNode.parentNode, targetNode, nodesInPath.concat(currentNode.parentNode));
  }
}
