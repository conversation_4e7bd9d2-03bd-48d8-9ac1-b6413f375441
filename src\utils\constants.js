export const CONVERSATION_ID_INFO = "conversationIdInfo";

export const WORKFLOW_RUNNING_STATUS = {
  Waiting: "waiting",
  Running: "running",
  Succeeded: "succeeded",
  Failed: "failed",
  Stopped: "stopped"
};

export const WORKFLOW_NODE_TYPE = {
  Start: "start",
  End: "end",
  Answer: "answer",
  LLM: "llm",
  KnowledgeRetrieval: "knowledge-retrieval",
  QuestionClassifier: "question-classifier",
  IfElse: "if-else",
  Code: "code",
  TemplateTransform: "template-transform",
  HttpRequest: "http-request",
  VariableAssigner: "variable-assigner",
  VariableAggregator: "variable-aggregator",
  Tool: "tool",
  ParameterExtractor: "parameter-extractor",
  Iteration: "iteration",
  Assigner: "assigner"
};

export const ICON_CONTAINER_BG_COLOR_MAP = {
  [WORKFLOW_NODE_TYPE.Start]: "bg-[#2970FF]",
  [WORKFLOW_NODE_TYPE.End]: "bg-[#F79009]",
  [WORKFLOW_NODE_TYPE.Answer]: "bg-[#F79009]",
  [WORKFLOW_NODE_TYPE.LLM]: "bg-[#6172F3]",
  [WORKFLOW_NODE_TYPE.KnowledgeRetrieval]: "bg-[#16B364]",
  [WORKFLOW_NODE_TYPE.QuestionClassifier]: "bg-[#16B364]",
  [WORKFLOW_NODE_TYPE.IfElse]: "bg-[#06AED4]",
  [WORKFLOW_NODE_TYPE.Code]: "bg-[#2E90FA]",
  [WORKFLOW_NODE_TYPE.TemplateTransform]: "bg-[#2E90FA]",
  [WORKFLOW_NODE_TYPE.HttpRequest]: "bg-[#875BF7]",
  [WORKFLOW_NODE_TYPE.VariableAssigner]: "bg-[#2E90FA]",
  [WORKFLOW_NODE_TYPE.VariableAggregator]: "bg-[#2E90FA]",
  [WORKFLOW_NODE_TYPE.Tool]: "bg-[#8BCD47]",
  [WORKFLOW_NODE_TYPE.ParameterExtractor]: "bg-[#2E90FA]",
  [WORKFLOW_NODE_TYPE.Iteration]: "bg-[#06AED4]",
  [WORKFLOW_NODE_TYPE.Assigner]: "bg-[#2E90FA]"
};

export const getIconName = (type) => {
  const iconNameMap = {
    [WORKFLOW_NODE_TYPE.Start]: "home",
    [WORKFLOW_NODE_TYPE.LLM]: "llm",
    [WORKFLOW_NODE_TYPE.Code]: "code",
    [WORKFLOW_NODE_TYPE.End]: "end",
    [WORKFLOW_NODE_TYPE.IfElse]: "if-else",
    [WORKFLOW_NODE_TYPE.HttpRequest]: "http",
    [WORKFLOW_NODE_TYPE.Answer]: "answer",
    [WORKFLOW_NODE_TYPE.KnowledgeRetrieval]: "knowledge-retrieval",
    [WORKFLOW_NODE_TYPE.QuestionClassifier]: "question-classifier",
    [WORKFLOW_NODE_TYPE.TemplateTransform]: "templating-transform",
    [WORKFLOW_NODE_TYPE.VariableAssigner]: "variable-x",
    [WORKFLOW_NODE_TYPE.VariableAggregator]: "variable-x",
    [WORKFLOW_NODE_TYPE.Assigner]: "assigner",
    [WORKFLOW_NODE_TYPE.Tool]: "tool",
    [WORKFLOW_NODE_TYPE.Iteration]: "iteration",
    [WORKFLOW_NODE_TYPE.ParameterExtractor]: "parameter-extractor",
    running: "loader",
    succeeded: "checkbox-circle",
    failed: "error-warning",
    stopped: "alert-triangle"
  };
  return iconNameMap[type];
};
