# 知识图谱地图组件优化

## 问题解决

### 1. 地图缩放和拖动时图谱节点位置同步更新

**问题描述：** 当用户缩放或拖动地图时，图谱节点的位置没有相应更新，导致节点与地理位置不匹配。

**解决方案：**
- 添加了 `onMapTransform()` 方法来监听地图变换事件
- 改进了 `getMapTransform()` 方法来准确获取当前地图的缩放和中心位置
- 在 `updateGraphPosition()` 中添加了变换检测逻辑，只有当地图真正发生变换时才重新计算节点位置
- 使用 `lastMapTransform` 缓存上次的地图状态，避免不必要的重复计算

### 2. 节点重叠问题优化

**问题描述：** 地理坐标接近的节点在地图上会重叠显示，影响用户体验。

**解决方案：**
- 添加了 `minNodeDistance` 配置（默认30像素），确保节点间的最小距离
- 实现了 `processNodePositions()` 方法来预处理节点位置
- 添加了 `resolveOverlaps()` 方法来检测和解决节点重叠
- 实现了 `adjustPositionForOverlap()` 方法来动态调整节点位置
- 在力模拟中添加了碰撞检测力 `forceCollide()`

## 新增功能

### 位置缓存机制
- 使用 `nodePositions` Map 来缓存节点位置
- 避免重复计算，提高性能
- 在数据更新时自动清除缓存

### 智能重叠避免算法
1. **预处理阶段：** 在初始化时检测所有节点的重叠情况
2. **动态调整：** 在地图变换时实时调整节点位置
3. **最小距离保证：** 确保任意两个节点间至少保持30像素距离

## 使用方法

### 基本使用
```vue
<template>
  <KnowledgeGraphMap
    :graph-data="graphData"
    :map-options="mapOptions"
  />
</template>
```

### 数据格式
```javascript
const graphData = {
  nodes: [
    {
      id: "node1",
      name: "节点名称",
      geo: [经度, 纬度], // 地理坐标
      size: 15,
      color: "#ff6b6b",
      type: "city"
    }
  ],
  links: [
    {
      source: "node1",
      target: "node2", 
      value: 10,
      type: "economic"
    }
  ]
}
```

## 配置选项

### 节点距离配置
可以通过修改组件的 `minNodeDistance` 属性来调整节点间的最小距离：

```javascript
// 在组件的 data() 中
data() {
  return {
    minNodeDistance: 30, // 像素，可根据需要调整
    // ...
  }
}
```

## 性能优化

1. **变换检测：** 只有在地图真正发生变换时才重新计算节点位置
2. **位置缓存：** 缓存计算结果，避免重复计算
3. **批量更新：** 一次性更新所有节点位置，减少DOM操作
4. **碰撞检测：** 使用D3的内置碰撞检测力，性能更优

## 测试

使用 `src/views/KnowledgeGraphMapTest.vue` 页面来测试功能：

1. **地图变换测试：** 缩放和拖动地图，观察节点位置是否正确更新
2. **重叠避免测试：** 点击"添加接近节点"按钮，观察重叠节点是否被正确分散
3. **性能测试：** 观察大量节点时的响应速度

## 技术细节

### 坐标转换
- 使用 ECharts 的 `convertToPixel("geo", [lng, lat])` 方法将地理坐标转换为屏幕坐标
- 在地图变换时重新计算所有节点的屏幕坐标

### 重叠检测算法
- 计算节点间的欧几里得距离
- 当距离小于最小距离时，沿着连线方向移动节点
- 使用迭代算法处理多个节点的复杂重叠情况

### 力模拟优化
- 添加碰撞检测力防止节点重叠
- 为有地理坐标的节点设置固定位置 (`fx`, `fy`)
- 保持其他节点的自由移动能力
