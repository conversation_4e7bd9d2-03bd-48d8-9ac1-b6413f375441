<template>
  <div class="tabs-container">
    <el-tabs
      v-model="activeTab"
      type="card"
    >
      <el-tab-pane
        v-for="item in data.tabs"
        :key="item.name"
        :label="item.title"
        :name="item.name"
      >
        <div
          class="fault-point-container"
          v-if="item.name === activeTab"
        >
          <info-card
            v-for="card in item.content"
            :key="card.key"
            :card-text="card.text"
            :value="card.value"
            :disabled="item.disabled"
            :card-key="card.key"
            :card-url="card.url"
            @input="(val, info) => handleInput(val, info, item.content)"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    <div class="tips text-sm">
      <div
        class="text-green-500"
        v-if="isAllSelected"
      >
        满足强送条件
      </div>
      <div
        class="text-red-400"
        v-else
      >
        未满足强送条件
      </div>
    </div>
  </div>
</template>

<script>
import InfoCard from "@/components/info-card";
export default {
  components: {
    InfoCard
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeTab: "",
      isAllSelected: false,
      disabled: false
    };
  },
  mounted() {
    this.activeTab = this.data.active || this.data.tabs[0].name;
    this.confirmSend(this.checkAllValues(this.data.tabs), false);
  },
  methods: {
    handleInput(newValue, info, list) {
      // 找到相应的卡片并更新值
      const card = list.find((c) => c.key === info.key && c.text === info.text);
      if (card) {
        card.value = newValue;
      }

      console.log(`组 "${info.key}" 的卡片 "${info.text}" 已更改为: ${newValue}`);

      // 您也可以根据key分组处理
      const sameGroupCards = list.filter((c) => c.key === info.key);
      console.log(
        `同组卡片状态:`,
        sameGroupCards.map((c) => ({ text: c.text, value: c.value }))
      );
      this.confirmSend(this.checkAllValues(this.data.tabs), true);
    },
    checkAllValues(data) {
      return data.map((line) => {
        const allFalse = line.content.every((item) => item.value === false);
        line.disabled = allFalse;
        return {
          name: line.name,
          allValuesFalse: allFalse
        };
      });
    },
    confirmSend(result, flag) {
      const isAllSelected = result.every((item) => item.allValuesFalse === true);
      this.isAllSelected = isAllSelected;
      isAllSelected &&
        setTimeout(() => {
          flag && this.$bus.$emit("fault-point-data", "事故预案生成");
        }, 2000);
    }
  }
};
</script>

<style lang="scss" scoped>
.fault-point-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.tabs-container {
  ::v-deep .el-tabs {
    .el-tabs__header {
      margin-bottom: 10px;
    }
    .el-tabs__item::after {
      width: 90%;
      left: 5%;
    }
  }
}

::v-deep .el-radio-button--mini {
  .el-radio-button__inner {
    padding: 7px 11px;
  }
  .el-radio-button__orig-radio:disabled + .el-radio-button__inner {
    background-color: var(--nari-c-bg);
    color: var(--nari-c-text-1);
    border-color: var(--nari-c-border-base);
  }
  &.is-active.is-disabled {
    .el-radio-button__inner {
      background-color: var(--nari-c-primary-light-1);
      color: #fff;
    }
  }
}
</style>
