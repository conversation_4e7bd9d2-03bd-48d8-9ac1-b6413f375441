import { Message } from "element-ui";
import { ERROR_CODE } from "../config";

const defaultErrorMessage = "请求失败！";

const getErrorStatus = (status) => {
  const message = ERROR_CODE[status] || defaultErrorMessage;
  Message.error(message);
};

export const applyInterceptors = (instance) => {
  instance.interceptors.request.use(
    (config) => config,
    (err) => Promise.reject(err)
  );

  instance.interceptors.response.use(
    (response) => {
      const { status, message } = response;
      if (status === 200) {
        return response.data;
      }
      if (response.data instanceof ArrayBuffer || response.data instanceof Blob) {
        return response;
      }
      Message.error(message || "系统出错");
      return Promise.reject(new Error(message || "Error"));
    },
    (error) => {
      const { response } = error;
      const { data: res } = response;
      if (!/^(2|3)\d{2}$/.test(String(res.status))) {
        switch (res.status) {
          case 401:
            Message.error(res.message);
            return Promise.reject(res.data);
          case 403:
            Message.error(res.message);
            break;
          default:
            Message.error(res.message);
        }
        return Promise.reject(error);
      }
      if (error.message.includes("timeout")) Message.error("请求超时！请您稍后重试");
      if (error.message.includes("Network Error")) Message.error("网络错误！请您稍后重试");
      if (res) getErrorStatus(res.status);
      return Promise.reject(error);
    }
  );
};
