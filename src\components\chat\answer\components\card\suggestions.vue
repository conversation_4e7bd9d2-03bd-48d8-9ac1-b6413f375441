<template>
  <div class="suggestions-card">
    <div
      class="suggestion-item"
      v-for="(item, index) in data.suggestionItems"
      :key="index"
    >
      <div class="suggestion-title">{{ item.title }}</div>
      <div class="suggestion-content">
        <div
          class="suggestion-content-item"
          :class="{ 'is-active': current === `${index}_${i}` }"
          v-for="(content, i) in item.content"
          :key="i"
          @click="handleClick(content, `${index}_${i}`, item)"
        >
          {{ content.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SuggestionsCard",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      current: ""
    };
  },
  created() {},
  methods: {
    handleClick(cell, index, row) {
      this.current = index;
      this.$bus.$emit("flow-graph-suggest-data", { suggestId: cell.suggestId, index });
    }
  }
};
</script>

<style scoped lang="scss">
.suggestions-card {
  .suggestion-title {
    display: flex;
    justify-content: flex-start;
    font-size: 14px;
    align-items: center;
    font-weight: bold;
    color: aqua;
    &::before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 12px;
      margin-right: 5px;
      background: var(--nari-c-primary);
    }
  }
  .suggestion-content {
    padding-left: 10px;
    &-item {
      &.is-active,
      &:hover {
        color: aqua;
        text-decoration: underline;
        cursor: pointer;
      }
    }
  }
}
</style>
