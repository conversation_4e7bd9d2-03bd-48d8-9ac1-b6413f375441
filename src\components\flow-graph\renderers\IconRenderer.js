import * as d3 from "d3";
import { GeometryUtils } from "../utils/geometryUtils.js";
import { ThemeManager } from "../core/ThemeManager.js";

/**
 * 图标渲染器类
 * 负责渲染连接线上的图标和节点图标
 */
export class IconRenderer {
  constructor(renderManager, dataProcessor) {
    this.renderManager = renderManager;
    this.dataProcessor = dataProcessor;
    this.themeManager = new ThemeManager();
    this.geometryUtils = new GeometryUtils();

    // 图标尺寸常量
    this.ICON_WIDTH = 16;
    this.ICON_HEIGHT = 16;
    this.ICON_MARGIN = 8;
    this.ICON_PERPENDICULAR_Y_OFFSET = 5;

    // 图标缓存
    this.iconCache = new Map();
    this.renderedLinkIcons = null;
    this.renderedNodeIcons = null;

    // 初始化图标资源
    this.initIconAssets();
    
    this.virtualNodeScale = 2; // 默认虚拟节点放大倍数
  }

  /**
   * 设置主题
   * @param {string} theme 主题名称 ('light' | 'dark')
   */
  setTheme(theme) {
    if (this.themeManager) {
      this.themeManager.setTheme(theme);
    }
  }

  /**
   * 设置虚拟节点缩放倍数
   * @param {number} scale 缩放倍数
   */
  setVirtualNodeScale(scale) {
    this.virtualNodeScale = scale > 0 && scale <= 10 ? scale : 2;
  }

  /**
   * 计算节点半径，考虑虚拟节点的放大
   * @param {Object} nodeData 节点数据
   * @returns {number} 节点半径
   */
  calculateNodeRadius(nodeData) {
    let baseRadius = nodeData.symbolSize ? nodeData.symbolSize / 4 : 3;
    
    // 检查是否是虚拟节点
    if (nodeData.attribute && nodeData.attribute.sub_type === "virtual") {
      baseRadius *= this.virtualNodeScale;
    }
    
    return baseRadius;
  }

  /**
   * 初始化图标资源
   */
  initIconAssets() {
    this.iconAssets = import.meta.globEager("@/components/flow-graph/icon/*.svg");
  }

  /**
   * 获取图标href
   * @param {string} iconName 图标名称
   * @returns {string|null} 图标路径
   */
  getIconHref(iconName) {
    if (!iconName) return null;

    // 检查缓存
    if (this.iconCache.has(iconName)) {
      return this.iconCache.get(iconName);
    }

    const targetPathSuffix = `/icon/${iconName}.svg`;
    for (const path in this.iconAssets) {
      if (path.endsWith(targetPathSuffix)) {
        const href = this.iconAssets[path].default;
        this.iconCache.set(iconName, href);
        return href;
      }
    }

    console.warn(`FlowGraph: Icon asset not found for '${iconName}'.`);
    this.iconCache.set(iconName, null);
    return null;
  }

  /**
   * 渲染连接线图标
   * @param {Array} links 连接线数组
   * @param {Map} nodeMap 节点映射
   * @returns {d3.Selection} 图标选择集
   */
  renderLinkIcons(links, nodeMap) {
    // 创建图标容器组
    const iconsContainer = this.renderManager.createGroup("link-icons");

    // 过滤有图标的连接线 - 参考index-v1.vue的过滤条件
    const linksWithIcons = links.filter((link) => link.label && link.label.icon && link.label.position);

    // 使用image元素而不是use元素，参考index-v1.vue
    // 使用连接线的唯一ID作为key
    const renderedIcons = iconsContainer
      .selectAll("image")
      .data(linksWithIcons, (d) => d.id)
      .enter()
      .append("image")
      .attr("width", this.ICON_WIDTH)
      .attr("height", this.ICON_HEIGHT)
      .attr("xlink:href", (d) => this.getIconHref(d.label.icon))
      .style("pointer-events", "none");

    this.renderedLinkIcons = renderedIcons;
    this.renderManager.renderedLinkIcons = renderedIcons;

    return renderedIcons;
  }

  /**
   * 渲染节点图标
   * @param {Array} nodes 节点数组
   * @returns {d3.Selection} 图标选择集
   */
  renderNodeIcons(nodes) {
    const iconsContainer = this.renderManager.createGroup("node-icons");

    // 过滤有图标的节点
    const nodesWithIcons = nodes.filter((node) => node.attribute && node.attribute.icon);

    const renderedNodeIcons = iconsContainer
      .selectAll("image")
      .data(nodesWithIcons, (d) => d.id)
      .enter()
      .append("image")
      .attr("width", this.ICON_WIDTH)
      .attr("height", this.ICON_HEIGHT)
      .attr("xlink:href", (d) => this.getIconHref(d.attribute.icon))
      .style("pointer-events", "none")
      .attr("transform", (d) => this.calculateNodeIconTransform(d));

    this.renderedNodeIcons = renderedNodeIcons;
    return renderedNodeIcons;
  }

  /**
   * 计算节点图标变换
   * @param {Object} node 节点数据
   * @returns {string} 变换字符串
   */
  calculateNodeIconTransform(node) {
    const x = node.x || 0;
    const y = node.y || 0;
    const radius = this.calculateNodeRadius(node);

    // 图标位置在节点右上角
    const iconX = x + radius + this.ICON_MARGIN;
    const iconY = y - radius - this.ICON_MARGIN;

    return `translate(${iconX - this.ICON_WIDTH / 2}, ${iconY - this.ICON_HEIGHT / 2})`;
  }

  /**
   * 更新连接线图标 - 参考index-v1.vue的实现
   * @param {Array} links 连接线数组
   * @param {Map} nodeMap 节点映射
   */
  updateLinkIcons(links, nodeMap) {
    if (!this.renderedLinkIcons) return;

    const linksWithIcons = links.filter((link) => link.label && link.label.icon && link.label.position);

    // 数据绑定 - 使用连接线的唯一ID作为key
    const iconSelection = this.renderManager.container
      .select("g.link-icons")
      .selectAll("image")
      .data(linksWithIcons, (d) => d.id);

    // 移除不需要的图标
    iconSelection.exit().remove();

    // 添加新图标
    const enterSelection = iconSelection.enter().append("image").attr("width", this.ICON_WIDTH).attr("height", this.ICON_HEIGHT).style("pointer-events", "none");

    // 更新所有图标
    const mergedSelection = iconSelection.merge(enterSelection).attr("xlink:href", (d) => this.getIconHref(d.label.icon));

    this.renderedLinkIcons = mergedSelection;
    this.renderManager.renderedLinkIcons = mergedSelection;
  }

  /**
   * 更新连接线图标位置 - 完整的位置计算逻辑，参考index-v1.vue
   * @param {Array} links 连接线数组
   * @param {Map} nodeMap 节点映射
   */
  updateLinkIconsPosition(links, nodeMap) {
    if (!this.renderedLinkIcons) return;

    const self = this;
    this.renderedLinkIcons.each(function (linkData) {
      const imageElement = d3.select(this);

      const sourceNode = nodeMap.get(String(typeof linkData.source === "object" ? linkData.source.id : linkData.source));
      const targetNode = nodeMap.get(String(typeof linkData.target === "object" ? linkData.target.id : linkData.target));

      if (!sourceNode || !targetNode || !linkData.label || !linkData.label.position) {
        imageElement.style("display", "none");
        return;
      }

      // 验证节点坐标
      if (
        !Number.isFinite(sourceNode.x) ||
        !Number.isFinite(sourceNode.y) ||
        !Number.isFinite(targetNode.x) ||
        !Number.isFinite(targetNode.y) ||
        sourceNode.x === 0 ||
        sourceNode.y === 0 ||
        targetNode.x === 0 ||
        targetNode.y === 0
      ) {
        imageElement.style("display", "none");
        return;
      }

      imageElement.style("display", null);

      let refNodeForEdge, otherNodeForEdge;

      if (linkData.label.position === "source") {
        refNodeForEdge = sourceNode;
        otherNodeForEdge = targetNode;
      } else if (linkData.label.position === "target") {
        refNodeForEdge = targetNode;
        otherNodeForEdge = sourceNode;
      } else {
        imageElement.style("display", "none");
        return;
      }

      // 检查两个节点间的连接线数量 - 用于判断单条线还是多条线
      const sourceId = typeof linkData.source === "object" ? linkData.source.id : linkData.source;
      const targetId = typeof linkData.target === "object" ? linkData.target.id : linkData.target;

      const parallelLinks = links.filter((l) => {
        const linkSourceId = typeof l.source === "object" ? l.source.id : l.source;
        const linkTargetId = typeof l.target === "object" ? l.target.id : l.target;
        return (linkSourceId === sourceId && linkTargetId === targetId) || (linkSourceId === targetId && linkTargetId === sourceId);
      });

      const connectionCount = parallelLinks.length;
      const isSingleLine = connectionCount === 1;
      const isHedgeIcon = linkData.label.icon === "hedge";

      // 获取边缘坐标
      const edgeCoords = self.getEdgeCoordinates(refNodeForEdge, otherNodeForEdge);
      const baseX = edgeCoords.x;
      const baseY = edgeCoords.y;

      const dx = otherNodeForEdge.x - refNodeForEdge.x;
      const dy = otherNodeForEdge.y - refNodeForEdge.y;
      const dist = Math.sqrt(dx * dx + dy * dy);

      let iconX, iconY;
      let rotationAngle = 0;
      let rotateCx, rotateCy;

      if (dist > 0) {
        const ux = dx / dist;
        const uy = dy / dist;

        // Calculate the center point on the line, considering ICON_MARGIN
        const onLineIconCenterX = baseX + (self.ICON_MARGIN + self.ICON_WIDTH / 2) * ux;
        const onLineIconCenterY = baseY + (self.ICON_MARGIN + self.ICON_HEIGHT / 2) * uy;

        // Calculate perpendicular vector
        const perpX = -uy;
        const perpY = ux;

        // 根据单条线和多条线的情况调整垂直偏移量
        let perpendicularOffset = self.ICON_PERPENDICULAR_Y_OFFSET;
        if (isSingleLine && isHedgeIcon) {
          // 单条线且图标为hedge时，减少垂直偏移量，使图标更贴近连接线
          perpendicularOffset = 0;
        }

        // Apply perpendicular offset
        const finalIconCenterX = onLineIconCenterX + perpX * perpendicularOffset;
        const finalIconCenterY = onLineIconCenterY + perpY * perpendicularOffset;

        iconX = finalIconCenterX - self.ICON_WIDTH / 2;
        iconY = finalIconCenterY - self.ICON_HEIGHT / 2;
        rotationAngle = (Math.atan2(dy, dx) * 180) / Math.PI;

        rotateCx = finalIconCenterX;
        rotateCy = finalIconCenterY;
      } else {
        const nodeRadiusValue = this.calculateNodeRadius(refNodeForEdge);
        const initialIconCenterX = refNodeForEdge.x + (nodeRadiusValue + self.ICON_MARGIN + self.ICON_WIDTH / 2) * 1;
        const initialIconCenterY = refNodeForEdge.y;

        iconX = initialIconCenterX - self.ICON_WIDTH / 2;
        iconY = initialIconCenterY - self.ICON_HEIGHT / 2;
        rotationAngle = 0;
        rotateCx = initialIconCenterX;
        rotateCy = initialIconCenterY;
      }

      // 验证最终坐标
      if (!Number.isFinite(iconX) || !Number.isFinite(iconY) || !Number.isFinite(rotationAngle) || !Number.isFinite(rotateCx) || !Number.isFinite(rotateCy)) {
        imageElement.style("display", "none");
        return;
      }

      imageElement.attr("x", iconX).attr("y", iconY).attr("transform", `rotate(${rotationAngle} ${rotateCx} ${rotateCy})`);
    });
  }

  /**
   * 更新节点图标位置
   * @param {Array} nodes 节点数组
   */
  updateNodeIconsPosition(nodes) {
    if (!this.renderedNodeIcons) return;

    this.renderedNodeIcons.attr("transform", (d) => this.calculateNodeIconTransform(d));
  }

  /**
   * 创建箭头标记
   * @param {string} id 标记ID
   * @param {string} color 颜色
   * @param {number} size 大小
   */
  createArrowMarker(id = "arrow", color = null, size = 6) {
    // 如果没有指定颜色，使用主题管理器的颜色
    const arrowColor = color || this.themeManager.getIconColor("arrow");

    const defs = this.renderManager.svg.select("defs");

    if (defs.select(`#${id}`).empty()) {
      const marker = defs.append("marker").attr("id", id).attr("viewBox", "0 -5 10 10").attr("refX", 8).attr("refY", 0).attr("markerWidth", size).attr("markerHeight", size).attr("orient", "auto");

      marker.append("path").attr("d", "M0,-5L10,0L0,5").attr("fill", arrowColor);
    }
  }

  /**
   * 创建自定义SVG图标
   * @param {string} name 图标名称
   * @param {string} svgContent SVG内容
   * @param {number} width 宽度
   * @param {number} height 高度
   */
  createCustomIcon(name, svgContent, width = this.ICON_WIDTH, height = this.ICON_HEIGHT) {
    const defs = this.renderManager.svg.select("defs");

    if (defs.select(`#icon-${name}`).empty()) {
      const symbol = defs.append("symbol").attr("id", `icon-${name}`).attr("viewBox", `0 0 ${width} ${height}`).attr("width", width).attr("height", height);

      symbol.html(svgContent);
    }

    return `#icon-${name}`;
  }

  /**
   * 设置图标可见性
   * @param {boolean} visible 是否可见
   */
  setIconsVisibility(visible) {
    if (this.renderedLinkIcons) {
      this.renderedLinkIcons.style("display", visible ? "block" : "none");
    }

    if (this.renderedNodeIcons) {
      this.renderedNodeIcons.style("display", visible ? "block" : "none");
    }
  }

  /**
   * 应用图标高亮样式
   * @param {Object} selectedNode 选中的节点
   */
  applyIconHighlightStyles(selectedNode) {
    if (!this.renderedLinkIcons) return;

    const highlightColor = this.themeManager.getIconColor("highlight");

    this.renderedLinkIcons
      .style("opacity", (d) => {
        if (d.source.id === selectedNode.id || d.target.id === selectedNode.id) {
          return 1;
        }
        return 0.3;
      })
      .style("filter", (d) => {
        if (d.source.id === selectedNode.id || d.target.id === selectedNode.id) {
          return `drop-shadow(0px 0px 3px ${highlightColor})`;
        }
        return "none";
      });
  }

  /**
   * 清除图标高亮样式
   */
  clearIconHighlightStyles() {
    if (this.renderedLinkIcons) {
      this.renderedLinkIcons.style("opacity", 1).style("filter", "none");
    }
  }

  /**
   * 获取预定义图标列表
   * @returns {Array} 图标名称列表
   */
  getAvailableIcons() {
    const icons = [];
    for (const path in this.iconAssets) {
      const match = path.match(/\/icon\/([^/]+)\.svg$/);
      if (match) {
        icons.push(match[1]);
      }
    }
    return icons;
  }

  /**
   * 清理图标渲染器
   */
  cleanup() {
    this.iconCache.clear();
    this.renderedLinkIcons = null;
    this.renderedNodeIcons = null;
  }

  /**
   * 获取边缘坐标
   * @param {Object} nodeFrom 起始节点
   * @param {Object} nodeTo 目标节点
   * @returns {Object} 边缘坐标 {x, y}
   */
  getEdgeCoordinates(nodeFrom, nodeTo) {
    const radiusFrom = this.calculateNodeRadius(nodeFrom);
    const fromX = nodeFrom.x || 0;
    const fromY = nodeFrom.y || 0;
    const toX = nodeTo.x || 0;
    const toY = nodeTo.y || 0;

    if (nodeFrom.attribute && nodeFrom.attribute.stationstyle === "series_power_supply" && nodeFrom.attribute.nodetype) {
      const nodeType = nodeFrom.attribute.nodetype;
      const R = radiusFrom;
      const separationGap = 3; // 与drawNodeShape中保持一致

      // 计算从节点中心到目标节点的方向向量
      const dx = toX - fromX;
      const dy = toY - fromY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance === 0) {
        return { x: fromX, y: fromY };
      }

      // 计算角度（以节点中心为原点，右侧为0度，逆时针为正）
      const angle = Math.atan2(dy, dx);

      if (nodeType === "left-half") {
        // 左半圆：有效角度范围是 π/2 到 3π/2（左半圆弧）
        let finalAngle = angle;
        const safeOffset = 0.1; // 约5.7度的安全偏移，避免连接到竖边和圆弧的连接点

        // 如果角度指向右侧（透明部分），将其调整到左半圆弧的有效范围
        if (angle >= -Math.PI / 2 && angle <= Math.PI / 2) {
          // 角度在右半圆范围内，需要调整到左半圆边界，但避开边界点
          if (angle >= 0) {
            finalAngle = Math.PI / 2 + safeOffset; // 上边界，加上安全偏移
          } else {
            finalAngle = -Math.PI / 2 - safeOffset; // 下边界，减去安全偏移
          }
        } else {
          // 角度已经在左半圆范围内，但仍需避开边界点
          if (Math.abs(angle - Math.PI / 2) < safeOffset) {
            finalAngle = Math.PI / 2 + safeOffset;
          } else if (Math.abs(angle + Math.PI / 2) < safeOffset) {
            finalAngle = -Math.PI / 2 - safeOffset;
          }
        }

        // 计算连接点，考虑separationGap
        const baseX = fromX - separationGap;
        const edgeX = baseX + Math.cos(finalAngle) * R;
        const edgeY = fromY + Math.sin(finalAngle) * R;
        return { x: edgeX, y: edgeY };
      } else if (nodeType === "right-half") {
        // 右半圆：有效角度范围是 -π/2 到 π/2（右半圆弧）
        let finalAngle = angle;
        const safeOffset = 0.1; // 约5.7度的安全偏移

        // 如果角度指向左侧（透明部分），将其调整到右半圆弧的有效范围
        if (angle > Math.PI / 2 || angle < -Math.PI / 2) {
          // 角度在左半圆范围内，需要调整到右半圆边界，但避开边界点
          if (angle > Math.PI / 2) {
            finalAngle = Math.PI / 2 - safeOffset; // 上边界，减去安全偏移
          } else {
            finalAngle = -Math.PI / 2 + safeOffset; // 下边界，加上安全偏移
          }
        } else {
          // 角度已经在右半圆范围内，但仍需避开边界点
          if (Math.abs(angle - Math.PI / 2) < safeOffset) {
            finalAngle = Math.PI / 2 - safeOffset;
          } else if (Math.abs(angle + Math.PI / 2) < safeOffset) {
            finalAngle = -Math.PI / 2 + safeOffset;
          }
        }

        // 计算连接点，考虑separationGap
        const baseX = fromX + separationGap;
        const edgeX = baseX + Math.cos(finalAngle) * R;
        const edgeY = fromY + Math.sin(finalAngle) * R;
        return { x: edgeX, y: edgeY };
      } else {
        // 未定义的 nodetype，使用标准圆形逻辑
        const angle_std = Math.atan2(dy, dx);
        const edgeX = fromX + Math.cos(angle_std) * R;
        const edgeY = fromY + Math.sin(angle_std) * R;
        return { x: edgeX, y: edgeY };
      }
    }

    // 标准圆形节点的原始逻辑
    const dx_orig = toX - fromX;
    const dy_orig = toY - fromY;
    const distance_orig = Math.sqrt(dx_orig * dx_orig + dy_orig * dy_orig);

    if (distance_orig === 0) {
      return { x: fromX, y: fromY };
    }
    const offsetX = (dx_orig / distance_orig) * radiusFrom;
    const offsetY = (dy_orig / distance_orig) * radiusFrom;
    const edgeX_orig = fromX + offsetX;
    const edgeY_orig = fromY + offsetY;

    return { x: edgeX_orig, y: edgeY_orig };
  }
}
