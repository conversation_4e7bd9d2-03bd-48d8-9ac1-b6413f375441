<template>
  <div class="layout-container layout-vertical flex_col">
    <Headers v-show="isSideBar" />
    <div class="flex justify-between w-full">
      <Asides v-show="isSideBar" />
      <div class="layout-main">
        <TagsView v-show="getThemeConfig.isTagsview && isSideBar" />
        <Mains />
      </div>
    </div>
    <el-backtop target=".layout-scrollbar"></el-backtop>
  </div>
</template>

<script>
import Asides from "@/layout/component/aside.vue";
import Headers from "@/layout/component/header.vue";
import Mains from "@/layout/component/main.vue";
import TagsView from "@/layout/navBars/tagsView/tagsView.vue";
export default {
  name: "layoutVertical",
  components: { Asides, Headers, Mains, TagsView },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    isSideBar() {
      return this.getThemeConfig.isSideBar;
    }
  }
};
</script>
