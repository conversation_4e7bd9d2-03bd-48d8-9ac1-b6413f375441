// SCSS for FlowGraph Context Menu
// 使用项目的CSS变量以更好地集成主题系统

.flow-graph-context-menu {
  position: absolute;
  display: none;
  z-index: 1000;
  opacity: 0;
  transform: scale(0.9);
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px); // Safari支持

  &.show {
    opacity: 1;
    transform: scale(1);
  }

  ul {
    list-style-type: none;
    margin: 0;
    padding: 8px 0;
    background-color: var(--nari-c-bg);
    border: 1px solid var(--nari-c-border);
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(255, 255, 255, 0.05);
    min-width: 180px;
    backdrop-filter: inherit;
    -webkit-backdrop-filter: inherit;

    li {
      padding: 12px 16px;
      color: var(--nari-c-text-1);
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      position: relative;
      transition: all 0.15s ease-in-out;
      margin: 0 6px;
      border-radius: 8px;

      &:hover {
        background-color: var(--nari-c-hover);
        color: var(--nari-c-primary);
        transform: translateX(2px);
      }

      &:active {
        transform: translateX(2px) scale(0.98);
      }

      &:focus {
        outline: 2px solid var(--nari-c-primary);
        outline-offset: -2px;
        background-color: var(--nari-c-hover);
      }

      i {
        margin-right: 12px;
        min-width: 18px;
        text-align: center;
        font-size: 16px;
        opacity: 0.8;
        transition: all 0.15s ease-in-out;
        color: inherit;
      }

      &:hover i {
        opacity: 1;
        transform: scale(1.1);
        color: var(--nari-c-primary);
      }

      // 禁用状态样式
      &[style*="pointer-events: none"] {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
          transform: none;
          color: var(--nari-c-text-1);

          i {
            transform: none;
            color: inherit;
          }
        }
      }

      // 分割线样式
      &.menu-divider {
        padding: 0;
        margin: 6px 12px;
        height: 1px;
        background-color: var(--nari-c-border);
        cursor: default;
        pointer-events: none;
        border-radius: 0;

        &:hover {
          background-color: var(--nari-c-border);
          transform: none;
        }
      }
    }
  }
}

// Dark theme specific adjustments
:root[data-theme="dark"] .flow-graph-context-menu {
  ul {
    background-color: rgba(var(--nari-c-bg-mute), 0.95);
    border: 1px solid var(--nari-c-border-base);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05), 0 0 20px rgba(var(--nari-c-primary), 0.1);

    li {
      &:hover {
        background-color: var(--nari-c-hover);
        box-shadow: 0 0 8px rgba(var(--nari-c-primary), 0.2);
      }

      &:focus {
        box-shadow: 0 0 0 2px var(--nari-c-primary), 0 0 8px rgba(var(--nari-c-primary), 0.3);
      }
    }
  }
}

// Light theme specific adjustments - 使用更柔和的hover颜色
:root[data-theme="light"] .flow-graph-context-menu {
  ul {
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--nari-c-border);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    li {
      &:hover {
        // 使用更柔和的背景色，基于primary色的淡化版本
        background-color: rgba(64, 158, 255, 0.08);
        color: var(--nari-c-primary);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      }

      &:focus {
        background-color: rgba(64, 158, 255, 0.12);
        box-shadow: 0 0 0 2px var(--nari-c-primary), 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 默认主题（当没有明确设置data-theme时，假设为light主题）
.flow-graph-context-menu {
  ul {
    li {
      &:hover {
        // 默认使用更柔和的hover效果
        background-color: rgba(64, 158, 255, 0.08) !important;
        color: var(--nari-c-primary) !important;
      }

      &:focus {
        background-color: rgba(64, 158, 255, 0.12) !important;
      }
    }
  }
}

// 键盘导航支持
.flow-graph-context-menu ul li:focus {
  outline: none;
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .flow-graph-context-menu {
    ul {
      border-width: 2px;

      li {
        &:hover {
          border: 2px solid var(--nari-c-primary);
          padding: 10px 14px; // 调整内边距以适应边框
        }
      }
    }
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  .flow-graph-context-menu {
    transition: opacity 0.1s ease;

    ul li {
      transition: background-color 0.1s ease;

      &:hover {
        transform: none;
      }

      i {
        transition: none;

        &:hover {
          transform: none;
        }
      }
    }
  }

  .flow-graph-context-menu.animate-in,
  .flow-graph-context-menu.animate-out {
    animation: none;
  }
}

// 动画关键帧
@keyframes menuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes menuSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
}

@keyframes menuFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes menuFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

// 动画类
.flow-graph-context-menu.animate-in {
  animation: menuSlideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.flow-graph-context-menu.animate-out {
  animation: menuSlideOut 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

// 简化动画版本（用于性能优化）
.flow-graph-context-menu.animate-simple-in {
  animation: menuFadeIn 0.15s ease;
}

.flow-graph-context-menu.animate-simple-out {
  animation: menuFadeOut 0.1s ease;
}

// 响应式设计
@media (max-width: 768px) {
  .flow-graph-context-menu {
    ul {
      min-width: 200px;
      font-size: 16px; // 移动设备上更大的字体

      li {
        padding: 14px 18px; // 移动设备上更大的触摸区域

        i {
          font-size: 18px;
          margin-right: 14px;
        }
      }
    }
  }
}

// 打印样式
@media print {
  .flow-graph-context-menu {
    display: none !important;
  }
}

// 图标样式定义
.graph-icon-reload::before {
  content: "🔄";
  font-style: normal;
  font-size: 14px;
}

.graph-icon-export::before {
  content: "📊";
  font-style: normal;
  font-size: 14px;
}
