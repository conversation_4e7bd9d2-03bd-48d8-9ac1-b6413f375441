<template>
  <div class="markdown-container">
    <vue-markdown
      ref="markdownRef"
      class="markdown-body"
      :class="{ '!text-[#F04438]': item.isError }"
      :source="compiledMarkdown"
      v-bind="$attrs"
      v-on="$listeners"
    >
    </vue-markdown>
  </div>
</template>

<script>
import VueMarkdown from "vue-markdown";
import * as marked from "marked";
import hljs from "highlight.js";
import "highlight.js/styles/atom-one-dark.css";
export default {
  name: "Markdown",
  components: { VueMarkdown },
  props: {
    content: {
      type: String,
      default: null
    },
    item: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    compiledMarkdown() {
      const renderer = new marked.Renderer();
      marked.setOptions({
        renderer,
        highlight: (code) => hljs.highlightAuto(code).value,
        pedantic: false,
        gfm: true,
        tables: true,
        breaks: false,
        sanitize: false,
        smartLists: true,
        smartypants: false,
        xhtml: false
      });
      return marked.parse(this.content);
    }
  }
};
</script>

<style lang="scss" scoped>
.markdown-container {
  position: relative;
  .typed-container {
    white-space: pre-wrap;
    font-size: 14px;
  }
}
</style>
