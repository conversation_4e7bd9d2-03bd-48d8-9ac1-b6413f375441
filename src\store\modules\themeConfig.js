import packageJson from "../../../package.json";
/**
 * 修改一下配置时，需要每次都清理 `window.localStorage` 浏览器永久缓存，配置才会生效
 */
const themeConfigModule = {
  namespaced: true,
  state: {
    themeConfig: {
      // 是否开启布局配置抽屉
      isDrawer: false,
      /**
       * 布局切换
       * 注意：为了演示，切换布局时，颜色会被还原成默认，代码位置：/@/layout/navBars/breadcrumb/settings.vue
       * 中的 `initSetLayoutChange(设置布局切换，重置主题样式)` 方法
       */
      // 布局切换：可选值"<defaults|vertical|horizontal|mix>"，默认 defaults
      layout: "defaults",
      // 布局切换：可选值"<normal|custom>"，默认 normal
      layoutStyle: "normal",
      layoutHeaderHeight: "60px",
      /**
       * 全局主题
       */
      // 默认 primary 主题颜色
      theme: "default",
      themeColor: "#409EFF",
      primary: "#409EFF",
      // 是否开启深色模式
      isIsDark: true,

      /**
       * 菜单 / 顶栏
       * 请注意：
       * 需要同时修改 `/@/theme/common/var.scss` 对应的值
       */
      // 默认顶栏导航背景颜色
      topBar: "#ffffff",
      // 默认顶栏导航字体颜色
      topBarColor: "#606266",
      // 默认菜单导航背景颜色
      menuBar: "#303643",
      // 默认菜单导航字体颜色
      menuBarColor: "#EAEAEA",

      /**
       * 界面设置
       */
      // 是否开启内容全屏
      isSideBar: false,
      // 是否开启菜单水平折叠效果
      isCollapse: false,
      // 是否开启侧边栏 Logo
      isShowLogo: true,
      // 是否开启 Breadcrumb
      isBreadcrumb: true,
      // 是否开启 Breadcrumb 图标
      isBreadcrumbIcon: false,
      // 是否开启 Tagsview
      isTagsview: false,
      // 是否开启 TagsView 缓存
      isCacheTagsView: false,
      // 是否开启 Footer 底部版权信息
      isFooter: false,
      // 是否开启消息
      isNotice: false,
      // 是否开启登录信息
      isUserInfo: true,
      /**
       * 其它设置
       */
      // 默认 Tagsview 风格，可选<card|schedule>，自行扩展：
      // 1、需修改 @/layout/navBars/breadcrumb/settings.vue `getThemeConfig.tagsStyle` el-option
      // 2、需修改 @/layout/navBars/tagsView/tagsView.vue 代码最底部注释部分 css 样式
      tagsStyle: "schedule",
      // 主页面切换动画：可选值"<slide-right|slide-left|opacitys>"，默认 ""
      animation: "",

      /**
       * 全局网站标题 / 副标题
       */
      // 网站主标题（菜单导航、浏览器当前网页标题）
      globalTitle: packageJson.description || "后台管理系统",
      // 网站副标题（登录页顶部文字）
      globalViceTitle: "Nari",
      // 网站描述（登录页顶部文字）
      globalViceDes: "在线预案及事故辅助决策"
    }
  },
  mutations: {
    // 设置布局配置
    getThemeConfig(state, data) {
      state.themeConfig = data;
    }
  },
  actions: {
    // 设置布局配置
    setThemeConfig({ commit }, data) {
      commit("getThemeConfig", data);
    }
  }
};

export default themeConfigModule;
