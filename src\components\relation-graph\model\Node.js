export class Node {
  constructor({ id, text, width = 120, height = 120, nodeShape = 0, color = "#43a2f1", fontColor = "#ffffff", data = {}, weight = 1, ...extraAttributes }) {
    if (typeof id === "undefined" || id === null || id === "") {
      throw new Error("The 'id' field is required.");
    }
    if (!text) {
      throw new Error("The 'text' field is required.");
    }

    this.id = id;
    this.text = text;
    this.width = width * weight;
    this.height = height * weight;
    this.nodeShape = nodeShape;
    this.color = color;
    this.fontColor = fontColor;
    this.data = data;

    // Store additional attributes
    Object.assign(this, extraAttributes);
  }
}
