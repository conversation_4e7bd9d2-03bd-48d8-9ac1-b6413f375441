import Vue from "vue";
import VueRouter from "vue-router";
import store from "@/store";
import { HOME_URL, LOGIN_URL, ROUTER_WHITE_LIST } from "@/config";
import NProgress from "@/config/nprogress";
import { PrevLoading } from "@/utils/loading";
import { staticRouter, errorRouter } from "./modules/remaining";
import { Local } from "@/utils/storage";
const { VITE_IS_LOGIN, VITE_LOCAL_IMPORT_ROUTE } = import.meta.env;
import { isString } from "lodash-es";
import Layout from "@/layout/routerView/parent";

Vue.use(VueRouter);

// 处理权限路由列表
function handleAuth(routes) {
  const list = [];
  const userType = store.state.userInfos.userInfos.username;
  for (let i = 0; i < routes.length; i++) {
    const item = routes[i];
    const roles = item.meta ? item.meta.roles : undefined;
    if (!roles || (roles && roles.includes(userType))) {
      if (item.children && item.children.length) {
        item.children = handleAuth(item.children);
      }
      list.push(item);
    }
  }
  return list;
}

// 处理路由列表排序
function sortByRank(routes) {
  return routes.sort((a, b) => {
    // 如果存在子节点，递归对子节点进行排序
    [a, b].forEach((item) => {
      if (item.children) {
        item.children = sortByRank(item.children);
      }
    });
    // 根据 rank 字段进行排序
    return (a.meta.rank || 0) - (b.meta.rank || 0);
  });
}

// 递归处理接口动态路由
function eachRouter(item, components) {
  const path = item.component;
  if (isString(path) && path) {
    const module = path.startsWith("layout") ? Layout : components[`/src/views/${path}.vue`];
    if (module) {
      item.component = module;
    } else {
      console.error(`Module not found for path: ${path}`);
    }
  }
  // 当没有路由名称时，自动设置一个
  if (!item.name) {
    item.name = item.component?.name;
  }
  if (item.children && item.children.length) {
    item.children.forEach((sub) => eachRouter(sub, components));
  }
}

// 通过本地加载路由操作
async function getRouterByLocal() {
  const modules = import.meta.glob(["./modules/**/*.js", "!./modules/**/remaining.js"], {
    eager: true
  });
  let list = [];
  Object.keys(modules).forEach((key) => {
    list.push(modules[key].default);
  });
  const viewsModules = import.meta.glob("@/views/**/*.vue");
  list.forEach((item) => eachRouter(item, viewsModules));
  return list;
}

// 通过接口加载路由操作
async function getRouterBySever() {
  // 这里不要放到函数之外，理由是文件过多时，会占用内存，
  // 而放在函数内部中，用完就给销毁了，所以不存在占用内存问题
  const modules = import.meta.glob("@/views/**/*.vue");
  await store.dispatch("routesList/getMenuList");
  let list = store.getters["routesList/routesListGet"];
  list.forEach((item) => eachRouter(item, modules));
  return list;
}
// 获取动态路由处理
async function initDynamicRouter(router) {
  let list = VITE_LOCAL_IMPORT_ROUTE === "true" ? await getRouterByLocal() : await getRouterBySever();
  /** 用于渲染菜单，保持原始层级 */
  const constantMenus = sortByRank(list.flat(Infinity));
  list = handleAuth(constantMenus);
  Local.set("routesList", list);
  store.commit("routesList/saveRoutesList", list);
  list.forEach((route) => {
    if (route.meta.isFull) {
      router.addRoute(route);
    } else {
      router.addRoute("layout", route);
    }
  });
  const flatMenuList = store.getters["routesList/flatMenuListGet"];
  flatMenuList.forEach((item) => {
    item.children && delete item.children;
    item.meta.close = item.name === "home" || (item.meta.isAffix && !item.meta.isHide) ? false : true;
  });
  store.dispatch("tagsViewRoutes/setTagsViewRoutes", flatMenuList);
}

/**
 * @description 📚 路由参数配置简介
 * @param path ==> 路由菜单访问路径
 * @param name ==> 路由 name (对应页面组件 name, 可用作 KeepAlive 缓存标识 && 按钮权限筛选)
 * @param redirect ==> 路由重定向地址
 * @param component ==> 视图文件路径
 * @param meta ==> 路由菜单元信息
 * @param meta.icon ==> 菜单和面包屑对应的图标
 * @param meta.title ==> 路由标题 (用作 document.title || 菜单的名称)
 * @param meta.isLink ==> 路由外链时填写的访问地址
 * @param meta.isHide ==> 是否在菜单中隐藏 (通常列表详情页需要隐藏)
 * @param meta.isFull ==> 菜单是否全屏 (示例：数据大屏页面)
 * @param meta.isAffix ==> 菜单是否固定在标签页中 (首页通常是固定项)
 * @param meta.isKeepAlive ==> 当前路由是否缓存
 * */
const createRouter = () =>
  new VueRouter({
    routes: [...staticRouter, ...errorRouter],
    scrollBehavior: () => ({ left: 0, top: 0 })
  });

// 创建路由
const router = createRouter();

// 加载 loading
PrevLoading.start();

// 路由加载前
router.beforeEach(async (to, from, next) => {
  // 1.NProgress 开始
  NProgress.start();

  // 2.动态设置标题
  let webTitle = to.meta.title;
  const { globalTitle } = store.state.themeConfig.themeConfig;
  document.title = webTitle ? `${webTitle} - ${globalTitle}` : globalTitle;
  if (VITE_IS_LOGIN === "true") {
    // 3.判断是访问登陆页，有 Token 就在当前页面，没有 Token 重置路由到登陆页
    const token = store.state.userInfos.token || Local.get("token");
    if (to.path === LOGIN_URL) {
      // from.fullPath
      if (token) return next({ path: HOME_URL, replace: true });
      resetRouter();
      return next();
    }

    // 4.判断访问页面是否在路由白名单地址(静态路由)中，如果存在直接放行
    if (ROUTER_WHITE_LIST.includes(to.path)) return next();

    // 5.判断是否有 Token，没有重定向到 login 页面
    if (!token) return next({ path: LOGIN_URL, replace: true });
  }

  // 6.如果没有菜单列表，就重新请求菜单列表并添加动态路由
  const routesListGet = store.getters["routesList/routesListGet"];
  if (!routesListGet.length) {
    await initDynamicRouter(router);
    return next({ ...to, replace: true });
  }

  // 7.正常访问页面
  next();
});

/**
 * @description 重置路由
 * */
export const resetRouter = () => {
  router.matcher = createRouter().matcher;
};

/**
 * @description 路由跳转错误
 * */
router.onError((error) => {
  NProgress.done();
  console.warn("路由错误", error.message);
});

/**
 * @description 路由跳转结束
 * */
router.afterEach(() => {
  PrevLoading.done();
  NProgress.done();
});

// 解决编程式路由往同一地址跳转时会报错的情况
const originalPush = VueRouter.prototype.push;
const originalReplace = VueRouter.prototype.replace;

// push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

// replace
VueRouter.prototype.replace = function push(location) {
  return originalReplace.call(this, location).catch((err) => err);
};

export default router;
