<template>
  <div class="input-container relative">
    <div class="input-field">
      <!-- <div class="input-prefix">
        <svg-icon
          class="input-icon star-icon"
          icon-name="star"
        ></svg-icon>
      </div> -->
      <div class="input-content">
        <el-input
          v-model="inputText"
          placeholder="请输入问题"
          size="mini"
          @keyup.enter.native="handleSend(inputText)"
        >
        </el-input>
        <!-- <el-autocomplete
          popper-class="chat-input-autocomplete"
          v-model="inputText"
          placeholder="请输入问题"
          size="mini"
          :fetch-suggestions="querySearch"
          :trigger-on-focus="false"
          @select="handleSelect"
          @keyup.enter.native="handleSend(inputText)"
        >
        </el-autocomplete> -->
      </div>
      <div class="input-suffix flex items-center">
        <!-- <span class="flex items-center px-1 h-5 rounded-md bg-gray-100 text-xs font-medium text-gray-500">{{ inputText.trim().length }}</span> -->
        <div
          v-if="inputText"
          class="flex justify-center items-center ml-2 w-8 h-8 cursor-pointer hover:bg-gray-100 rounded-lg"
          @click="inputText = ''"
        >
          <svg-icon
            class="text-gray-400"
            icon-name="close-circle"
          ></svg-icon>
        </div>
        <div
          v-if="!inputText && speechToTextConfig?.enabled"
          class="group flex justify-center items-center ml-2 w-8 h-8 hover:bg-primary-50 rounded-lg cursor-pointer"
          @click="handleVoiceInputShow"
        >
          <svg-icon
            class="!block group-hover:!hidden"
            icon-name="voice"
          ></svg-icon>
          <svg-icon
            class="text-blue-500 !hidden group-hover:!block"
            icon-name="voice-fill"
          ></svg-icon>
        </div>
        <span
          class="send-icon group flex items-center justify-center w-8 h-8 rounded-lg cursor-pointer"
          :class="{ 'is-active': inputText.trim().length }"
        >
          <svg-icon
            class="input-icon"
            icon-name="send"
            @click="handleSend(inputText)"
          ></svg-icon>
        </span>
      </div>
    </div>

    <VoiceInput
      v-if="voiceInputShow"
      :id="id"
      @onCancel="voiceInputShow = false"
      @onConverted="
        (text) => {
          this.inputText = text;
        }
      "
    />
  </div>
</template>

<script>
import Recorder from "js-audio-recorder";
import VoiceInput from "./voice-input";
export default {
  name: "ChartInput",
  inheritAttrs: false,
  components: {
    VoiceInput
  },
  props: {
    id: {
      type: String,
      default: ""
    },
    speechToTextConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      inputText: "",
      activities: [],
      voiceInputShow: false
    };
  },
  created() {},
  mounted() {},
  methods: {
    handleVoiceInputShow() {
      Recorder.getPermission().then(
        () => {
          this.voiceInputShow = true;
        },
        () => {
          this.$message.error("麦克风未授权");
        }
      );
    },
    handleSend(text) {
      this.$emit("send", text);
      this.inputText = "";
    },
    querySearch(queryString, cb) {
      let restaurants = [
        { value: "220kV顺甫甲乙线跳闸" },
        { value: "220kV顺甫甲乙线跳闸预案" },
        { value: "帮我生成220kV顺甫甲乙线跳闸的预案" },
        { value: "220kV顺甫甲线、顺甫乙线跳闸" },
        { value: "220kV顺甫甲线、顺甫乙线跳闸预案" },
        { value: "帮我生成220kV顺甫甲线、顺甫乙线跳闸的预案" }
      ];
      const results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
      };
    },
    handleSelect(item) {
      this.handleSend(item.value);
    }
  }
};
</script>

<style scoped lang="scss">
.input-container {
  $border-color: #2e6369;
  border: 2px solid $border-color;
  border-radius: 25px;
  &:focus-within {
    border-color: var(--nari-c-primary);
  }
  .input-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-height: 32px;
    background: transparent;
    padding: 2px 4px;
    border-radius: 24px;
    overflow: hidden;
  }
  .input-prefix,
  .input-suffix {
    .input-icon {
      width: 20px;
      height: 20px;
      color: var(--nari-c-primary);
    }
    .send-icon {
      .input-icon {
        color: var(--nari-c-gray);
        cursor: pointer;
      }
      &.is-active,
      &:hover {
        .input-icon {
          color: var(--nari-c-primary);
        }
      }
    }
  }
  .input-content {
    flex: 1;
    ::v-deep .el-input__inner {
      width: 100%;
      padding: 0 10px;
      font-size: 14px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      text-align: justify;
      color: #fff;
      border: none;
      box-shadow: none;
      white-space: break-spaces;
    }
    ::v-deep .el-autocomplete {
      width: 100%;
    }
  }
}
</style>
