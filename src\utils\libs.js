import store from "@/store";
import { Message } from "element-ui";
import { Local } from "@/utils/storage.js";

// 复制文本到剪贴板
export const copyTextToClipboard = (text) => {
  if (text.includes("<think>")) {
    text = text.replace(/<think>/g, "").replace(/<\/think>/g, "\n\n");
  }
  const textarea = document.createElement("textarea");
  textarea.value = text;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand("Copy");
  document.body.removeChild(textarea);
};

/**
 * 检测字符串是否包含HTML/XML标签
 * @param {string} str 要检测的字符串
 * @returns {boolean} 是否包含标签
 */
export const isHTMLTag = (str) => {
  if (typeof str !== "string") return false;
  // 匹配HTML标签的正则表达式，包括自闭合标签和属性
  const htmlRegex = /<([a-z][a-z0-9]*)(?:\s+[^>]*)?(?:\/>|>.*<\/\1>)/i;
  // 匹配XML声明、DOCTYPE等特殊标签
  const specialTagRegex = /<(![DOCTYPE]|\\?[?])[^>]*>/i;
  return htmlRegex.test(str) || specialTagRegex.test(str);
};

export const isJSON = (content) => {
  try {
    JSON.parse(content);
    return true;
  } catch (error) {
    return false;
  }
};
export const objectToURLParams = (obj, prefix) => {
  const params = new URLSearchParams();

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      const paramKey = prefix ? `${prefix}[${key}]` : key;

      if (typeof value === "object" && value !== null) {
        // 递归处理对象
        if (Array.isArray(value)) {
          value.forEach((item) => {
            params.append(`${paramKey}[]`, item);
          });
        } else {
          objectToURLParams(value, paramKey);
        }
      } else {
        params.append(paramKey, value);
      }
    }
  }

  return params.toString();
};

export const getRandomInt = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

export const sortAgentSorts = (list) => {
  if (!list) return list;
  if (list.some((item) => item.position === undefined)) return list;
  const temp = [...list];
  temp.sort((a, b) => a.position - b.position);
  return temp;
};

export const addFileInfos = (list, messageFiles) => {
  if (!list || !messageFiles) return list;
  return list.map((item) => {
    if (item.files && item.files?.length > 0) {
      return {
        ...item,
        message_files: item.files.map((fileId) => messageFiles.find((file) => file.id === fileId))
      };
    }
    return item;
  });
};

/**
 * @description: 文件下载
 * @param {*} res 响应数据
 * @param {*} fileName 文件名
 * @param {*} isLink 是否打开新标签页下载
 * @return {*}
 */
export const fileDownload = (res, fileName = "文件导出.xlsx", isLink = false) => {
  if (!res) return;
  if (res.data.size === 0) return;
  if (isLink) {
    const url = res.request.responseURL;
    window.open(url);
    return;
  }
  const disposition = res.headers["content-disposition"];
  if (disposition) {
    fileName = disposition.substring(disposition.indexOf("filename=") + 9, disposition.length);
    // iso8859-1的字符转换成中文
    fileName = decodeURI(fileName);
  }
  const blob = new Blob([res.data], { type: res.data.type || "application/vnd.ms-excel" }); // 构造一个blob对象来处理数据，并设置文件类型

  if (window.navigator.msSaveOrOpenBlob) {
    // 兼容IE10
    navigator.msSaveBlob(blob, fileName);
  } else {
    const href = window.URL.createObjectURL(blob); // 创建新的URL表示指定的blob对象
    const a = document.createElement("a"); // 创建a标签
    a.style.display = "none";
    a.href = href; // 指定下载链接
    a.download = fileName; // 指定下载文件名
    a.click(); // 触发下载
    URL.revokeObjectURL(a.href); // 释放URL对象
  }
};

// 全屏
export const fullScreen = {
  request() {
    const doc = document.documentElement;
    if (doc.webkitRequestFullScreen) {
      doc.webkitRequestFullScreen();
    } else if (doc.mozRequestFullScreen) {
      doc.mozRequestFullScreen();
    } else if (doc.msRequestFullscreen) {
      doc.msRequestFullscreen();
    } else if (doc.requestFullScreen) {
      doc.requestFullScreen();
    }
  },
  exit() {
    const doc = document.documentElement;
    if (doc.mozRequestFullScreen) {
      document.mozCancelFullScreen();
    } else if (doc.webkitRequestFullScreen) {
      document.webkitCancelFullScreen();
    } else if (doc.msRequestFullscreen) {
      document.msExitFullscreen();
    } else if (doc.requestFullScreen) {
      document.exitFullScreen();
    }
  },
  enable() {
    let status = document.fullscreen ?? document.isFullScreen ?? document.mozFullScreen ?? document.webkitIsFullScreen ?? document.webkitFullScreen ?? document.msFullScreen;
    // IE
    if (status === undefined) {
      status = window.outerHeight === window.screen.height && window.outerWidth === window.screen.width;
    }
    return status;
  },
  toggle() {
    let status = fullScreen.enable();
    if (status) {
      fullScreen.exit();
    } else {
      fullScreen.request();
    }
  },
  listen(callback) {
    const doc = document.documentElement;
    let eventName = "fullscreenchange";
    if (doc.webkitRequestFullScreen) {
      eventName = "webkitfullscreenchange";
    } else if (doc.mozRequestFullScreen) {
      eventName = "mozfullscreenchange";
    } else if (doc.msRequestFullscreen) {
      eventName = "msfullscreenchange";
    }
    document.addEventListener(eventName, callback);

    window.addEventListener("keydown", (e) => {
      e = e || window.event;
      if (e.keyCode === 122) {
        e.preventDefault();
        fullScreen.toggle();
      }
    });
  }
};

// 内容全屏
export const contentFullScreen = {
  request() {
    store.state.themeConfig.themeConfig.isSideBar = false;
  },
  exit() {
    store.state.themeConfig.themeConfig.isSideBar = true;
  },
  enable() {
    return !store.state.themeConfig.themeConfig.isSideBar;
  },
  toggle() {
    let status = contentFullScreen.enable();
    if (status) {
      contentFullScreen.exit();
    } else {
      contentFullScreen.request();
      Message({
        customClass: "content-full-screen-message",
        dangerouslyUseHTMLString: true,
        message: "按<span style='border: 1px solid #fff;border-radius:3px;padding:6px 4px;margin: 0 8px'>ESC</span>即可退出内容全屏模式",
        center: true
      });
    }
    Local.remove("themeConfigPrev");
    const { isSideBar, isTagsview } = store.state.themeConfig.themeConfig;
    store.state.themeConfig.themeConfig.layoutHeaderHeight = !isSideBar ? "0px" : isTagsview ? "95px" : "60px";
    document.documentElement.style.setProperty("--nari-header-height", store.state.themeConfig.themeConfig.layoutHeaderHeight);
    Local.set("themeConfigPrev", store.state.themeConfig.themeConfig);
    Local.set("themeConfigStyle", document.documentElement.style.cssText);
  },
  listen() {
    window.addEventListener("beforeunload", () => {
      let status = contentFullScreen.enable();
      status && contentFullScreen.toggle();
    });
    // Escape退出
    window.addEventListener("keydown", (e) => {
      e = e || window.event;
      let status = contentFullScreen.enable();
      if ((e.keyCode === 27 || e.keyCode === 116) && status) {
        e.preventDefault();
        contentFullScreen.toggle();
      }
    });
  }
};
