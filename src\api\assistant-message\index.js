import services from "@/services";

const { http } = services;

const prefix = "";

export const AssistantApi = {
  // 获取失压设备列表
  fetchLoseEnergyEquList: (params) => http.get(prefix + `/message/getLoseEnergyEquList`, params),
  // 获取故障设备信息列表
  fetchEquipmentTroubleList: (params) => http.post(prefix + `/message/getEquipmentTroubleList`, params),
  // 获取负荷情况列表
  fetchLoadSituationList: (params) => http.post(prefix + `/message/getLoadSituationList`, params),
  // 获取风险列表
  fetchRiskList: (params) => http.post(prefix + `/message/getNSubOneList`, params),
  // 获取断面越限信息列表
  fetchSectionExceedsLimitList: (params) => http.post(prefix + `/message/getSectionExceedsLimitList`, params),
  // 获取信息报送列表
  fetchReportMessageList: (params) => http.post(prefix + `/message/getReportMessageList`, params),
  // 获取潮流图
  fetchFlowGraph: () => http.get(prefix + `/svg/getFactoryStaticFlow`)
};
