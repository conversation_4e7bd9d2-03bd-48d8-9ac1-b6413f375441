<template>
  <div class="surround-menu">
    <!-- <PERSON>de <PERSON> -->
    <div
      class="c-surround-menu-panel"
      style="display: flex; align-items: center; justify-content: center; place-items: center"
      :style="{
        width: nodeMenuPanel.width + 'px',
        height: nodeMenuPanel.height + 'px',
        left: nodeMenuPanel.x + 'px',
        top: nodeMenuPanel.y + 'px'
      }"
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 400 400"
      >
        <g>
          <g fill="#000000">
            <path
              class="c-svg-button"
              @click="clickNodeMenu('1', $event)"
              d="m9.99469,187.08997c0,-40.82082 27.52652,-97.86001 62.20722,-128.85359c32.50037,-29.06937 66.90853,-43.9133 114.603,-49.41106l7.15417,-0.82466l0,61.84972l0,61.91844l-4.49691,0c-23.5747,0 -58.05098,34.01735 -58.18725,57.3828l0,4.46692l-60.64011,0l-60.64011,0l0,-6.52858z"
            />
            <path
              class="c-svg-button"
              @click="clickNodeMenu('2', $event)"
              d="m269.58887,188.4644c-4.36064,-26.73282 -34.27188,-56.69558 -56.68828,-56.69558l-3.95183,0l0,-61.781l0,-61.71228l7.83552,0.68722c91.98219,8.52152 166.11303,82.60374 174.22109,174.00389l0.95389,10.6519l-60.77638,0l-60.77638,0l-0.81762,-5.15414z"
            />
            <path
              class="c-svg-button"
              @click="clickNodeMenu('3', $event)"
              d="m176.92533,393.32443c-89.32493,-11.68273 -166.93065,-94.35519 -166.93065,-177.92103l0,-6.66603l60.43571,0l60.50384,0l0.68135,5.97881c2.52099,23.09056 29.02549,50.99166 52.12324,55.04625c11.3104,1.99294 10.22024,-4.87926 10.22024,63.43033l0,61.09378l-6.47282,-0.20617c-3.54302,-0.13744 -8.31246,-0.41233 -10.56092,-0.75594z"
            />
            <path
              class="c-svg-button"
              @click="clickNodeMenu('4', $event)"
              d="m209.28944,332.98659l0.34067,-61.57483l7.49485,-1.64933c7.08604,-1.51188 11.51481,-3.22993 21.12184,-8.4528c6.06401,-3.22993 20.03168,-18.28003 23.43843,-25.22094c4.97385,-10.17084 7.22231,-15.94348 7.22231,-18.62364c0,-1.37444 0.40881,-3.91715 0.88575,-5.6352l0.81762,-3.09249l60.43571,0l60.50384,0l0,5.15414c-0.06813,86.31472 -81.62568,171.87351 -171.35942,179.77653l-11.24227,0.96211l0.34067,-61.64356z"
            />
          </g>
        </g>
      </svg>
      <div style="height: 100%; width: 100%; position: absolute; left: 0px; top: 0px; user-select: none; pointer-events: none; color: #ffffff; font-size: 22px">
        <i
          class="el-icon-info"
          style="position: absolute; left: 20px; top: 20px"
        />
        <i
          class="el-icon-copy-document"
          style="position: absolute; right: 20px; top: 20px"
        />
        <i
          class="el-icon-male"
          style="position: absolute; left: 20px; top: 75px; transform: rotate(180deg)"
        />
        <i
          class="el-icon-circle-plus"
          style="position: absolute; right: 20px; top: 75px"
        />
      </div>
      <div class="c-current-node-text"><input v-model="nodeText" /></div>
    </div>
    <div class="c-operate-panels">
      <!--- Node options editor -->
    </div>
  </div>
</template>

<script>
export default {
  name: "SurroundMenu",
  props: {
    nodeMenuPanel: {
      mustUseProp: false,
      typeof: Object,
      default: () => {
        return {
          x: 0,
          y: 0,
          width: 250,
          height: 187
        };
      }
    },
    currentNode: {
      mustUseProp: false,
      typeof: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      nodeText: "",
      closed: false
    };
  },
  watch: {
    currentNode(val) {
      this.nodeText = val.text;
    }
  },
  mounted() {
    this.nodeText = this.currentNode.text;
  },
  methods: {
    clickNodeMenu(type, event) {
      this.$emit("node-menu-click", type);
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .rel-node-shape-0 {
  padding: 3px !important;
}
// --------------------------Node menu-----------------------
@keyframes growUp {
  from {
    scale: 10%;
    rotate: 0deg;
  }
  50% {
    scale: 30%;
    rotate: 90deg;
  }
  to {
    scale: 100%;
    rotate: 360deg;
  }
}
.c-surround-menu-panel {
  position: absolute;
  width: 160px;
  height: 160px;
  z-index: 999;
  animation: growUp 0.5s linear;
  box-shadow: 0 0 0 38px rgba(255, 255, 255, 0.56) inset;
  border-radius: 50%;
}
.c-svg-button {
  fill: rgb(22 21 122 / 65%);
  cursor: pointer;
}
.c-svg-bottom-text {
  cursor: pointer;
  color: #ffffff;
}
.c-svg-button:hover {
  fill: rgba(26, 23, 28, 0.85);
}
@keyframes node-text-in {
  from {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.c-current-node-text {
  position: absolute;
  top: calc(100% + 16px);
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  animation: node-text-in 1s linear;
  input {
    background-color: rgba(35, 30, 37, 0.68);
    color: #ffffff;
    border-radius: 5px;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    border-width: 0px;
    &:focus {
      background-color: #dfac03;
    }
  }
}
// --------------------------Node info card-----------------------
.c-operate-panels {
  position: absolute;
  z-index: 700;
  left: 30px;
  top: 30px;
  width: 200px;
}
.c-node-info-card {
  text-align: left;
  padding: 10px;
  background-color: rgba(233, 210, 243, 0.68);
  border-radius: 10px;
  font-size: 12px;
  line-height: 25px;
}
.c-person-pic {
  width: 120px;
  border-radius: 50%;
  margin-top: 10px;
}
// --------------------------Node options panel-----------------------
.c-node-options-panel {
  border-radius: 10px;
  font-size: 12px;
  line-height: 25px;
  margin-top: 20px;
}
</style>
