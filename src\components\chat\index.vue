<template>
  <div class="flex justify-center wh-100 relative">
    <div class="chat-container w-full flex-1 px-2">
      <div
        class="chat-header"
        v-if="$slots.title || title"
      >
        <slot name="title">{{ title }}</slot>
      </div>
      <div
        class="chat-content relative"
        ref="contentArea"
      >
        <div
          class="welcome-container"
          v-if="!chatList.length"
        >
          <div class="welcome">
            <div class="image-container">
              <div class="avatar">
                <img
                  src="@/assets/images/chat/avatar.png"
                  alt=""
                />
              </div>
            </div>
            <div class="statement">
              <div class="introduction">我是虚拟调度员决策智能体</div>
              <div class="example">请问需要我处理什么事故?</div>
            </div>
          </div>
        </div>
        <template v-for="(item, index) in chatList">
          <Answer
            v-if="item.isAnswer"
            class="chat-item answer"
            :key="item.id"
            :taskId="task_id"
            :item="item"
            :question="chatList[index - 1]?.content"
            :index="index"
            :appData="appData"
            :responding="isLast(item) && isResponding"
            :hideProcessDetail="false"
            :isAvatar="isAvatar"
          ></Answer>
          <Question
            v-else
            class="chat-item question"
            :style="{ display: questionList.includes(item.content) ? 'none' : 'flex' }"
            :key="`${item.id}`"
            :item="item"
            :isAvatar="isAvatar"
          ></Question>
        </template>
      </div>
      <div class="chat-bottom">
        <!-- <div
          class="flex justify-center"
          v-if="isResponding"
        >
          <div
            class="flex items-center cursor-pointer mb-2"
            @click="handleStopResponding"
          >
            <svg-icon
              style="width: 20px; height: 20px"
              class="mr-[5px] w-3.5 h-3.5 text-gray-500"
              icon-name="stop"
            ></svg-icon>
            <span class="text-xs text-gray-500 font-normal">暂停执行</span>
          </div>
        </div> -->
        <ChatInput
          :id="appId"
          :speechToTextConfig="config?.speech_to_text"
          @send="handleSend"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Question from "./question";
import Answer from "./answer";
import ChatInput from "./chat-input";
import { Local } from "@/utils/storage";
import { CONVERSATION_ID_INFO, WORKFLOW_RUNNING_STATUS } from "@/utils/constants";
import { addFileInfos, sortAgentSorts, isJSON } from "@/utils/libs";
import { chartPost } from "@/services/fetch";
import { chatApi } from "@/api/chat";
import { produce } from "immer";
const QUESTION = ["事故预案", "故障设置", "事故后果", "风险分析", "处置建议", "强送判断", "事故预案生成"];
import jsonData from "@/mock/chat.json";
export default {
  name: "Chat",
  inheritAttrs: false,
  components: {
    Question,
    Answer,
    ChatInput
  },
  props: {
    appId: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    isAvatar: {
      type: Boolean,
      default: false
    },
    modelType: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      lastScrollHeight: 0,
      mutationObserver: null,
      checkInterval: null,
      appData: {},
      isAgentMode: false,
      hasSetResponseId: false,
      isResponding: false,
      hasStopResponded: false,
      questionList: QUESTION,
      questionId: "",
      questionItem: {},
      responseId: "",
      responseItem: {},
      conversationId: "",
      task_id: this.appId,
      isInIteration: false,
      chatList: [],
      inputText: "",
      config: {
        supportAnnotation: false,
        appId: this.appId,
        supportFeedback: false,
        supportCitationHitInfo: false,
        speech_to_text: {
          enabled: false
        }
      },
      user: "admin",
      messageTag: "",
      eventMessage: "",
      equId: "",
      accidentInfoId: "",
      demoId: "",
      type: "",
      nextQuestion: "",
      currentStep: 1,
      currentStepAutoContinue: 0,
      equIds: [],
      stationIds: []
    };
  },
  watch: {
    chatList: {
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      },
      deep: true
    }
  },
  computed: {
    isLast() {
      return (item) => item.id === this.chatList[this.chatList.length - 1]?.id;
    }
  },
  mounted() {
    this.initMutationObserver();
    this.$bus.$on("scrollToBottom", () => {
      this.scrollToBottom();
    });
    this.$bus.$on("fault-point-data", (question) => {
      console.log("question>>>>>: ", question);
      this.handleSend(question, false);
    });
  },
  beforeDestroy() {
    this.$bus.$off("scrollToBottom");
    this.$bus.$off("fault-point-data");
    this.cleanupMutationObserver();
    this.stopCheckInterval();
  },
  methods: {
    initMutationObserver() {
      if (this.mutationObserver !== null) return;
      this.mutationObserver = new MutationObserver(() => {
        this.checkScrollHeight();
      });
      const config = { childList: true, subtree: true, characterData: true };
      if (this.$refs.contentArea) {
        this.mutationObserver.observe(this.$refs.contentArea, config);
      }
    },
    cleanupMutationObserver() {
      if (this.mutationObserver && this.$refs.contentArea) {
        this.mutationObserver.disconnect();
      }
    },
    startCheckInterval() {
      if (this.checkInterval !== null) return;
      this.checkInterval = setInterval(this.checkScrollHeight, 500); // 每隔500毫秒检查一次
    },
    stopCheckInterval() {
      clearInterval(this.checkInterval);
    },
    checkScrollHeight() {
      const currentScrollHeight = this.$refs.contentArea.scrollHeight;
      if (currentScrollHeight !== this.lastScrollHeight) {
        this.lastScrollHeight = currentScrollHeight;
        this.scrollToBottom();
      }
    },
    scrollToBottom() {
      const container = this.$refs.contentArea;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    updateCurrentQA({ responseItem, questionId, placeholderAnswerId, questionItem }) {
      const newListWithAnswer = produce(
        this.chatList.filter((item) => item.id !== responseItem.id && item.id !== placeholderAnswerId),
        (draft) => {
          if (!draft.find((item) => item.id === questionId)) draft.push({ ...questionItem });
          draft.push({ ...responseItem });
        }
      );
      this.handleUpdateChatList(newListWithAnswer);
    },
    async handleStopResponding() {
      try {
        this.hasStopResponded = true;
        this.handleResponding(false);
      } catch (error) {
        console.log("error: ", error);
      }
    },
    handleResponding(isResponding) {
      this.isResponding = isResponding;
    },
    handleUpdateChatList(newChatList) {
      this.chatList = newChatList;
    },
    async handleSend(query, flag = true) {
      this.$emit("start-ask");
      this.inputText = query;
      if (!this.inputText || !this.inputText.trim()) {
        return this.$message({ type: "info", message: "提问不能为空" });
      }
      if (!this.appId) {
        return this.$message.error("appId 不能为空");
      }
      this.lastScrollHeight = this.$refs.contentArea.scrollHeight;
      this.initMutationObserver();
      this.startCheckInterval();
      try {
        this.questionId = `question-${Date.now()}`;
        let questionId = this.questionId;

        let questionItem = {
          id: questionId,
          content: this.inputText,
          isAnswer: false,
          message_files: this.files
        };
        this.$set(this, "questionItem", questionItem);
        this.responseId = `answer-placeholder-${Date.now()}`;
        const placeholderAnswerId = this.responseId;
        let placeholderAnswerItem = {
          id: placeholderAnswerId,
          content: "",
          question: this.inputText,
          isAnswer: true
        };

        const newList = [...this.chatList, questionItem, placeholderAnswerItem];
        this.handleUpdateChatList(newList);
        let responseItem = {
          id: placeholderAnswerId,
          content: "",
          question: this.inputText,
          agent_thoughts: [],
          message_files: [],
          isAnswer: true,
          workflow_run_id: "",
          workflowProcess: {
            status: "",
            tracing: []
          }
        };
        this.$set(this, "responseItem", responseItem);
        responseItem = this.responseItem;
        this.fetchChartMessage(flag);
      } catch (error) {
        console.error("error: ", error);
      }
    },
    fetchChartMessage(flag) {
      const self = this;
      let isInIteration = false;
      let questionId = this.questionId;
      let questionItem = this.questionItem;
      let placeholderAnswerId = this.responseId;
      let responseItem = this.responseItem;

      this.handleResponding(true);
      this.hasStopResponded = true;
      const params = {
        conversationId: this.conversationId,
        inputs: {},
        query: this.inputText,
        user: this.user
      };
      if ((flag || this.currentStep === 1) && this.inputText.includes("线跳闸")) {
        this.$emit("init-graph");
        params.inputs.equNames = "[]";
      }
      if (QUESTION.includes(this.inputText)) {
        params.inputs = {
          eventMessage: this.eventMessage,
          equIds: this.equId,
          accidentInfoId: this.accidentInfoId,
          token: this.accidentInfoId
        };
      }
      params.inputs.modelType = `${this.modelType}`;
      this.inputText = "";
      const url = chatApi.fetchMessage(this.appId);
      chartPost(
        url,
        {
          body: params
        },
        {
          onData(message, isFirstMessage, { conversationId: newConversationId, messageId, taskId }) {
            // console.log("onData: ", message);
            const isJsonMessage = isJSON(message);
            if (isJsonMessage) {
              const messageData = JSON.parse(message);
              const { data } = messageData;
              console.log("data: ", data);
              const isMzSmartDisposal = data?.messageTag === "GD_ASSISANCE_PLAN";
              self.messageTag = isMzSmartDisposal ? data?.messageTag : "";
              if (isMzSmartDisposal) {
                self.currentStep = data.currentStep;
                self.currentStepAutoContinue = data.currentStepAutoContinue;
                self.nextQuestion = data.nextStepName;
                if (data.currentStep === 1) {
                  self.eventMessage = data.reminder[0];
                  self.equId = self.getContentText(data.content, "equId");
                  try {
                    self.equIds = JSON.parse(self.equId);
                    const stations = self.getContentText(data.content, "station");
                    self.stationIds = Object.keys(JSON.parse(stations));
                  } catch (error) {
                    self.equIds = [];
                    self.stationIds = [];
                  }
                  self.demoId = self.getContentText(data.content, "demoId");
                  self.$bus.$emit("demo-id", self.demoId);
                  self.accidentInfoId = self.getContentText(data.content, "accidentInfoId");
                } else if (data.currentStep === 3) {
                  self.$bus.$emit("accident-id", {
                    accidentInfoId: self.accidentInfoId,
                    modelType: self.modelType,
                    overhaulEquips: self.equIds
                  });
                }
                if (data.currentStep !== 0) {
                  self.type = self.getContentText(data.content, "type") || "-1";
                  self.currentStepAutoContinue === 1 &&
                    self.$bus.$emit("flow-graph-load-data", {
                      equIds: self.equIds,
                      stationIds: self.stationIds,
                      type: self.type,
                      demoId: self.demoId,
                      accidentInfoId: self.accidentInfoId
                    });
                }
                message = JSON.stringify(data);
              }
              if (!self.isAgentMode) {
                responseItem.content = isMzSmartDisposal ? message : responseItem.content + message;
              } else {
                const lastThought = responseItem.agent_thoughts?.[responseItem.agent_thoughts?.length - 1];
                if (lastThought) lastThought.thought = isMzSmartDisposal ? message : lastThought.thought + message;
              }
            } else {
              self.messageTag = "";
              if (!self.isAgentMode) {
                responseItem.content += message;
              } else {
                const lastThought = responseItem.agent_thoughts?.[responseItem.agent_thoughts?.length - 1];
                if (lastThought) lastThought.thought += message;
              }
            }

            if (messageId && !self.hasSetResponseId) {
              responseItem.id = messageId;
              self.hasSetResponseId = true;
            }

            if (isFirstMessage && newConversationId) {
              const conversationIdInfo = Local.get(CONVERSATION_ID_INFO) || {};
              if (!conversationIdInfo[self.appId]) {
                self.conversationId = newConversationId;
                Local.set(CONVERSATION_ID_INFO, {
                  ...conversationIdInfo,
                  [self.appId]: newConversationId
                });
              }
            }

            if (taskId) {
              self.task_id = taskId;
            }
            if (messageId) responseItem.id = messageId;

            self.updateCurrentQA({
              responseItem,
              questionId,
              placeholderAnswerId,
              questionItem
            });
          },
          async onCompleted(hasError, message) {
            // console.log("onCompleted: ", hasError, message);
          },
          onFile(file) {
            // console.log("onFile: ", file);
            const lastThought = responseItem.agent_thoughts?.[responseItem.agent_thoughts?.length - 1];
            if (lastThought) responseItem.agent_thoughts[responseItem.agent_thoughts.length - 1].message_files = [...lastThought.message_files, file];

            self.updateCurrentQA({
              responseItem,
              questionId,
              placeholderAnswerId,
              questionItem
            });
          },
          onThought(thought) {
            // console.log("onThought: ", thought);
            self.isAgentMode = true;
            const response = responseItem;
            if (thought.message_id && !self.hasSetResponseId) response.id = thought.message_id;
            if (response.agent_thoughts.length === 0) {
              response.agent_thoughts.push(thought);
            } else {
              const lastThought = response.agent_thoughts[response.agent_thoughts.length - 1];
              if (lastThought.id === thought.id) {
                thought.thought = lastThought.thought;
                thought.message_files = lastThought.message_files;
                responseItem.agent_thoughts[response.agent_thoughts.length - 1] = thought;
              } else {
                responseItem.agent_thoughts.push(thought);
              }
            }
            self.updateCurrentQA({
              responseItem,
              questionId,
              placeholderAnswerId,
              questionItem
            });
          },
          onMessageEnd(messageEnd) {
            // console.log("onMessageEnd: ", messageEnd);
            if (messageEnd.metadata?.annotation_reply) {
              responseItem.id = messageEnd.id;
              responseItem.annotation = {
                id: messageEnd.metadata.annotation_reply.id,
                authorName: messageEnd.metadata.annotation_reply.account.name
              };
              const baseState = self.chatList.filter((item) => item.id !== responseItem.id && item.id !== placeholderAnswerId);
              const newListWithAnswer = produce(baseState, (draft) => {
                if (!draft.find((item) => item.id === questionId)) draft.push({ ...questionItem });

                draft.push({
                  ...responseItem
                });
              });
              self.handleUpdateChatList(newListWithAnswer);
              return;
            }
            responseItem.citation = messageEnd.metadata?.retriever_resources || [];

            const newListWithAnswer = produce(
              self.chatList.filter((item) => item.id !== responseItem.id && item.id !== placeholderAnswerId),
              (draft) => {
                if (!draft.find((item) => item.id === questionId)) draft.push({ ...questionItem });

                draft.push({ ...responseItem });
              }
            );
            self.handleUpdateChatList(newListWithAnswer);
            self.handleResponding(false);
            self.cleanupMutationObserver();
            self.stopCheckInterval();
            if (self.messageTag === "GD_ASSISANCE_PLAN") {
              console.log("%c流程推送消息已结束>>>>>>>>>>", "color: green;", self.currentStep, self.currentStepAutoContinue, self.nextQuestion);
              if (self.currentStepAutoContinue === 1 && self.nextQuestion) {
                self.handleSend(self.nextQuestion, false);
              }
            }
          },
          onMessageReplace(messageReplace) {
            // console.log("onMessageReplace: ", messageReplace);
            responseItem.content = messageReplace.answer;
          },
          onError(errorMessage, errorCode) {
            console.log(errorMessage, errorCode);
            self.handleResponding(false);
            const newChatList = produce(self.chatList, (draft) => {
              draft.splice(
                draft.findIndex((item) => item.id === placeholderAnswerId),
                1
              );
            });
            self.handleUpdateChatList(newChatList);
          },
          onWorkflowStarted({ workflow_run_id, task_id }) {
            // console.log("onWorkflowStarted: ", workflow_run_id, task_id);
            self.task_id = task_id;
            responseItem.workflow_run_id = workflow_run_id;
            responseItem.workflowProcess = {
              status: WORKFLOW_RUNNING_STATUS.Running,
              tracing: []
            };

            self.handleUpdateChatList(
              produce(self.chatList, (draft) => {
                const currentIndex = draft.findIndex((item) => item.id === responseItem.id);
                draft[currentIndex] = {
                  ...draft[currentIndex],
                  ...responseItem
                };
              })
            );
          },
          onWorkflowFinished({ data }) {
            // console.log("onWorkflowFinished: ", data);
            responseItem = {
              ...responseItem,
              workflowProcess: {
                ...responseItem.workflowProcess,
                workflowData: data,
                status: data.status
              }
            };
            self.handleUpdateChatList(
              produce(self.chatList, (draft) => {
                const currentIndex = draft.findIndex((item) => item.id === responseItem.id);
                draft[currentIndex] = {
                  ...draft[currentIndex],
                  ...responseItem
                };
              })
            );
          },
          onIterationStart({ data }) {
            // console.log("onIterationStart: ", data);
            const tracing = [
              {
                ...data,
                status: WORKFLOW_RUNNING_STATUS.Running
              }
            ];
            responseItem.workflowProcess.tracing = [responseItem.workflowProcess.tracing, ...tracing];
            self.handleUpdateChatList(
              produce(self.chatList, (draft) => {
                const currentIndex = draft.findIndex((item) => item.id === responseItem.id);
                draft[currentIndex] = {
                  ...draft[currentIndex],
                  ...responseItem
                };
              })
            );
            isInIteration = true;
          },
          onIterationFinish({ data }) {
            // console.log("onIterationFinish: ", data);
            const tracing = responseItem.workflowProcess.tracing;
            tracing[tracing.length - 1] = {
              ...tracing[tracing.length - 1],
              ...data,
              status: WORKFLOW_RUNNING_STATUS.Succeeded
            };

            self.handleUpdateChatList(
              produce(self.chatList, (draft) => {
                const currentIndex = draft.findIndex((item) => item.id === responseItem.id);
                draft[currentIndex] = {
                  ...draft[currentIndex],
                  ...responseItem
                };
              })
            );
            isInIteration = false;
          },
          onNodeStarted({ data, conversation_id }) {
            // console.log("onNodeStarted: ", data);
            if (isInIteration) return;
            const tracing = [
              {
                ...data,
                status: WORKFLOW_RUNNING_STATUS.Running
              }
            ];
            responseItem.workflowProcess.tracing = [...responseItem.workflowProcess.tracing, ...tracing];
            self.handleUpdateChatList(
              produce(self.chatList, (draft) => {
                const currentIndex = draft.findIndex((item) => item.id === responseItem.id);
                draft[currentIndex] = {
                  ...draft[currentIndex],
                  ...responseItem
                };
              })
            );
          },
          onNodeFinished({ data }) {
            // console.log("onNodeFinished: ", data);
            if (isInIteration) return;
            const currentIndex = responseItem.workflowProcess.tracing.findIndex((item) => item.node_id === data.node_id);
            responseItem.workflowProcess.tracing[currentIndex] = data;
            self.handleUpdateChatList(
              produce(self.chatList, (draft) => {
                const currentIndex = draft.findIndex((item) => item.id === responseItem.id);
                draft[currentIndex] = {
                  ...draft[currentIndex],
                  ...responseItem
                };
              })
            );
          }
        }
      );
    },
    getContentText(data, type) {
      // 查找 type 为 "equId" 的对象
      if (!data) return "";
      const equIdObject = data.find((item) => item.type === type);
      // 如果找到并且有 text 属性，则返回 text 的值
      if (equIdObject && equIdObject.data && equIdObject.data.text) {
        return equIdObject.data.text;
      }
      // 如果未找到符合条件的对象
      return "";
    },

    async fetchChatList() {
      // 使用mock数据
      console.log("使用本地mock数据");
      this.chatList = jsonData;
      this.chatList.forEach((item) => {
        item.question === "事故预案生成" && this.$bus.$emit("accident-id", "");
      });
    },
    createChatItem(item, isAnswer) {
      return {
        id: isAnswer ? item.id : `question-${item.id}`,
        content: isAnswer ? this.extractDataToString(item.answer) : item.query,
        parent_message_id: item.parent_message_id,
        isAnswer,
        message_files: item.message_files?.filter((file) => file.belongs_to === (isAnswer ? "assistant" : "user")) || [],
        ...(isAnswer && {
          agent_thoughts: addFileInfos(item.agent_thoughts ? sortAgentSorts(item.agent_thoughts) : item.agent_thoughts, item.message_files),
          feedback: item.feedback,
          citation: item.retriever_resources
        })
      };
    },
    extractDataToString(jsonString) {
      if (!jsonString) return "";
      try {
        const parsed = JSON.parse(jsonString);
        if (parsed) {
          if (parsed.data) {
            return JSON.stringify(parsed.data);
          }
          throw new Error("No data property found");
        }
        return jsonString;
      } catch (error) {
        console.error("Error processing JSON:", error);
        return null;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.chat-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  .chat-header {
    width: 100%;
    height: 30px;
    font-size: 18px;
    color: var(--nari-c-white);
    line-height: 30px;
    font-weight: 600;
  }
  ::v-deep .chat-content {
    width: 100%;
    overflow: auto;
    flex: 1;
    padding-bottom: 10px;
    .chat-item {
      display: flex;
      width: 100%;
      &:not(:last-child) {
        margin-bottom: 10px;
      }
      .chat-text {
        font-size: 14px;
        border-radius: 6px;
      }
      &.question {
        justify-content: flex-end;
        .question-text {
          background: var(--nari-c-primary);
          background: linear-gradient(143deg, #9943e3 30%, #2ee8ee 100%);
          color: var(--nari-c-white);
          padding: 8px;
        }
        .image-container {
          .avatar {
            border: 2px solid var(--nari-c-white-mute);
          }
        }
      }
      &.answer {
        justify-content: flex-start;
        .answer-text {
          width: 100%;
          &-detail {
            font-size: 12px;
            line-height: 1.2;
            color: var(--nari-c-gray);
            padding-top: 5px;
          }
          &-content {
            word-break: break-all;
          }
        }
        .image-container {
          .avatar {
            border: 2px solid var(--nari-c-primary);
            &::after {
              display: block;
              content: "";
              width: 42px;
              height: 42px;
              border: 2px solid var(--nari-c-primary-light-4);
              border-radius: 100%;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
            &.animation {
              animation: boderAnim 1.2s infinite ease;
              img {
                animation: breath 1.2s infinite ease;
              }
              &::after {
                animation: circles 1.2s infinite ease, circlesOpa 1.2s infinite ease;
              }
            }
          }
        }
      }
      .image-container {
        width: 40px;
        height: 40px;
        margin: 0 10px;
        &:hover {
          cursor: pointer;
        }
        .avatar {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 42px;
          height: 42px;
          border-radius: 50%;
          position: relative;
          img {
            display: block;
            width: 40px;
            height: 40px;
            border-radius: 50px;
          }
        }
      }
      .chat-answer-container {
        // width: calc(100% - 64px);
        &.bg-gradient {
          background: linear-gradient(90deg, #082985 0%, #104fa0 30%, #103baf 70%, #082985 100%);
        }
      }
      @keyframes boderAnim {
        0%,
        to {
          border-color: var(--nari-c-primary-light-3);
        }
        50% {
          border-color: var(--nari-c-primary);
        }
      }
      @keyframes breath {
        0%,
        to {
          transform: scale(0.9);
          transform-origin: 50% 50%;
        }

        50% {
          transform: scale(0.9);
          transform-origin: 50% 50%;
        }
      }
      @keyframes circles {
        0% {
          width: 42px;
          height: 42px;
        }
        to {
          width: 52px;
          height: 52px;
        }
      }
      @keyframes circlesOpa {
        0%,
        to {
          opacity: 0;
        }
        20% {
          opacity: 0.6;
        }
      }
    }
    &:hover {
      &::-webkit-scrollbar-thumb {
        background-color: #133268;
        &:hover {
          background-color: var(--nari-c-primary) !important;
        }
      }
    }
  }
  .welcome-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .welcome {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 90%;
    padding: 15px 0;
    .statement {
      .introduction {
        font-size: 20px;
        font-weight: 700;
        background: linear-gradient(to right, #00d9f1, #007bf5);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
      }
      .example {
        font-size: 14px;
        color: var(--nari-c-gray-light-2);
        margin-top: 5px;
      }
    }

    .image-container {
      width: 50px;
      height: 50px;
      margin: 0 10px;
      .avatar {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 52px;
        height: 52px;
        border-radius: 50%;
        position: relative;
        img {
          display: block;
          width: 50px;
          height: 50px;
          border-radius: 50px;
        }
        &::after {
          display: block;
          content: "";
          width: 52px;
          height: 52px;
          border: 2px solid var(--nari-c-primary-light-4);
          border-radius: 100%;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
        @keyframes floatAnimation {
          0% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
          100% {
            transform: translateY(0);
          }
        }
        &.animation {
          animation: floatAnimation 1.2s infinite ease;
        }
      }
    }
  }
  .chat-bottom {
    width: 100%;
    height: auto;
    padding-top: 10px;
    padding-bottom: 10px;
    position: relative;
  }
  ::v-deep .card-section ~ .text-container {
    margin-left: 10px;
  }
}
</style>
