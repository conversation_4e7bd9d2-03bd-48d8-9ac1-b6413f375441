// 浅色主题
$bg-light: #ffffff; // 背景 - 白色
$bg-light-accent: #f0f2f5; // 辅助背景 - 淡灰色
$bg-light-hover: #e4e7ed; // 悬停背景
$border-light: #dcdfe6; // 边框 - 浅灰色
$highlight-primary: #409eff; // 主高亮/按钮背景
$highlight-primary-hover: #66b1ff; // 主高亮悬停
$text-primary: #303133; // 主要文字 - 深灰色
$text-secondary: #606266; // 次要文字 - 中灰色
$text-placeholder: #c0c4cc; // 占位文字/图标 - 更浅的灰色
$text-on-highlight: #ffffff; // 高亮背景上的文字 - 白色
$shadow-light: rgba(0, 0, 0, 0.1); // 阴影

// 图谱连接线颜色
$link-color-default: #2196f3; // 默认连接线颜色 - 蓝色
$link-color-primary: #4caf50; // 主要连接线颜色 - 绿色
$link-color-secondary: #2196f3; // 次要连接线颜色 - 蓝色
$link-color-backup: #ff9800; // 备用连接线颜色 - 橙色
