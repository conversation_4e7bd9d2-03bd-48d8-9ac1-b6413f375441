<template>
  <div
    class="flex flex-wrap"
    :class="`img-${imgNum}`"
  >
    <img
      v-for="(src, index) in srcs"
      :key="index"
      class="img-item"
      :style="getWidthStyle(imgNum)"
      :src="src"
      alt=""
    />
  </div>
</template>

<script>
export default {
  name: "ImageGallery",
  props: {
    srcs: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  computed: {
    imgNum() {
      return this.srcs.length;
    }
  },
  methods: {
    getWidthStyle(imgNum) {
      if (imgNum === 1) {
        return {
          maxWidth: "100%"
        };
      }

      if (imgNum === 2 || imgNum === 4) {
        return {
          width: "calc(50% - 4px)"
        };
      }

      return {
        width: "calc(33.3333% - 5.3333px)"
      };
    }
  }
};
</script>

<style scoped lang="scss">
.img-item {
  height: 200px;
  margin-right: 8px;
  margin-bottom: 8px;
  object-fit: cover;
  object-position: center;
  border-radius: 8px;
  cursor: pointer;
  &:nth-child(3n) {
    margin-right: 0;
  }
}
.img-2 .img-item:nth-child(2n),
.img-4 .img-item:nth-child(2n) {
  margin-right: 0;
}

.img-4 .img-item:nth-child(3n) {
  margin-right: 8px;
}
</style>
