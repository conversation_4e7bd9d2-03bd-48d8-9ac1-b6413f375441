@mixin merge-style() {
  // 默认样式修改
  .el-menu {
    border-right: none;
  }
  .el-menu-item,
  .el-submenu__title {
    color: var(--nari-c-aside);
    transition: none;
  }
  // 外部链接时
  .el-menu-item a,
  .el-menu-item a:hover,
  .el-menu-item i,
  .el-submenu__title i {
    color: inherit;
    text-decoration: none;
  }
  .el-menu-item a {
    width: 86%;
    display: inline-block;
  }
  // 默认 hover 时
  .el-menu-item:hover,
  .el-submenu__title:hover {
    color: var(--nari-c-primary);
    background-color: transparent !important;
    i {
      color: var(--nari-c-primary);
    }
  }
  // 高亮时
  .el-menu-item.is-active {
    color: var(--nari-c-white);
    border-radius: 10px;
    &:hover i {
      color: var(--nari-c-white);
    }
    &::before {
      content: "";
      background-color: var(--nari-c-primary) !important;
      border-radius: 8px;
      inset: 0 8px;
      margin: 4px 0;
      position: absolute;
      z-index: -1;
    }
  }

  // 去掉离开浏览器时，菜单的默认高亮
  .el-menu-item:focus {
    background-color: transparent !important;
  }

  /* vertical 菜单 */
  .el-menu--vertical {
    background: var(--nari-bg-aside);
  }

  /* horizontal 菜单 */
  .el-menu--horizontal {
    &.el-menu {
      border-bottom: none;
    }
    .el-menu-item:not(.is-disabled):focus,
    .el-menu-item:not(.is-disabled):hover,
    > .el-submenu:focus .el-submenu__title,
    > .el-submenu:hover .el-submenu__title,
    .el-menu .el-menu-item.is-active,
    .el-menu .el-submenu.is-active > .el-submenu__title {
      color: var(--nari-c-primary);
    }
    > .el-menu-item,
    > .el-submenu .el-submenu__title,
    .el-menu-item,
    .el-submenu__title {
      color: var(--nari-c-header);
    }
    > .el-menu-item.is-active,
    > .el-submenu.is-active .el-submenu__title {
      color: var(--nari-c-primary);
      border-bottom: 4px solid var(--nari-c-primary);
      border-radius: 0;
      &:hover i {
        color: var(--nari-c-primary);
      }
    }
    .el-menu {
      border: 1px solid var(--nari-c-divider-light);
      background: var(--nari-bg-header) !important;
    }
    .el-menu--popup {
      .el-submenu__title:hover {
        color: var(--nari-c-primary);
      }
    }
  }
}

html[layout-theme-color="light"] {
  .layout-logo-medium-img,
  .layout-logo-size {
    color: var(--nari-c-primary);
  }
  .layout-logo-title {
    color: var(--nari-c-aside);
  }
  .layout-logo-split {
    border-color: var(--nari-c-aside);
  }
  .el-breadcrumb__inner a,
  .el-breadcrumb__inner.is-link {
    color: var(--nari-c-aside);
  }
}

body[layout="defaults"],
body[layout="vertical"],
body[layout="horizontal"],
body[layout="mix"] {
  @include merge-style();
}

body[layout="horizontal"] {
  .el-menu--popup {
    .el-menu-item:hover {
      color: var(--nari-c-primary);
      i {
        color: var(--nari-c-primary);
      }
    }
    .el-menu-item.is-active,
    .el-menu-item:focus,
    .el-menu-item:hover,
    .el-submenu__title:hover {
      &::before {
        display: none;
      }
    }
  }
}

html[data-theme="dark"] {
  .layout-container {
    background: var(--nari-c-bg);
    .layout-aside {
      background: var(--nari-c-black);
      &::before {
        content: "";
        display: block;
        width: 10%;
        height: 100%;
        background: linear-gradient(90deg, transparent 0%, rgba(15, 70, 110, 0.1) 50%, rgba(20, 110, 180, 0.2) 100%);
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
  [layout-style="custom"] {
    .el-menu-item,
    .el-submenu__title {
      border-left: 4px solid transparent;
    }
    &[layout="defaults"],
    &[layout="vertical"] {
      // menu
      .el-menu-item.is-active,
      .el-menu-item:focus,
      .el-menu-item:hover,
      .el-submenu__title:hover {
        border-left-color: var(--nari-c-green);
        border-radius: 0;
        color: var(--nari-c-white);
        background: linear-gradient(to right, var(--nari-c-blue-mute), transparent) !important;
        i {
          color: var(--nari-c-white);
        }
        &::before {
          display: none;
        }
      }
    }
    &[layout="vertical"] {
      .layout-navbars-breadcrumb-index {
        background: url("../assets/images/layout/header-bg.png") no-repeat;
        background-size: 100% 100%;
      }
    }
    &[layout="horizontal"],
    &[layout="mix"] {
      .layout-navbars-breadcrumb-index {
        background: url("../assets/images/layout/header-bg-horizontal.png") no-repeat;
        background-size: 100% 100%;
      }
      .layout-navbars-breadcrumb-user {
        padding-top: 20px;
      }
      .el-menu-horizontal-warp {
        .el-scrollbar__wrap {
          clip-path: polygon(6% 0, 100% 0, 100% 100%, 0% 100%);
        }
      }
      .el-menu--horizontal {
        &.el-menu {
          margin-top: 30px;
          padding: 0 8px;
        }
        > li:not(:first-child) {
          margin-left: -30px;
        }
        > .el-menu-item,
        > .el-submenu .el-submenu__title {
          height: 45px;
          line-height: 45px;
          border: none;
          padding: 0 45px;
          display: inline-block;
          position: relative;
          &::before {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: -1;
            border-radius: 12px 6px;
            border: 3px solid transparent;
            background-clip: padding-box, border-box;
            background-origin: padding-box, border-box;
            background-image: linear-gradient(136deg, var(--nari-c-black) 30%, var(--nari-c-blue-soft) 100%), linear-gradient(136deg, var(--nari-c-blue), var(--nari-c-green-light));
            margin: 0;
            transform: skewX(135deg) scale(0.8);
          }
        }
        > .el-menu-item.is-active,
        > .el-submenu.is-active .el-submenu__title {
          color: var(--nari-c-white);
          border-bottom: none;
          background: transparent !important;
          &:hover,
          &:hover i {
            color: var(--nari-c-white);
          }
          &::before {
            background-image: linear-gradient(136deg, var(--nari-c-blue-mute) 30%, var(--nari-c-blue-soft) 100%), linear-gradient(136deg, var(--nari-c-blue), var(--nari-c-green-light));
          }
        }
        .el-menu-item:hover,
        .el-submenu__title:hover {
          color: var(--nari-c-white);
          i {
            color: var(--nari-c-white);
          }
          &::before {
            background-image: linear-gradient(136deg, var(--nari-c-blue-mute) 30%, var(--nari-c-blue-soft) 100%), linear-gradient(136deg, var(--nari-c-blue), var(--nari-c-green-light));
          }
        }
      }
    }
    &[layout="horizontal"] {
      .el-menu--popup {
        .el-menu-item:hover {
          color: var(--nari-c-primary);
          i {
            color: var(--nari-c-primary);
          }
          &::before {
            background-image: linear-gradient(136deg, var(--nari-c-blue-mute) 30%, var(--nari-c-blue-soft) 100%), linear-gradient(136deg, var(--nari-c-blue), var(--nari-c-green-light));
          }
        }
        .el-menu-item.is-active,
        .el-menu-item:focus,
        .el-menu-item:hover,
        .el-submenu__title:hover {
          &::before {
            display: none;
          }
        }
      }
    }
    &[layout="mix"] {
      .el-menu--horizontal {
        .el-menu-item.is-active,
        .el-menu-item:focus,
        .el-menu-item:hover,
        .el-submenu__title:hover {
          background: transparent !important;
        }
      }
      .el-menu:not(.el-menu--horizontal) {
        .el-menu-item.is-active,
        .el-menu-item:focus,
        .el-menu-item:hover,
        .el-submenu__title:hover {
          border-left-color: var(--nari-c-green);
          border-radius: 0;
          color: var(--nari-c-white);
          background: linear-gradient(to right, var(--nari-c-blue-mute), transparent) !important;
          i {
            color: var(--nari-c-white);
          }
          &::before {
            display: none;
          }
        }
      }
    }
  }
}
