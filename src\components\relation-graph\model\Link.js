export class Link {
  constructor({ from, to, text, lineShape = 1, color = "#54ff85", fontColor = "#ffffff", useTextPath = true, data = {}, ...extraAttributes }) {
    if (typeof from === "undefined" || from === null || from === "") {
      throw new Error("The 'from' field is required.");
    }
    if (typeof to === "undefined" || to === null || to === "") {
      throw new Error("The 'to' field is required.");
    }
    if (!text) {
      throw new Error("The 'text' field is required.");
    }

    this.from = from;
    this.to = to;
    this.text = text;
    this.lineShape = lineShape;
    this.color = color;
    this.fontColor = fontColor;
    this.useTextPath = useTextPath;
    this.data = data;

    // Store additional attributes
    Object.assign(this, extraAttributes);
  }
}
