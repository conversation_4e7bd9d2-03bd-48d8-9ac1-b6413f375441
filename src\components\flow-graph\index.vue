<template>
  <div class="knowledge-graph-container">
    <flow-graph-toolbar
      v-if="isToolbarEffectivelyVisible"
      :category-stats="categoryStats"
      :get-category-color="getCategoryColor"
      @search="searchNode"
      @update-search-results="updateSearchResults"
      @select-node="selectSearchResult"
      @select-link="selectSearchLink"
      @select-category="handleCategorySelect"
      ref="toolbar"
      :show-legend="shouldShowLegend"
      :show-search="shouldShowSearch"
    />
    <flow-graph-loading
      :loading="loading"
      :error="loadError"
      :loading-text="incrementalUpdate ? '更新图谱数据' : '正在加载潮流图数据'"
      @retry="reloadData"
    />
    <div
      class="graph-container"
      ref="graphContainer"
      @mouseleave="onMouseLeave"
      :style="{ height: isToolbarEffectivelyVisible ? 'calc(100% - 40px)' : '100%' }"
    ></div>
    <div
      class="tooltip"
      ref="tooltip"
      :style="tooltipStyle"
    >
      <div
        class="tooltip-content"
        v-if="tooltipData"
      >
        <div class="tooltip-title">{{ tooltipData.name }}</div>
        <div
          class="tooltip-desc"
          v-if="tooltipData.des"
        >
          {{ tooltipData.des }}
        </div>
        <div
          class="tooltip-category"
          v-if="tooltipData.category !== undefined"
        >
          <span
            class="category-dot"
            :style="{ backgroundColor: getCategoryColor(tooltipData.category) }"
          ></span>
          <span>分类: {{ getCategoryName(tooltipData.category) }}</span>
        </div>
        <div
          class="tooltip-attrs"
          v-if="tooltipData.attrs && tooltipData.attrs.length"
        >
          <div
            v-for="(attr, index) in tooltipData.attrs"
            :key="index"
          >
            {{ attr.name }}: {{ attr.value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as d3 from "d3";
import jsonData from "@/mock/falt_g-1-start.json";
import FlowGraphToolbar from "./FlowGraphToolbar.vue";
import FlowGraphLoading from "./FlowGraphLoading.vue";
import * as graphUtils from "./utils/graphUtils.js";
import "./theme/animation.scss";
import "./theme/common/_menu.scss";

// 数据处理
import { DataProcessor } from "./core/DataProcessor.js";
// 状态管理
import { StateManager } from "./core/StateManager.js";
// 渲染管理
import { RenderManager } from "./core/RenderManager.js";
// 节点渲染
import { NodeRenderer } from "./renderers/NodeRenderer.js";
// 连接线渲染
import { LinkRenderer } from "./renderers/LinkRenderer.js";
// 标签渲染
import { LabelRenderer } from "./renderers/LabelRenderer.js";
// 图标渲染
import { IconRenderer } from "./renderers/IconRenderer.js";
// 菜单管理
import { MenuManager } from "./core/MenuManager.js";
// 主题管理
import { ThemeManager } from "./core/ThemeManager.js";
// 数据导出工具
import { DataExportUtils } from "./utils/dataExportUtils.js";

export default {
  name: "FlowGraph",
  components: {
    FlowGraphToolbar,
    FlowGraphLoading
  },
  props: {
    dataSource: {
      type: String,
      default: "mock",
      validator: function (value) {
        return ["mock", "local"].includes(value);
      }
    },
    data: {
      type: Object,
      default: null
    },
    incrementalUpdate: {
      type: Boolean,
      default: false
    },
    theme: {
      type: String,
      default: "light",
      validator: function (value) {
        return ["light", "dark"].includes(value);
      }
    },
    showNodeLabels: {
      type: Boolean,
      default: true
    },
    showLinkLabels: {
      type: Boolean,
      default: true
    },
    initialHighlightNode: {
      type: [String, Number],
      default: null
    },
    toolbar: {
      type: [Boolean, String],
      default: true,
      validator: function (value) {
        return typeof value === "boolean" || ["legend", "search"].includes(value);
      }
    },
    initialScale: {
      type: Number,
      default: null,
      validator: function (value) {
        return value === null || (typeof value === "number" && value > 0);
      }
    },
    posLayoutScale: {
      type: Number,
      default: 1
    },
    autoLoad: {
      type: Boolean,
      default: true
    },
    virtualNodeScale: {
      type: Number,
      default: 2,
      validator: function (value) {
        return value > 0 && value <= 10;
      }
    }
  },
  data() {
    return {
      // 核心管理器实例
      dataProcessor: null,
      stateManager: null,
      renderManager: null,
      nodeRenderer: null,
      linkRenderer: null,
      labelRenderer: null,
      iconRenderer: null,
      menuManager: null,
      themeManager: null,

      // 数据存储
      nodes: [],
      links: [],
      nodeMap: new Map(),
      graphData: null,
      categoryStats: {},

      // 布局相关
      simulation: null,
      width: 0,
      height: 0,
      resizeObserver: null,

      // 临时兼容属性（从StateManager获取）
      loading: false,
      loadError: null,
      selectedNode: null,
      selectedLink: null,
      hoveredNode: null,
      tooltipData: null,
      tooltipStyle: {
        display: "none",
        left: "0px",
        top: "0px"
      }
    };
  },
  watch: {
    data: {
      /**
       * 监听外部传入的data属性变化
       * @param {Object} newData 新的图谱数据
       */
      handler(newData) {
        if (newData) {
          console.log("数据已更新，准备重新加载图谱", newData);

          if (this.incrementalUpdate && this.renderManager && this.renderManager.svg && this.nodes.length > 0) {
            console.log("使用增量更新模式");

            // 防止连续快速更新导致的问题
            if (this.loading) {
              console.warn("上一次更新尚未完成，延迟当前更新");
              setTimeout(() => {
                if (!this.loading) {
                  this.graphData = newData;
                  this.updateGraph();
                }
              }, 300);
              return;
            }

            this.graphData = newData;
            this.updateGraph();
          } else {
            this.clearGraph();
            setTimeout(() => {
              this.graphData = newData;
              this.$nextTick(() => {
                if (this.autoLoad) {
                  this.reloadData();
                } else {
                  this.processData();
                  if (this.nodes.length > 0) {
                    this.initGraph();
                  } else {
                    this.stateManager.setLoadingState(false);
                  }
                }
              });
            }, 50);
          }
        }
      },
      deep: true
    },
    theme: {
      /**
       * 监听主题变化并更新样式
       * @param {String} newTheme 新的主题名称
       */
      handler(newTheme) {
        if (newTheme) {
          this.loadThemeStyles(newTheme);
          // 更新主题管理器和渲染器的主题
          if (this.themeManager) {
            this.themeManager.setTheme(newTheme);
          }
          if (this.dataProcessor) {
            this.dataProcessor.setTheme(newTheme);
          }
          if (this.nodeRenderer) {
            this.nodeRenderer.setTheme(newTheme);
          }
          if (this.linkRenderer) {
            this.linkRenderer.setTheme(newTheme);
            // 如果已经渲染了连接线，需要重新应用样式
            if (this.renderManager && this.renderManager.renderedLinks) {
              this.linkRenderer.clearHighlightStyles();
            }
          }
          if (this.labelRenderer) {
            this.labelRenderer.setTheme(newTheme);
          }
          if (this.iconRenderer) {
            this.iconRenderer.setTheme(newTheme);
          }
        }
      },
      immediate: true
    },
    initialHighlightNode: {
      /**
       * 监听初始高亮节点的变化
       * @param {String|Number} newVal 新的初始高亮节点标识
       */
      handler(newVal) {
        if (newVal && !this.loading && this.nodes.length > 0) {
          this.highlightNodeByIdentifier(newVal);
        }
      },
      immediate: true
    },
    showNodeLabels: {
      /**
       * 监听节点标签显示状态变化
       * @param {Boolean} newVal 是否显示节点标签
       */
      handler(newVal) {
        this.stateManager?.setShowNodeLabels(newVal);
        this.labelRenderer?.setNodeLabelsVisibility(newVal);
      }
    },
    showLinkLabels: {
      /**
       * 监听连接线标签显示状态变化
       * @param {Boolean} newVal 是否显示连接线标签
       */
      handler(newVal) {
        this.stateManager?.setShowLinkLabels(newVal);
        this.labelRenderer?.setLinkLabelsVisibility(newVal);
      }
    },
    virtualNodeScale: {
      /**
       * 监听虚拟节点放大倍数变化
       * @param {Number} newVal 新的放大倍数
       */
      handler(newVal) {
        if (this.nodeRenderer) {
          this.nodeRenderer.setVirtualNodeScale(newVal);
        }
        if (this.labelRenderer) {
          this.labelRenderer.setVirtualNodeScale(newVal);
        }
        if (this.iconRenderer) {
          this.iconRenderer.setVirtualNodeScale(newVal);
        }

        // 如果已经渲染了图谱，重新渲染相关元素
        if (this.nodes.length > 0 && this.renderManager?.renderedNodes) {
          this.nodeRenderer.render(this.nodes);
          this.iconRenderer.renderNodeIcons(this.nodes);
          this.labelRenderer.updateNodeLabelsPosition(this.nodes);
        }
      }
    }
  },
  /**
   * Vue组件挂载后执行
   */
  mounted() {
    this.initializeComponents();
    this.initDimensions();

    // 初始化 MenuManager
    this.menuManager = new MenuManager(this.$refs.graphContainer);
    this.menuManager.setMenuItems([
      {
        label: "重新加载",
        action: "reload",
        callback: this.handleMenuAction,
        iconClass: "graph-icon-reload"
      },
      {
        type: "divider"
      },
      {
        label: "导出JSON数据",
        action: "export",
        callback: this.handleMenuAction,
        iconClass: "graph-icon-export"
      }
    ]);

    if (this.autoLoad) {
      this.loadGraphData();
    }

    this.setupResizeObserver();
    this.setupEventListeners();
    this.setupContextMenu();
  },
  /**
   * Vue组件销毁前执行
   */
  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    /**
     * 初始化所有核心管理器和渲染器实例
     */
    initializeComponents() {
      this.dataProcessor = new DataProcessor();
      this.stateManager = new StateManager();
      this.renderManager = new RenderManager();
      this.themeManager = new ThemeManager();

      // 设置显示状态
      this.stateManager.setShowNodeLabels(this.showNodeLabels);
      this.stateManager.setShowLinkLabels(this.showLinkLabels);

      // 设置初始主题
      this.themeManager.setTheme(this.theme);
      this.dataProcessor.setTheme(this.theme);

      // 初始化渲染器
      this.nodeRenderer = new NodeRenderer(this.renderManager, this.dataProcessor);
      this.linkRenderer = new LinkRenderer(this.renderManager, this.dataProcessor);
      this.labelRenderer = new LabelRenderer(this.renderManager, this.dataProcessor);
      this.iconRenderer = new IconRenderer(this.renderManager, this.dataProcessor);

      // 设置渲染器的主题和配置
      this.nodeRenderer.setTheme(this.theme);
      this.nodeRenderer.setVirtualNodeScale(this.virtualNodeScale);
      this.linkRenderer.setTheme(this.theme);
      this.labelRenderer.setTheme(this.theme);
      this.labelRenderer.setVirtualNodeScale(this.virtualNodeScale);
      this.iconRenderer.setTheme(this.theme);
      this.iconRenderer.setVirtualNodeScale(this.virtualNodeScale);
    },

    /**
     * 初始化图谱容器的尺寸
     */
    initDimensions() {
      const container = this.$refs.graphContainer;
      this.width = container ? container.clientWidth : 800;
      this.height = container ? container.clientHeight : 600;

      this.dataProcessor?.setDimensions(this.width, this.height);
      this.renderManager?.updateSize(this.width, this.height);
    },

    /**
     * 异步加载图谱数据
     * @returns {Promise<Boolean>} 加载成功返回true，否则返回false
     */
    async loadGraphData() {
      this.loading = true;
      this.stateManager.setLoadingState(true);
      this.loadError = null;

      this.initDimensions();

      if (this.width === 0 || this.height === 0) {
        console.warn("容器尺寸为零，延迟加载");
        this.loading = false;
        this.stateManager.setLoadingState(false);
        this.stateManager.setPendingLoadDueToZeroDimensions(true);
        return false;
      }

      this.stateManager.setPendingLoadDueToZeroDimensions(false);

      try {
        if (this.dataSource === "mock") {
          this.graphData = jsonData;
        } else {
          this.graphData = this.data;
        }

        await this.initGraph();
        this.$emit("graph-loaded");

        this.$nextTick(() => {
          if (this.initialHighlightNode && this.nodes.length > 0) {
            this.highlightNodeByIdentifier(this.initialHighlightNode);
          }
        });

        return true;
      } catch (error) {
        console.error("加载图谱数据失败:", error);
        this.loadError = error.message || "加载图谱数据失败";
        this.stateManager.setLoadingState(false, this.loadError);
        this.loading = false;
        return false;
      }
    },

    /**
     * 处理原始图谱数据，转换为节点和连接线数组
     * @param {Boolean} isIncrementalUpdate 是否为增量更新
     */
    processData(isIncrementalUpdate = false) {
      const result = this.dataProcessor.processGraphData(this.graphData, this.posLayoutScale, isIncrementalUpdate, this.nodes);
      this.nodes = result.nodes;
      this.links = result.links;
      this.nodeMap = result.nodeMap;

      this.categoryStats = this.dataProcessor.countCategoryStats(this.nodes);

      // 为小型图谱设置更好的初始布局
      if (this.nodes.length < 50 && !isIncrementalUpdate) {
        this.hasPrecomputedLayout = true;
        this.setOptimalNoCrossLayout();
      } else {
        this.hasPrecomputedLayout = false;
      }
    },

    /**
     * 为小型图谱设置完全无交错的布局
     */
    setOptimalNoCrossLayout() {
      const nodeCount = this.nodes.length;
      console.log(`为${nodeCount}个节点计算无交错布局`);
      // 分析图的结构
      const graphAnalysis = this.analyzeGraphStructure();

      if (graphAnalysis.isPlanar || nodeCount <= 6) {
        // 对于平面图或少量节点，使用基于度数的圆形布局
        this.setPlanarCircularLayout(graphAnalysis);
      } else if (graphAnalysis.isTree) {
        // 对于树结构，使用层次化布局
        this.setTreeLayout(graphAnalysis);
      } else {
        // 对于复杂图，使用分层布局
        this.setLayeredLayout(graphAnalysis);
      }

      // 验证并修正交错
      this.eliminateCrossings();
    },

    /**
     * 分析图的拓扑结构
     */
    analyzeGraphStructure() {
      const nodeCount = this.nodes.length;
      const linkCount = this.links.length;

      // 构建邻接表
      const adjacency = new Map();
      const degrees = new Map();

      this.nodes.forEach((node) => {
        adjacency.set(node.id, []);
        degrees.set(node.id, 0);
      });

      this.links.forEach((link) => {
        const sourceId = link.source.id || link.source;
        const targetId = link.target.id || link.target;

        adjacency.get(sourceId).push(targetId);
        adjacency.get(targetId).push(sourceId);

        degrees.set(sourceId, degrees.get(sourceId) + 1);
        degrees.set(targetId, degrees.get(targetId) + 1);
      });

      // 检查是否为树
      const isTree = linkCount === nodeCount - 1 && this.isConnected(adjacency);

      // 检查是否为平面图 (使用欧拉公式的近似判断)
      const isPlanar = linkCount <= 3 * nodeCount - 6;

      // 找到最高度数的节点作为中心
      let centerNode = null;
      let maxDegree = 0;
      degrees.forEach((degree, nodeId) => {
        if (degree > maxDegree) {
          maxDegree = degree;
          centerNode = nodeId;
        }
      });

      return {
        nodeCount,
        linkCount,
        adjacency,
        degrees,
        isTree,
        isPlanar,
        centerNode,
        maxDegree
      };
    },

    /**
     * 检查图是否连通
     */
    isConnected(adjacency) {
      if (this.nodes.length === 0) return true;

      const visited = new Set();
      const stack = [this.nodes[0].id];

      while (stack.length > 0) {
        const nodeId = stack.pop();
        if (visited.has(nodeId)) continue;

        visited.add(nodeId);
        const neighbors = adjacency.get(nodeId) || [];
        stack.push(...neighbors.filter((id) => !visited.has(id)));
      }

      return visited.size === this.nodes.length;
    },

    /**
     * 设置基于度数的圆形布局
     */
    setPlanarCircularLayout(analysis) {
      const centerX = this.width / 2;
      const centerY = this.height / 2;
      const nodeCount = this.nodes.length;

      // 按度数排序节点
      const nodesByDegree = [...this.nodes].sort((a, b) => (analysis.degrees.get(b.id) || 0) - (analysis.degrees.get(a.id) || 0));

      if (nodeCount <= 3) {
        // 特殊处理少量节点
        nodesByDegree.forEach((node, i) => {
          const angle = (i * 2 * Math.PI) / nodeCount;
          const radius = 150;
          node.x = centerX + Math.cos(angle) * radius;
          node.y = centerY + Math.sin(angle) * radius;
        });
      } else {
        // 中心节点放在中央
        const centerNode = nodesByDegree[0];
        centerNode.x = centerX;
        centerNode.y = centerY;

        // 其他节点按角度分布，考虑连接关系
        const otherNodes = nodesByDegree.slice(1);
        const radius = Math.min(this.width, this.height) / 3;

        otherNodes.forEach((node, i) => {
          const angle = (i * 2 * Math.PI) / otherNodes.length;
          node.x = centerX + Math.cos(angle) * radius;
          node.y = centerY + Math.sin(angle) * radius;
        });
      }
    },

    /**
     * 设置树形层次布局
     */
    setTreeLayout(analysis) {
      // 找到根节点（度数最高的节点）
      const rootId = analysis.centerNode;
      // 广度优先遍历构建层次
      const levels = [];
      const visited = new Set();
      const queue = [{ nodeId: rootId, level: 0 }];

      while (queue.length > 0) {
        const { nodeId, level } = queue.shift();
        if (visited.has(nodeId)) continue;

        visited.add(nodeId);

        if (!levels[level]) levels[level] = [];
        levels[level].push(nodeId);

        const neighbors = analysis.adjacency.get(nodeId) || [];
        neighbors.forEach((neighborId) => {
          if (!visited.has(neighborId)) {
            queue.push({ nodeId: neighborId, level: level + 1 });
          }
        });
      }

      // 设置节点位置
      const levelHeight = this.height / (levels.length + 1);

      levels.forEach((levelNodes, levelIndex) => {
        const y = levelHeight * (levelIndex + 1);
        const nodeWidth = this.width / (levelNodes.length + 1);

        levelNodes.forEach((nodeId, nodeIndex) => {
          const node = this.nodes.find((n) => n.id === nodeId);
          node.x = nodeWidth * (nodeIndex + 1);
          node.y = y;
        });
      });
    },

    /**
     * 设置分层布局（适用于复杂图）
     */
    setLayeredLayout(analysis) {
      // 按度数分层
      const maxDegree = Math.max(...analysis.degrees.values());
      const layers = Array.from({ length: maxDegree + 1 }, () => []);

      this.nodes.forEach((node) => {
        const degree = analysis.degrees.get(node.id) || 0;
        layers[degree].push(node);
      });

      // 设置位置
      let currentY = 100;
      const layerSpacing = (this.height - 200) / layers.length;

      layers.reverse().forEach((layerNodes, layerIndex) => {
        if (layerNodes.length === 0) return;

        const nodeSpacing = this.width / (layerNodes.length + 1);
        layerNodes.forEach((node, nodeIndex) => {
          node.x = nodeSpacing * (nodeIndex + 1);
          node.y = currentY;
        });

        currentY += layerSpacing;
      });
    },

    /**
     * 消除连接线交错
     */
    eliminateCrossings() {
      const maxIterations = 10;
      let iteration = 0;

      while (iteration < maxIterations) {
        const crossings = this.detectAllCrossings();
        if (crossings.length === 0) {
          console.log(`经过${iteration}次迭代，成功消除所有连接线交错`);
          break;
        }

        console.log(`第${iteration + 1}次迭代，发现${crossings.length}个交错`);

        // 尝试通过调整节点位置消除交错
        this.resolveSpecificCrossings(crossings);

        iteration++;
      }

      if (iteration === maxIterations) {
        console.warn("达到最大迭代次数，可能仍存在少量交错");
      }
    },

    /**
     * 检测所有连接线交错
     */
    detectAllCrossings() {
      const crossings = [];

      for (let i = 0; i < this.links.length; i++) {
        for (let j = i + 1; j < this.links.length; j++) {
          const link1 = this.links[i];
          const link2 = this.links[j];

          if (this.linksShareNode(link1, link2)) continue;

          if (this.detectLinkIntersection(link1, link2)) {
            crossings.push({ link1, link2, index1: i, index2: j });
          }
        }
      }

      return crossings;
    },

    /**
     * 解决特定的交错问题
     */
    resolveSpecificCrossings(crossings) {
      crossings.forEach((crossing) => {
        const { link1, link2 } = crossing;

        // 计算交错点
        const intersection = this.getIntersectionPoint(link1, link2);
        if (!intersection) return;

        // 尝试移动度数较低的节点
        const nodes1 = [link1.source, link1.target];
        const nodes2 = [link2.source, link2.target];

        // 找到最适合移动的节点
        const movableNode = this.findBestNodeToMove(nodes1, nodes2);
        if (movableNode) {
          this.repositionNodeToAvoidCrossing(movableNode, intersection);
        }
      });
    },

    /**
     * 获取两条线段的交点
     */
    getIntersectionPoint(link1, link2) {
      const x1 = link1.source.x,
        y1 = link1.source.y;
      const x2 = link1.target.x,
        y2 = link1.target.y;
      const x3 = link2.source.x,
        y3 = link2.source.y;
      const x4 = link2.target.x,
        y4 = link2.target.y;

      const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
      if (Math.abs(denom) < 1e-10) return null;

      const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;

      return {
        x: x1 + t * (x2 - x1),
        y: y1 + t * (y2 - y1)
      };
    },

    /**
     * 找到最适合移动的节点
     */
    findBestNodeToMove(nodes1, nodes2) {
      const allNodes = [...nodes1, ...nodes2];

      // 选择度数最小的节点
      let bestNode = null;
      let minConnections = Infinity;

      allNodes.forEach((node) => {
        const connections = this.links.filter((link) => link.source.id === node.id || link.target.id === node.id).length;

        if (connections < minConnections) {
          minConnections = connections;
          bestNode = node;
        }
      });

      return bestNode;
    },

    /**
     * 重新定位节点以避免交错
     */
    repositionNodeToAvoidCrossing(node, intersection) {
      const currentX = node.x;
      const currentY = node.y;

      // 尝试多个新位置
      const attempts = [
        { x: currentX + 100, y: currentY },
        { x: currentX - 100, y: currentY },
        { x: currentX, y: currentY + 100 },
        { x: currentX, y: currentY - 100 },
        { x: currentX + 70, y: currentY + 70 },
        { x: currentX - 70, y: currentY - 70 },
        { x: currentX + 70, y: currentY - 70 },
        { x: currentX - 70, y: currentY + 70 }
      ];

      for (const newPos of attempts) {
        // 确保新位置在画布范围内
        if (newPos.x < 50 || newPos.x > this.width - 50 || newPos.y < 50 || newPos.y > this.height - 50) {
          continue;
        }

        // 临时移动节点
        const oldX = node.x;
        const oldY = node.y;
        node.x = newPos.x;
        node.y = newPos.y;

        // 检查是否减少了交错
        const newCrossings = this.detectAllCrossings();
        const originalCrossings = (() => {
          node.x = oldX;
          node.y = oldY;
          const count = this.detectAllCrossings();
          node.x = newPos.x;
          node.y = newPos.y;
          return count;
        })();

        if (newCrossings.length < originalCrossings.length) {
          // 新位置更好，保持
          console.log(`移动节点${node.id}到(${newPos.x}, ${newPos.y})，减少了交错`);
          return;
        } else {
          // 恢复原位置
          node.x = oldX;
          node.y = oldY;
        }
      }
    },

    /**
     * 原有的布局方法（作为后备）
     */
    setOptimalInitialLayout() {
      const nodeCount = this.nodes.length;
      const centerX = this.width / 2;
      const centerY = this.height / 2;

      if (nodeCount <= 1) return;

      // 计算合适的布局半径
      const minRadius = 150;
      const maxRadius = Math.min(this.width, this.height) / 3;
      const baseRadius = Math.max(minRadius, Math.min(maxRadius, nodeCount * 30));

      if (nodeCount <= 6) {
        // 少量节点使用圆形布局
        this.nodes.forEach((node, i) => {
          const angle = (i * 2 * Math.PI) / nodeCount;
          node.x = centerX + Math.cos(angle) * baseRadius;
          node.y = centerY + Math.sin(angle) * baseRadius;
        });
      } else if (nodeCount <= 20) {
        // 中等数量节点使用双层圆形布局
        const innerRadius = baseRadius * 0.6;
        const outerRadius = baseRadius;

        this.nodes.forEach((node, i) => {
          if (i < nodeCount / 2) {
            // 内圆
            const angle = (i * 2 * Math.PI) / Math.ceil(nodeCount / 2);
            node.x = centerX + Math.cos(angle) * innerRadius;
            node.y = centerY + Math.sin(angle) * innerRadius;
          } else {
            // 外圆
            const outerIndex = i - Math.ceil(nodeCount / 2);
            const outerCount = nodeCount - Math.ceil(nodeCount / 2);
            const angle = (outerIndex * 2 * Math.PI) / outerCount;
            node.x = centerX + Math.cos(angle) * outerRadius;
            node.y = centerY + Math.sin(angle) * outerRadius;
          }
        });
      } else {
        // 较多节点使用网格布局
        const cols = Math.ceil(Math.sqrt(nodeCount));
        const rows = Math.ceil(nodeCount / cols);
        const spacing = Math.min(this.width / (cols + 1), this.height / (rows + 1));

        this.nodes.forEach((node, i) => {
          const col = i % cols;
          const row = Math.floor(i / cols);
          node.x = (col + 1) * spacing;
          node.y = (row + 1) * spacing;
        });
      }

      console.log(`为${nodeCount}个节点设置了初始布局`);
    },

    /**
     * 初始化图谱，包括数据处理、SVG创建、力导向布局和元素渲染
     * @returns {Promise<void>}
     */
    async initGraph() {
      this.loading = true;
      this.stateManager.setLoadingState(true);

      // 设置超时机制，确保loading状态不会永远不消失（30秒超时）
      const loadingTimeout = setTimeout(() => {
        if (this.loading) {
          console.warn("图谱加载超时，强制清除loading状态");
          this.loading = false;
          this.stateManager.setLoadingState(false);
        }
      }, 30000);

      try {
        if (!this.graphData) {
          console.error("无图谱数据可用");
          this.loading = false;
          this.stateManager.setLoadingState(false);
          clearTimeout(loadingTimeout);
          return;
        }

        this.processData();

        if (!this.nodes.length) {
          console.error("处理后无有效节点数据");
          this.loading = false;
          this.stateManager.setLoadingState(false);
          clearTimeout(loadingTimeout);
          return;
        }

        // 初始化SVG
        this.renderManager.initSVG(this.$refs.graphContainer, this.width, this.height);

        // 初始化力导向布局
        this.initForceSimulation();

        // 渲染图形元素
        this.renderGraphElements();

        // 设置事件处理
        this.setupGraphEvents();

        // 启动模拟
        this.startSimulation();

        // 清除超时器
        clearTimeout(loadingTimeout);
      } catch (error) {
        console.error("初始化图谱时出错:", error);
        this.loading = false;
        this.stateManager.setLoadingState(false);
        clearTimeout(loadingTimeout);
        throw error;
      }
    },

    /**
     * 初始化D3力导向模拟
     */
    initForceSimulation() {
      const nodeCount = this.nodes.length;
      const isSmallGraph = nodeCount < 50;

      console.log(`图谱节点数量: ${nodeCount}, 使用${isSmallGraph ? "小型" : "大型"}图谱布局策略`);

      this.simulation = d3
        .forceSimulation(this.nodes)
        .alphaDecay(isSmallGraph && this.hasPrecomputedLayout ? 0.1 : isSmallGraph ? 0.05 : 0.03) // 预计算布局更快衰减
        .velocityDecay(isSmallGraph && this.hasPrecomputedLayout ? 0.6 : isSmallGraph ? 0.4 : 0.3) // 预计算布局更高阻尼
        .alpha(isSmallGraph && this.hasPrecomputedLayout ? 0.3 : isSmallGraph ? 0.8 : 0.8) // 预计算布局更低初始能量
        .force(
          "link",
          d3
            .forceLink(this.links)
            .id((d) => d.id)
            .strength(isSmallGraph ? 0.4 : 0.5) // 小图谱稍微降低连接线强度
            .distance((d) => {
              const baseMinDistance = isSmallGraph ? 180 : 200; // 小图谱适度减少基础距离
              const labelWidth = d.labelComputedWidth || 0;
              const paddingAroundLabel = isSmallGraph ? 60 : 80; // 小图谱适度减少填充
              const multiplier = isSmallGraph ? 1.8 : 2;
              return Math.max(baseMinDistance, labelWidth * multiplier + paddingAroundLabel);
            })
        )
        .force(
          "charge",
          d3
            .forceManyBody()
            .strength(isSmallGraph ? -350 : -400) // 小图谱适度降低排斥力
            .distanceMax(isSmallGraph ? 500 : 600) // 小图谱适度降低作用距离
            .theta(0.9)
        )
        .force(
          "center",
          d3.forceCenter(this.width / 2, this.height / 2).strength(isSmallGraph ? 0.15 : 0.1) // 小图谱增强中心约束
        )
        .force(
          "x",
          d3.forceX(this.width / 2).strength(isSmallGraph ? 0.05 : 0.03) // 小图谱增强X轴约束
        )
        .force(
          "y",
          d3.forceY(this.height / 2).strength(isSmallGraph ? 0.05 : 0.03) // 小图谱增强Y轴约束
        )
        .force(
          "collide",
          d3
            .forceCollide()
            .radius((d) => {
              let baseRadius = d.symbolSize ? d.symbolSize / 4 : 3;

              // 考虑虚拟节点的放大
              if (d.attribute && d.attribute.sub_type === "virtual") {
                baseRadius *= this.virtualNodeScale;
              }

              const extraRadius = isSmallGraph ? 50 : 60; // 小图谱适度降低碰撞半径
              return baseRadius + extraRadius;
            })
            .strength(isSmallGraph ? 0.7 : 0.8) // 小图谱适度降低碰撞检测强度
            .iterations(isSmallGraph ? 3 : 3) // 小图谱使用相同的迭代次数
        );

      // 如果已经预先计算了无交错布局，则不需要避让力
      // 仅在使用力导向布局时才启用避让力
      if (isSmallGraph && nodeCount >= 6 && nodeCount <= 30 && !this.hasPrecomputedLayout) {
        this.simulation.force("linkAvoidance", this.createLinkAvoidanceForce());
      }
    },

    /**
     * 创建连接线交错避让力
     * @returns {Function} D3 force function
     */
    createLinkAvoidanceForce() {
      const force = () => {
        const links = this.links;
        const strength = 0.02; // 大幅降低避让力强度，避免过度抖动

        // 检测每对连接线是否交错
        for (let i = 0; i < links.length; i++) {
          for (let j = i + 1; j < links.length; j++) {
            const link1 = links[i];
            const link2 = links[j];

            // 跳过共享节点的连接线
            if (this.linksShareNode(link1, link2)) continue;

            // 检测交错并应用避让力
            if (this.detectLinkIntersection(link1, link2)) {
              this.applyAvoidanceForce(link1, link2, strength);
            }
          }
        }
      };

      return force;
    },

    /**
     * 检测两条连接线是否共享节点
     */
    linksShareNode(link1, link2) {
      const l1Source = link1.source.id || link1.source;
      const l1Target = link1.target.id || link1.target;
      const l2Source = link2.source.id || link2.source;
      const l2Target = link2.target.id || link2.target;

      return l1Source === l2Source || l1Source === l2Target || l1Target === l2Source || l1Target === l2Target;
    },

    /**
     * 检测两条连接线是否相交
     */
    detectLinkIntersection(link1, link2) {
      const l1Source = link1.source;
      const l1Target = link1.target;
      const l2Source = link2.source;
      const l2Target = link2.target;

      // 检查坐标是否有效
      if (!this.hasValidCoordinates(l1Source) || !this.hasValidCoordinates(l1Target) || !this.hasValidCoordinates(l2Source) || !this.hasValidCoordinates(l2Target)) {
        return false;
      }

      // 使用线段相交算法
      return this.lineSegmentsIntersect(l1Source.x, l1Source.y, l1Target.x, l1Target.y, l2Source.x, l2Source.y, l2Target.x, l2Target.y);
    },

    /**
     * 检查节点是否有有效坐标
     */
    hasValidCoordinates(node) {
      return node && Number.isFinite(node.x) && Number.isFinite(node.y);
    },

    /**
     * 线段相交检测算法
     */
    lineSegmentsIntersect(x1, y1, x2, y2, x3, y3, x4, y4) {
      const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
      if (Math.abs(denom) < 1e-10) return false; // 平行线

      const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
      const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

      return t > 0 && t < 1 && u > 0 && u < 1;
    },

    /**
     * 应用避让力
     */
    applyAvoidanceForce(link1, link2, strength) {
      // 计算连接线中点
      const l1MidX = (link1.source.x + link1.target.x) / 2;
      const l1MidY = (link1.source.y + link1.target.y) / 2;
      const l2MidX = (link2.source.x + link2.target.x) / 2;
      const l2MidY = (link2.source.y + link2.target.y) / 2;

      // 计算避让方向
      const dx = l2MidX - l1MidX;
      const dy = l2MidY - l1MidY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < 1e-6) return; // 避免除零

      // 归一化方向向量
      const normalizedDx = dx / distance;
      const normalizedDy = dy / distance;

      // 应用避让力到节点
      const force = strength * 20; // 大幅降低力的大小

      // 对link1的节点施加远离link2的力
      link1.source.vx -= normalizedDx * force;
      link1.source.vy -= normalizedDy * force;
      link1.target.vx -= normalizedDx * force;
      link1.target.vy -= normalizedDy * force;

      // 对link2的节点施加远离link1的力
      link2.source.vx += normalizedDx * force;
      link2.source.vy += normalizedDy * force;
      link2.target.vx += normalizedDx * force;
      link2.target.vy += normalizedDy * force;
    },

    /**
     * 渲染所有图谱元素（节点、连接线、图标、标签）
     */
    renderGraphElements() {
      // 渲染连接线
      this.linkRenderer.render(this.links, this.nodeMap);

      // 渲染节点
      this.nodeRenderer.render(this.nodes);

      // 渲染图标
      this.iconRenderer.renderLinkIcons(this.links, this.nodeMap);
      this.iconRenderer.renderNodeIcons(this.nodes);

      // 渲染标签
      this.labelRenderer.renderNodeLabels(this.nodes, this.stateManager.showNodeLabels);
      this.labelRenderer.renderLinkLabels(this.links, this.stateManager.showLinkLabels);
    },

    /**
     * 设置图谱元素的交互事件（拖拽、点击、悬停）
     */
    setupGraphEvents() {
      // 添加拖拽事件
      this.renderManager.renderedNodes.call(
        d3
          .drag()
          .on("start", (event, d) => this.dragstarted(event, d))
          .on("drag", (event, d) => this.dragged(event, d))
          .on("end", (event, d) => this.dragended(event, d))
      );

      // 添加节点点击事件
      this.renderManager.renderedNodes.on("click", (event, d) => {
        event.preventDefault();
        event.stopPropagation();
        this.handleNodeClick(d);
      });

      // 添加悬停事件
      this.renderManager.renderedNodes.on("mouseover", (event, d) => this.handleNodeMouseOver(event, d)).on("mouseout", (event, d) => this.handleNodeMouseOut(event, d));

      // 点击空白区域
      this.renderManager.onBackgroundClick(() => {
        this.clearHighlight();
        if (this.selectedLink) {
          this.clearLinkHighlight();
        }
      });
    },

    /**
     * 启动力导向模拟
     */
    startSimulation() {
      this.simulation
        .on("tick", () => this.tick())
        .on("end", () => {
          console.log("Simulation ended");
          this.stateManager.setSimulationStopped(true);
          this.$emit("graph-finished");
          this.loading = false;
          this.stateManager.setLoadingState(false);

          // 固定节点位置
          this.nodes.forEach((node) => {
            node._x = node.x;
            node._y = node.y;
            node.fx = node.x;
            node.fy = node.y;
          });

          this.$nextTick(() => {
            this.renderManager.applyInitialScale(this.initialScale, this.nodes);
          });
        });

      // 启动simulation，使用已配置的参数
      this.simulation.restart();
    },

    /**
     * 力导向模拟的每一帧更新函数
     */
    tick() {
      try {
        if (!this.nodes || !this.links || !this.renderManager.renderedNodes || !this.renderManager.renderedLinks) {
          return;
        }

        // 更新节点位置
        this.renderManager.renderedNodes.attr("transform", (d) => {
          if (!d) return "translate(0,0)";
          const x = Number.isFinite(d.x) ? d.x : this.width / 2;
          const y = Number.isFinite(d.y) ? d.y : this.height / 2;
          return `translate(${x}, ${y})`;
        });

        // 更新连接线路径
        this.updateLinkPaths();

        // 只有在simulation相对稳定时才更新标签位置，避免抖动
        const alpha = this.simulation ? this.simulation.alpha() : 0;
        if (alpha < 0.3) {
          // 更严格的坐标验证
          const validNodes = this.nodes.filter((node) => Number.isFinite(node.x) && Number.isFinite(node.y) && node.x > 0 && node.y > 0 && node.x < this.width && node.y < this.height);

          // 只有当大部分节点都有有效坐标时才更新标签
          const validRatio = validNodes.length / this.nodes.length;
          if (validRatio > 0.5 && this.labelRenderer) {
            // 增加延迟，确保节点位置完全稳定
            requestAnimationFrame(() => {
              if (this.labelRenderer) {
                this.labelRenderer.updateNodeLabelsPosition(this.nodes);
                this.labelRenderer.updateLinkLabelsPosition(this.links);
              }
            });
          }
        }

        // 更新图标位置
        if (alpha < 0.5) {
          this.iconRenderer.updateLinkIconsPosition(this.links, this.nodeMap);
          this.iconRenderer.updateNodeIconsPosition(this.nodes);
        }
      } catch (error) {
        console.error("Tick方法执行错误:", error);
        if (this.simulation && !this.stateManager.simulationStopped) {
          this.simulation.stop();
          this.stateManager.setSimulationStopped(true);
        }
        // 清除loading状态，避免loading一直不消失
        this.loading = false;
        this.stateManager.setLoadingState(false);
      }
    },

    /**
     * 更新连接线的路径
     */
    updateLinkPaths() {
      this.renderManager.renderedLinks.attr("d", (d, i) => {
        return this.linkRenderer.calculateLinkPath(d, i, this.nodeMap);
      });
    },

    /**
     * 节点拖拽开始事件处理
     * @param {Object} event D3拖拽事件对象
     * @param {Object} d 被拖拽的节点数据
     */
    dragstarted(event, d) {
      d.fx = null;
      d.fy = null;
      d._isBeingDragged = true;

      if (this.stateManager.simulationStopped) {
        this.simulation.restart();
        this.stateManager.setSimulationStopped(false);
      }

      if (!event.active) this.simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    },

    /**
     * 节点拖拽中事件处理
     * @param {Object} event D3拖拽事件对象
     * @param {Object} d 被拖拽的节点数据
     */
    dragged(event, d) {
      d.fx = event.x;
      d.fy = event.y;
    },

    /**
     * 节点拖拽结束事件处理
     * @param {Object} event D3拖拽事件对象
     * @param {Object} d 被拖拽的节点数据
     */
    dragended(event, d) {
      if (!event.active) {
        this.simulation.alphaTarget(0);
      }

      this.nodes.forEach((node) => {
        node._x = node.x;
        node._y = node.y;
        node.fx = node.x;
        node.fy = node.y;
        delete node._isBeingDragged;
      });

      setTimeout(() => {
        if (this.simulation) {
          this.simulation.stop();
          this.stateManager.setSimulationStopped(true);
        }
      }, 100);
    },

    /**
     * 节点点击事件处理
     * @param {Object} node 被点击的节点数据
     */
    handleNodeClick(node) {
      if (this.selectedNode === node) {
        this.clearHighlight();
        return;
      }

      this.selectedNode = node;
      this.stateManager.setSelectedNode(node);
      this.highlightRelated(node);

      this.$emit("node-click", node);
    },

    /**
     * 节点鼠标悬停事件处理
     * @param {Object} event D3鼠标事件对象
     * @param {Object} d 悬停的节点数据
     */
    handleNodeMouseOver(event, d) {
      this.hoveredNode = d;
      this.stateManager.setHoveredNode(d);
      this.tooltipData = d;
      this.stateManager.setTooltipData(d);

      this.showTooltip(event, d);

      if (this.labelRenderer && this.stateManager.showNodeLabels) {
        this.labelRenderer.setNodeLabelHoverHighlight(d.id, true);
      }

      // 移除条件限制，始终应用悬停样式
      this.applyHoverStyles(d);
    },

    /**
     * 节点鼠标移出事件处理
     * @param {Object} event D3鼠标事件对象
     * @param {Object} d 移出的节点数据
     */
    handleNodeMouseOut(event, d) {
      if (this.labelRenderer && this.stateManager.showNodeLabels && d && d.id !== null) {
        this.labelRenderer.setNodeLabelHoverHighlight(d.id, false);
      }

      this.hoveredNode = null;
      this.stateManager.setHoveredNode(null);
      this.hideTooltip();

      // 移除条件限制，始终清除悬停样式
      this.clearHoverStyles();
    },

    /**
     * 显示Tooltip提示框
     * @param {Object} event 鼠标事件对象
     * @param {Object} nodeData 节点数据
     */
    showTooltip(event, nodeData) {
      this.tooltipStyle = { display: "block", visibility: "hidden" };

      this.$nextTick(() => {
        if (!this.$refs.tooltip || !this.hoveredNode || this.hoveredNode.id !== nodeData.id) {
          this.tooltipStyle = { display: "none", visibility: "visible" };
          return;
        }

        // 计算tooltip位置
        const tooltipPosition = this.calculateTooltipPosition(event);
        this.tooltipStyle = {
          display: "block",
          visibility: "visible",
          left: `${tooltipPosition.x}px`,
          top: `${tooltipPosition.y}px`
        };
      });
    },

    /**
     * 隐藏Tooltip提示框
     */
    hideTooltip() {
      this.tooltipStyle = { display: "none" };
      this.tooltipData = null;
      this.stateManager.hideTooltip();
    },

    /**
     * 计算Tooltip提示框的位置
     * @param {Object} event 鼠标事件对象
     * @returns {Object} Tooltip的位置 {x, y}
     */
    calculateTooltipPosition(event) {
      const tooltipEl = this.$refs.tooltip;
      const tooltipWidth = tooltipEl.offsetWidth;
      const tooltipHeight = tooltipEl.offsetHeight;
      const nodeRect = event.target.getBoundingClientRect();
      const containerRect = this.$el.getBoundingClientRect();

      let x = nodeRect.right + 15 - containerRect.left;
      let y = nodeRect.top + nodeRect.height / 2 - tooltipHeight / 2 - containerRect.top;

      // 边界检查
      if (x + tooltipWidth > this.width) {
        x = nodeRect.left - tooltipWidth - 15 - containerRect.left;
      }
      if (y < 0) y = 10;
      if (y + tooltipHeight > this.height) y = this.height - tooltipHeight - 10;

      return { x, y };
    },

    /**
     * 应用节点和连接线的悬停样式
     * @param {Object} node 悬停的节点数据
     */
    applyHoverStyles(node) {
      // 如果有选中的节点或连接线，不应用悬停样式，避免冲突
      if (this.selectedNode || this.selectedLink) {
        return;
      }

      // 找出与该节点相连的所有连接线
      const connectedLinks = this.links.filter((link) => link.source.id === node.id || link.target.id === node.id);

      // 找出相连的节点ID
      const connectedNodeIds = new Set();
      connectedLinks.forEach((link) => {
        if (link.source.id === node.id) {
          connectedNodeIds.add(link.target.id);
        } else {
          connectedNodeIds.add(link.source.id);
        }
      });

      // 添加当前悬停的节点ID
      connectedNodeIds.add(node.id);

      // 获取主题感知的颜色
      const selectedColor = this.themeManager.getNodeHighlightColor("selected");
      const connectedColor = this.themeManager.getNodeHighlightColor("connected");

      // 应用节点悬停样式
      if (this.renderManager.renderedNodes) {
        this.renderManager.renderedNodes.each(function (d) {
          const group = d3.select(this);

          if (connectedNodeIds.has(d.id)) {
            // 检查是否为特殊类型节点，如果是则保持原有边框
            const isSpecialNode = d.attribute && (d.attribute.stationstyle === "loss_pressure_half" || d.attribute.stationstyle === "disconnection");

            if (!isSpecialNode) {
              // 高亮相关节点
              if (d.id === node.id) {
                // 当前悬停的节点使用主题感知的选中颜色
                group.selectAll(".node-shape").attr("stroke", selectedColor).attr("stroke-width", 3);
              } else {
                // 相关节点使用主题感知的相连颜色
                group.selectAll(".node-shape").attr("stroke", connectedColor).attr("stroke-width", 2);
              }
            }
            // 特殊节点保持原有样式，不做修改
          } else {
            // 其他节点保持原样或稍微变暗
            group.selectAll(".node-shape").style("opacity", 0.6);
          }
        });
      }

      // 应用连接线悬停样式
      this.linkRenderer?.applyHighlightStyles(node);
      // 连接线标签悬停样式
      this.labelRenderer?.applyLinkHighlightStyles(node);
    },

    /**
     * 清除节点和连接线的悬停样式
     */
    clearHoverStyles() {
      // 如果有选中的节点或连接线，不清除样式，保持选中状态
      if (this.selectedNode || this.selectedLink) {
        return;
      }

      // 恢复节点默认样式
      if (this.renderManager.renderedNodes) {
        this.renderManager.renderedNodes.each(function (d) {
          const group = d3.select(this);

          // 检查是否为特殊类型节点，如果是则保持原有边框
          const isSpecialNode = d.attribute && (d.attribute.stationstyle === "loss_pressure_half" || d.attribute.stationstyle === "disconnection");

          if (!isSpecialNode) {
            group.selectAll(".node-shape").attr("stroke", null).attr("stroke-width", null);
          }
          // 恢复透明度
          group.selectAll(".node-shape").style("opacity", 1);
        });
      }

      // 恢复连接线默认样式
      this.linkRenderer?.clearHighlightStyles();

      // 恢复标签默认样式
      if (this.labelRenderer) {
        this.labelRenderer.clearHighlightStyles(this.stateManager.showNodeLabels, this.stateManager.showLinkLabels);
      }
    },

    /**
     * 高亮与指定节点相关的节点和连接线
     * @param {Object} node 要高亮的节点数据
     */
    highlightRelated(node) {
      // 找出与该节点相连的所有边
      const connectedLinks = this.links.filter((link) => link.source.id === node.id || link.target.id === node.id);

      // 找出相连的节点ID
      const connectedNodeIds = new Set();
      connectedLinks.forEach((link) => {
        if (link.source.id === node.id) {
          connectedNodeIds.add(link.target.id);
        } else {
          connectedNodeIds.add(link.source.id);
        }
      });

      // 更新高亮状态
      this.stateManager.clearHighlightedNodes();
      this.stateManager.addHighlightedNode(node.id);
      connectedNodeIds.forEach((id) => this.stateManager.addHighlightedNode(id));

      // 应用高亮样式
      this.nodeRenderer.applyHighlightStyles(node, connectedNodeIds);
      this.linkRenderer.applyHighlightStyles(node);
      this.iconRenderer.applyIconHighlightStyles(node);
      this.labelRenderer.applyHighlightStyles(this.stateManager.highlightedNodeIds, node);
      this.labelRenderer.applyLinkHighlightStyles(node);
    },

    /**
     * 清除所有高亮效果
     */
    clearHighlight() {
      this.selectedNode = null;
      this.stateManager.setSelectedNode(null);
      this.stateManager.clearHighlightedNodes();

      // 清除样式
      this.nodeRenderer.clearHighlightStyles();
      this.linkRenderer.clearHighlightStyles();
      this.iconRenderer.clearIconHighlightStyles();
      this.labelRenderer.clearHighlightStyles(this.stateManager.showNodeLabels, this.stateManager.showLinkLabels);
    },

    /**
     * 清除连接线的高亮效果
     */
    clearLinkHighlight() {
      this.selectedLink = null;
      this.stateManager.setSelectedLink(null);
      this.clearHighlight();
    },

    /**
     * 根据搜索查询高亮节点
     */
    searchNode() {
      const query = this.stateManager?.searchQuery;
      if (!query) return;
      if (!this.highlightNodeByIdentifier(query)) {
        console.log("未找到匹配的节点");
      }
    },

    /**
     * 根据标识符（ID、名称、描述等）高亮节点
     * @param {String|Number} identifier 节点标识符
     * @returns {Boolean} 是否找到并高亮节点
     */
    highlightNodeByIdentifier(identifier) {
      if (!identifier || !this.nodes.length) return false;

      const node = graphUtils.findNode(this.nodes, identifier);
      if (node) {
        if (this.selectedNode) {
          this.clearHighlight();
        }
        this.highlightNode(node.id);
        this.$emit("node-highlighted", node);
        return true;
      } else {
        console.log(`未找到匹配的节点: ${identifier}`);
        return false;
      }
    },

    /**
     * 根据节点ID高亮节点，并缩放至该节点
     * @param {String|Number} nodeId 节点ID
     */
    highlightNode(nodeId) {
      const node = this.nodeMap.get(nodeId);
      if (!node) {
        console.log("未找到指定ID的节点");
        return;
      }

      try {
        this.highlightRelated(node);

        // 缩放到节点
        const scale = 2;
        const x = this.width / 2 - (node.x || 0) * scale;
        const y = this.height / 2 - (node.y || 0) * scale;

        this.renderManager.svg.transition().duration(750).call(this.renderManager.zoom.transform, d3.zoomIdentity.translate(x, y).scale(scale));

        this.selectedNode = node;
        this.stateManager.setSelectedNode(node);
      } catch (error) {
        console.error("高亮节点时出错:", error);
      }
    },

    /**
     * 更新搜索结果，并传递给工具栏组件
     * @param {String} query 搜索查询字符串
     */
    updateSearchResults(query) {
      const { filteredNodes, filteredLinks } = graphUtils.filterGraphElements(this.nodes, this.links, query);
      this.setFilteredResults(filteredNodes, filteredLinks);
    },

    /**
     * 将筛选后的节点和连接线结果设置到工具栏组件
     * @param {Array} nodes 筛选后的节点数组
     * @param {Array} links 筛选后的连接线数组
     */
    setFilteredResults(nodes, links) {
      if (this.$refs.toolbar) {
        this.$refs.toolbar.setFilteredData(nodes, links);
      }
    },

    /**
     * 处理从工具栏选择搜索结果节点的事件
     * @param {Object} node 被选中的节点数据
     */
    selectSearchResult(node) {
      this.highlightNode(node.id);
    },

    /**
     * 处理从工具栏选择搜索结果连接线的事件
     * @param {Object} link 被选中的连接线数据
     */
    selectSearchLink(link) {
      if (this.selectedNode) {
        this.clearHighlight();
      }
      if (this.selectedLink === link) {
        this.clearLinkHighlight();
        return;
      }
      this.selectedLink = link;
      this.stateManager.setSelectedLink(link);
      this.highlightLink(link);
    },

    /**
     * 高亮指定的连接线，并缩放至该连接线
     * @param {Object} link 要高亮的连接线数据
     */
    highlightLink(link) {
      const { sourceNode, targetNode } = graphUtils.getLinkEndNodes(link, this.nodeMap);
      if (!sourceNode || !targetNode) {
        console.error("无法找到连接线的源节点或目标节点");
        return;
      }

      // 应用连接线高亮
      this.linkRenderer.applyLinkHighlightStyles(link, sourceNode, targetNode);
      this.labelRenderer.applyLinkSelectionHighlightStyles(link, sourceNode, targetNode);

      // 缩放到连接线
      this.renderManager.zoomToFitNodes([sourceNode, targetNode]);
    },

    /**
     * 处理从工具栏选择类别的事件
     * @param {String|Number|null} category 被选中的类别，或null表示清除筛选
     */
    handleCategorySelect(category) {
      console.log("类别选择:", category);

      if (!this.renderManager.renderedNodes) {
        return;
      }

      if (category === null) {
        this.clearCategoryFilter();
        return;
      }

      // 清除其他高亮
      this.clearHighlight();
      if (this.selectedLink) {
        this.clearLinkHighlight();
      }

      // 应用类别筛选
      this.applyCategoryFilter(category);
    },

    /**
     * 应用类别筛选，使匹配类别的节点和连接线高亮，其余变暗
     * @param {String|Number} category 要筛选的类别
     */
    applyCategoryFilter(category) {
      // 节点筛选
      this.renderManager.renderedNodes.each(function (d) {
        const group = d3.select(this);
        let isMatchingCategory = false;

        if (category === "loss_pressure") {
          isMatchingCategory = d.attribute && d.attribute.stationstyle === "loss_pressure";
        } else if (category === "loss_pressure_half") {
          isMatchingCategory = d.attribute && d.attribute.stationstyle === "loss_pressure_half";
        } else if (category === "disconnection") {
          isMatchingCategory = d.attribute && d.attribute.stationstyle === "disconnection";
        } else if (category === "series_power_supply") {
          isMatchingCategory = d.attribute && d.attribute.stationstyle === "series_power_supply";
        } else {
          isMatchingCategory = String(d.category) === String(category);
        }

        group.style("opacity", isMatchingCategory ? 1 : 0.2);
      });

      // 连接线筛选
      this.linkRenderer.applyCategoryFilter(category, this.nodes);

      // 隐藏连接线标签
      if (this.renderManager.renderedLinkLabels) {
        this.renderManager.renderedLinkLabels.style("display", "none");
      }
    },

    /**
     * 清除类别筛选效果，恢复所有元素的默认显示
     */
    clearCategoryFilter() {
      // 恢复节点透明度
      if (this.renderManager.renderedNodes) {
        this.renderManager.renderedNodes.style("opacity", 1);
      }

      // 恢复连接线
      this.linkRenderer.clearCategoryFilter();

      // 恢复标签
      if (this.renderManager.renderedLinkLabels) {
        this.renderManager.renderedLinkLabels.style("display", this.stateManager.showLinkLabels ? "block" : "none");
      }
    },

    /**
     * 清除图谱所有元素和状态，重置组件
     */
    clearGraph() {
      if (this.renderManager) {
        this.renderManager.clear();
      }

      if (this.simulation) {
        this.simulation.stop();
        this.simulation = null;
      }

      // 重置数据
      this.nodes = [];
      this.links = [];
      this.nodeMap.clear();
      this.categoryStats = {};

      // 重置状态
      this.stateManager?.reset();
      this.loading = false;
      this.loadError = null;
      this.selectedNode = null;
      this.selectedLink = null;
      this.hoveredNode = null;
      this.tooltipData = null;
    },

    /**
     * 重新加载图谱数据，会先清空现有图谱
     */
    reloadData() {
      this.clearGraph();
      this.loadGraphData();
    },

    /**
     * 更新图谱数据，用于增量更新
     */
    updateGraph() {
      if (!this.graphData) {
        console.error("无图谱数据可用");
        return;
      }

      console.log("开始增量更新图谱...");

      // 增量更新不显示loading状态，直接更新数据
      // this.loading = true;
      // this.stateManager.setLoadingState(true);

      // 确保容器尺寸有效
      this.initDimensions();
      if (this.width === 0 || this.height === 0) {
        console.warn("容器尺寸无效，延迟更新");
        // this.loading = false;
        // this.stateManager.setLoadingState(false);
        setTimeout(() => this.updateGraph(), 100);
        return;
      }

      this.processData(true); // 传递增量更新标识

      if (!this.renderManager.svg) {
        console.log("图谱尚未初始化，先进行初始化渲染");
        this.initGraph();
        return;
      }

      // 确保首次渲染已经完成
      if (this.loading) {
        console.log("初始渲染尚未完成，等待完成后再执行增量更新");
        setTimeout(() => this.updateGraph(), 300);
        return;
      }

      console.log("更新现有图谱...");

      // 使用稳定的分阶段更新策略
      this.$nextTick(() => {
        console.log("开始分阶段增量更新...");

        // 第一阶段：更新节点和连接线
        Promise.resolve()
          .then(() => {
            console.log("阶段1：更新节点");
            this.nodeRenderer.update(this.nodes);
            return new Promise((resolve) => setTimeout(resolve, 100));
          })
          .then(() => {
            console.log("阶段2：更新连接线");
            this.linkRenderer.update(this.links, this.nodeMap);
            return new Promise((resolve) => setTimeout(resolve, 150));
          })
          .then(() => {
            console.log("阶段3：更新连接线图标");
            this.iconRenderer.updateLinkIcons(this.links, this.nodeMap);
            return new Promise((resolve) => setTimeout(resolve, 100));
          })
          .then(() => {
            console.log("阶段4：更新标签");
            this.labelRenderer.updateNodeLabels(this.nodes);
            // 增量更新模式下优化连接线标签更新，避免闪烁
            return new Promise((resolve) => {
              // 直接更新连接线标签，不使用多重延迟
              this.labelRenderer.updateLinkLabelsForIncremental(this.links);
              // 简化验证步骤，减少延迟
              setTimeout(() => {
                this.labelRenderer.validateAndFixLinkLabels();
                resolve();
              }, 100);
            });
          })
          .then(() => {
            console.log("阶段5：设置事件处理");
            this.setupGraphEvents();
            return new Promise((resolve) => setTimeout(resolve, 100));
          })
          .then(() => {
            console.log("阶段6：轻微调整布局（保持文字位置）");
            if (this.simulation) {
              this.simulation.nodes(this.nodes);
              this.simulation.force("link").links(this.links);

              // 增量更新使用非常温和的参数，避免重排
              this.simulation.alpha(0.05).alphaDecay(0.3).restart();

              return new Promise((resolve) => {
                setTimeout(() => {
                  this.simulation.stop();
                  this.stateManager.setSimulationStopped(true);

                  // 固定节点位置
                  this.nodes.forEach((node) => {
                    node._x = node.x;
                    node._y = node.y;
                    node.fx = node.x;
                    node.fy = node.y;
                  });

                  // 最终验证连接线标签
                  setTimeout(() => {
                    this.labelRenderer.validateAndFixLinkLabels();

                    // 清除loading状态（增量更新模式不需要）
                    // this.loading = false;
                    // this.stateManager.setLoadingState(false);

                    console.log("<<======增量更新完成========>>");
                    this.$emit("graph-finished");
                    resolve();
                  }, 300);
                }, 500); // 减少等待时间，因为使用了更小的alpha值
              });
            }
          })
          .catch((error) => {
            console.error("增量更新过程中出错:", error);
            // this.loading = false;
            // this.stateManager.setLoadingState(false);
          });
      });
    },

    /**
     * 设置ResizeObserver以监听容器尺寸变化
     */
    setupResizeObserver() {
      this.resizeObserver = new ResizeObserver(() => {
        this.handleResize();
      });

      if (this.$refs.graphContainer) {
        this.resizeObserver.observe(this.$refs.graphContainer);
      }

      window.addEventListener("resize", this.handleResize);
    },

    /**
     * 设置事件总线监听器
     */
    setupEventListeners() {
      // 注册事件总线监听器
      this.$bus &&
        this.$bus.$on("flow-graph-locate", (payload) => {
          console.log("接收到元素定位请求:", payload);
          if (!this.loading && this.nodes.length > 0) {
            if (payload.type === "node" && (payload.id || payload.name)) {
              this.highlightNodeByIdentifier(payload.id || payload.name);
            } else if (payload.type === "link" && payload.id) {
              this.highlightLinkById(payload.id);
            } else if (payload.type === "link" && payload.sourceId && payload.targetId) {
              this.highlightLinkByNodes(payload.sourceId, payload.targetId);
            }
          }
        });
    },

    /**
     * 处理容器尺寸变化事件
     */
    handleResize() {
      const oldWidth = this.width;
      const oldHeight = this.height;

      this.initDimensions();

      if ((oldWidth === 0 || oldHeight === 0) && this.width > 0 && this.height > 0 && this.stateManager?.pendingLoadDueToZeroDimensions) {
        console.log("容器尺寸已更新，重新尝试加载");
        this.loadGraphData();
      } else if (this.renderManager?.svg) {
        this.renderManager.updateSize(this.width, this.height);
      }
    },

    /**
     * 根据连接线ID高亮连接线
     * @param {String|Number} linkId 连接线ID
     */
    highlightLinkById(linkId) {
      const link = graphUtils.findLinkById(this.links, linkId);
      if (!link) {
        console.warn(`未找到ID为 ${linkId} 的连接线`);
        return;
      }

      if (this.selectedNode) {
        this.clearHighlight();
      }
      if (this.selectedLink === link) {
        this.clearLinkHighlight();
        return;
      }

      this.selectedLink = link;
      this.stateManager.setSelectedLink(link);
      this.highlightLink(link);
    },

    /**
     * 根据源节点和目标节点ID高亮连接线
     * @param {String|Number} sourceId 源节点ID
     * @param {String|Number} targetId 目标节点ID
     */
    highlightLinkByNodes(sourceId, targetId) {
      const foundLink = graphUtils.findLinkByNodes(this.links, sourceId, targetId);
      if (foundLink) {
        this.highlightLinkById(foundLink.id);
      } else {
        console.warn(`未找到连接 ${sourceId} 和 ${targetId} 的连接线`);
      }
    },

    /**
     * 鼠标移出图谱容器事件处理
     */
    onMouseLeave() {
      const lastHovered = this.hoveredNode;
      this.hideTooltip();

      if (this.labelRenderer && this.stateManager.showNodeLabels && lastHovered && lastHovered.id !== null) {
        this.labelRenderer.setNodeLabelHoverHighlight(lastHovered.id, false);
      }

      this.hoveredNode = null;
      this.stateManager.setHoveredNode(null);

      // 移除条件限制，始终清除悬停样式
      this.clearHoverStyles();
    },

    /**
     * 动态加载主题样式文件
     * @param {String} themeName 主题名称 (e.g., "light", "dark")
     */
    loadThemeStyles(themeName) {
      import(`./theme/${themeName}/index.scss`)
        .then(() => {
          console.log(`主题 '${themeName}' 样式已加载`);
        })
        .catch((err) => {
          console.error(`加载主题 '${themeName}' 样式失败:`, err);
        });
    },

    // 公开方法 (Public API)

    /**
     * 公开方法：加载图谱数据 (如果未在自动加载)
     * @returns {Promise<Boolean>|Boolean} 加载成功返回true，否则返回false
     */
    loadData() {
      if (this.loading) {
        console.warn("数据正在加载中，请勿重复调用");
        return false;
      }
      return this.loadGraphData();
    },

    // 辅助方法 (Utility Methods)
    /**
     * 获取指定类别的颜色
     * @param {String|Number} categoryInput 类别标识
     * @returns {String} 颜色值 (e.g., "#RRGGBB")
     */
    getCategoryColor(categoryInput) {
      return this.dataProcessor?.getCategoryColor(categoryInput) || "#999999";
    },

    /**
     * 获取指定类别的名称
     * @param {String|Number} categoryId 类别ID
     * @returns {String} 类别名称
     */
    getCategoryName(categoryId) {
      return this.dataProcessor?.getCategoryName(categoryId) || "未知类别";
    },

    /**
     * 公开方法：设置所有图标的可见性
     * @param {boolean} visible 是否显示图标
     */
    setIconsVisibility(visible) {
      this.iconRenderer?.setIconsVisibility(visible);
    },

    /**
     * 公开方法：获取当前可用的所有图标名称列表
     * @returns {Array<String>} 图标名称数组
     */
    getAvailableIcons() {
      return this.iconRenderer?.getAvailableIcons() || [];
    },

    /**
     * 组件销毁前的清理工作
     */
    cleanup() {
      window.removeEventListener("resize", this.handleResize);

      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
      }

      if (this.simulation) {
        this.simulation.stop();
      }

      // 移除事件总线监听器
      this.$bus && this.$bus.$off("flow-graph-locate");

      // 清理渲染器
      this.iconRenderer?.cleanup();
      this.menuManager?.destroy();

      this.clearGraph();
    },

    /**
     * 处理右键菜单操作
     * @param {string} action 菜单项的action
     */
    handleMenuAction(action) {
      if (action === "reload") {
        this.reloadData();
      } else if (action === "export") {
        this.exportGraphData();
      }
      // 根据需要处理其他 action
    },

    /**
     * 设置右键菜单
     */
    setupContextMenu() {
      if (this.$refs.graphContainer && this.menuManager) {
        this.$refs.graphContainer.addEventListener("contextmenu", (event) => {
          event.preventDefault();
          this.menuManager.show(event.clientX, event.clientY);
        });
      }
    },

    /**
     * 导出当前图谱数据为JSON文件
     */
    exportGraphData() {
      try {
        if (!this.nodes || this.nodes.length === 0) {
          this.$message?.warning?.("没有可导出的图谱数据") || console.warn("没有可导出的图谱数据");
          return;
        }

        console.log("开始导出图谱数据...");

        // 导出数据，传递画布尺寸用于正确的坐标转换
        const exportedData = DataExportUtils.exportGraphData(this.nodes, this.links, this.posLayoutScale, this.graphData, this.width, this.height);

        // 验证导出数据
        const validation = DataExportUtils.validateExportData(exportedData);
        if (!validation.isValid) {
          console.error("导出数据验证失败:", validation.errors);
          this.$message?.error?.("导出数据验证失败") || console.error("导出数据验证失败");
          return;
        }

        // 创建导出摘要
        const summary = DataExportUtils.createExportSummary(exportedData);
        console.log("导出数据摘要:", summary);

        // 下载文件
        DataExportUtils.downloadAsJson(exportedData, "flow-graph-data");

        // 显示成功消息
        const message = `图谱数据导出成功！节点: ${summary.totalNodes}个, 连接线: ${summary.totalLinks}个`;
        this.$message?.success?.(message) || console.log(message);

        // 触发导出事件
        this.$emit("graph-exported", {
          data: exportedData,
          summary: summary
        });
      } catch (error) {
        console.error("导出图谱数据失败:", error);
        const errorMessage = "导出图谱数据失败: " + error.message;
        this.$message?.error?.(errorMessage) || console.error(errorMessage);
      }
    }
  },
  computed: {
    /**
     * 计算工具栏是否真实可见 (基于toolbar prop 和 legend/search 的显示状态)
     * @returns {Boolean}
     */
    isToolbarEffectivelyVisible() {
      return this.toolbar !== false && (this.shouldShowLegend || this.shouldShowSearch);
    },
    /**
     * 计算是否应显示图例 (基于toolbar prop)
     * @returns {Boolean}
     */
    shouldShowLegend() {
      return this.toolbar === true || this.toolbar === "legend";
    },
    /**
     * 计算是否应显示搜索框 (基于toolbar prop)
     * @returns {Boolean}
     */
    shouldShowSearch() {
      return this.toolbar === true || this.toolbar === "search";
    }
  }
};
</script>

<style lang="scss" scoped>
// 主容器
.knowledge-graph-container {
  width: 100%;
  height: 100%;
  position: relative;
}

// 图形容器
.graph-container {
  width: 100%;
  height: calc(100% - 40px);
  overflow: hidden;
}

.highlight {
  padding: 0 2px;
  border-radius: 2px;
  font-weight: bold;
  text-shadow: none;
}

// 提示框
.tooltip {
  position: absolute;
  z-index: 100;
  padding: 10px;
  border-radius: 4px;
  max-width: 300px;
  pointer-events: none;
  transition: opacity 0.3s;
}

.tooltip-title {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 14px;
  padding-bottom: 5px;
}

.tooltip-desc {
  font-size: 12px;
  margin-bottom: 5px;
}

.tooltip-category {
  font-size: 12px;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.category-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
  display: inline-block;
}

.tooltip-attrs {
  font-size: 11px;
}
</style>
