<template>
  <div class="layout-navbars-breadcrumb-index">
    <Logo v-if="setIsShowLogo" />
    <Breadcrumb />
    <Horizontal
      :menuList="menuList"
      v-if="isLayoutHorizontal"
    />
    <User />
  </div>
</template>

<script>
import Breadcrumb from "@/layout/navBars/breadcrumb/breadcrumb.vue";
import User from "@/layout/navBars/breadcrumb/user.vue";
import Logo from "@/layout/logo/index.vue";
import Horizontal from "@/layout/navMenu/horizontal.vue";
export default {
  name: "layoutNavBars",
  components: { Breadcrumb, User, Logo, Horizontal },
  data() {
    return {
      menuList: []
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    // 设置 logo 是否显示
    setIsShowLogo() {
      const { isShowLogo, layout } = this.getThemeConfig;
      return isShowLogo && layout !== "defaults";
    },
    // 设置是否显示横向菜单
    isLayoutHorizontal() {
      const { layout } = this.getThemeConfig;
      return layout === "horizontal" || layout === "mix";
    }
  },
  mounted() {
    this.setFilterRoutes();
  },
  methods: {
    // 设置路由的过滤
    setFilterRoutes() {
      this.menuList = this.filterRoutesFun(this.$store.state.routesList.routesList);
    },
    // 设置路由的过滤递归函数
    filterRoutesFun(arr) {
      return arr
        .filter((item) => !item.meta.isHide)
        .map((item) => {
          item = Object.assign({}, item);
          if (item.children) item.children = this.filterRoutesFun(item.children);
          return item;
        });
    }
  },
  watch: {
    // 监听 vuex 数据变化
    "$store.state": {
      handler(val) {
        if (val.routesList.routesList.length === this.menuList.length) return false;
        this.setFilterRoutes();
      },
      deep: true
    }
  }
};
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-index {
  display: flex;
  align-items: center;
  height: inherit;
  padding: 0 20px;
  background-color: var(--nari-bg-header);
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}
</style>
