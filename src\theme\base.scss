@import "./common/transition.scss";

$black: #001237;
:root {
  --nari-c-primary: #409eff;
  --nari-c-primary-light-1: #53a7ff;
  --nari-c-primary-light-2: #66b1ff;
  --nari-c-primary-light-3: #79bbff;
  --nari-c-primary-light-4: #8cc4ff;
  --nari-c-primary-light-5: #9fceff;
  --nari-c-primary-light-6: #b2d8ff;
  --nari-c-primary-light-7: #c5e1ff;
  --nari-c-primary-light-8: #d8ebff;
  --nari-c-primary-light-9: #ebf5ff;
  --nari-c-success: #67c23a;
  --nari-c-warning: #e6a23c;
  --nari-c-danger: #f56c6c;
  --nari-c-info: #909399;
  --nari-c-blue: #4988d4;
  --nari-c-blue-mute: #0095ff;
  --nari-c-blue-soft: #0673b3;
  --nari-c-white: #ffffff;
  --nari-c-white-mute: #f1f1f1;
  --nari-c-white-soft: #f9f9f9;
  --nari-c-black: #001237;
  --nari-c-black-mute: #002346;
  --nari-c-black-soft: #0a0d13;
  --nari-c-green: rgba(60, 184, 188, 1);
  --nari-c-green-1: rgba(60, 184, 188, 0.6);
  --nari-c-green-2: rgba(60, 184, 188, 0.4);
  --nari-c-green-3: rgba(27, 255, 255, 1);
  --nari-c-green-4: rgba(27, 255, 255, 0.5);
  --nari-c-indigo: #234e92;
  --nari-c-purple: rgba(75, 30, 133, 1);
  --nari-c-purple-1: rgba(75, 30, 133, 0.5);
  --nari-c-purple-2: rgba(75, 30, 133, 0.4);
  --nari-c-purple-3: rgba(143, 76, 232, 1);
  --nari-c-purple-4: rgba(143, 76, 232, 0.4);
  --nari-c-gray: #8e8e8e;
  --nari-c-gray-light-1: #aeaeae;
  --nari-c-gray-light-2: #c7c7c7;
  --nari-c-gray-light-3: #d1d1d1;
  --nari-c-gray-light-4: #e5e5e5;
  --nari-c-gray-light-5: #f2f2f2;
  --nari-c-gray-light-6: #efefef;
  --nari-c-gray-dark-1: #636363;
  --nari-c-gray-dark-2: #484848;
  --nari-c-gray-dark-3: #3a3a3a;
  --nari-c-gray-dark-4: #282828;
  --nari-c-gray-dark-5: #202020;
  --nari-c-setting-header: var(--nari-c-black-mute);
  --nari-c-setting-aside: var(--nari-c-gray-light-1);
  --nari-c-setting-main: var(--nari-c-gray-light-4);
  --nari-header-height: "60px";
}

// 当HTML的data-theme为dark时，样式引用dark
// data-theme为其他值时，就采用组件库的默认样式
// 这里我只定义了两套主题方案，想要再多只需在`$themes`里加就行了
// 注意一点是，每套配色方案里的key可以自定义但必须一致，不然就会混乱
$themes: (
  light: (
    // 背景
    c-bg: var(--nari-c-white),
    c-bg-mute: var(--nari-c-white-mute),
    c-bg-soft: var(--nari-c-white-soft),
    // 字体
    c-text-1: var(--nari-c-gray-dark-1),
    c-text-2: var(--nari-c-gray-dark-2),
    c-text-3: var(--nari-c-gray-dark-3),
    c-text-4: var(--nari-c-gray-dark-4),
    c-text-5: var(--nari-c-gray-dark-5),
    c-text-inverse-1: var(--nari-c-gray-light-1),
    c-text-inverse-2: var(--nari-c-gray-light-2),
    c-text-inverse-3: var(--nari-c-gray-light-3),
    c-text-inverse-4: var(--nari-c-gray-light-4),
    c-text-inverse-5: var(--nari-c-gray-light-5),
    // 边框
    c-divider-light-1: var(--nari-c-gray-dark-4),
    c-divider-light-2: var(--nari-c-gray-dark-5),
    c-border: var(--nari-c-gray-light-5),
    c-border-base: var(--nari-c-gray-light-4),
    c-border-hover: var(--nari-c-gray-dark-1),
    bg-aside: var(--nari-c-black),
    bg-header: var(--nari-c-black),
    bg-main: var(--nari-c-gray-light-6),
    c-aside: var(--nari-c-white),
    c-header: var(--nari-c-white),
    c-card-title: var(--nari-c-gray-dark-4),
    c-hover: var(--nari-c-primary-light-9),
    c-table-bg: var(--nari-c-white),
    c-table-header-bg: var(--nari-c-gray-light-5),
    c-table-row-stripe-bg: var(--nari-c-white-soft),
    c-table-row-hover-bg: var(--nari-c-primary-light-9),
    c-table-row-highlight-bg: var(--nari-c-primary-light-9),
    c-table-head-text: var(--nari-c-gray-dark-5),
    c-table-body-text: var(--nari-c-gray-dark-5)
  ),
  dark: (
    // 背景
    c-bg: var(--nari-c-black),
    c-bg-mute: var(--nari-c-black-mute),
    c-bg-soft: var(--nari-c-black-soft),
    // 字体
    c-text-1: var(--nari-c-gray-light-1),
    c-text-2: var(--nari-c-gray-light-2),
    c-text-3: var(--nari-c-gray-light-3),
    c-text-4: var(--nari-c-gray-light-4),
    c-text-5: var(--nari-c-gray-light-5),
    c-text-inverse-1: var(--nari-c-gray-dark-1),
    c-text-inverse-2: var(--nari-c-gray-dark-2),
    c-text-inverse-3: var(--nari-c-gray-dark-3),
    c-text-inverse-4: var(--nari-c-gray-dark-4),
    c-text-inverse-5: var(--nari-c-gray-dark-5),
    // 边框
    c-divider-dark-1: var(--nari-c-gray-dark-1),
    c-divider-dark-2: var(--nari-c-gray-dark-3),
    c-border: var(--nari-c-gray-dark-3),
    c-border-base: var(--nari-c-gray-dark-1),
    c-border-hover: var(--nari-c-gray-dark-1),
    bg-aside: var(--nari-c-black),
    bg-header: var(--nari-c-black),
    bg-main: darken($black, 3%),
    c-aside: var(--nari-c-white-mute),
    c-header: var(--nari-c-white-mute),
    c-card-title: var(--nari-c-white),
    c-hover: darken($black, 3%),
    c-table-bg: var(--nari-c-black),
    c-table-header-bg: var(--nari-c-indigo),
    c-table-row-stripe-bg: var(--nari-c-black-mute),
    c-table-row-hover-bg: var(--nari-c-blue),
    c-table-row-highlight-bg: var(--nari-c-blue),
    c-table-head-text: var(--nari-c-gray-light-5),
    c-table-body-text: var(--nari-c-gray-light-5)
  )
);

// 遍历主题map
@mixin themeify {
  @each $theme-name, $theme-map in $themes {
    :root[data-theme="#{$theme-name}"] {
      @each $key, $value in $theme-map {
        --nari-#{$key}: #{$value};
      }
    }
  }
}

@include themeify();
