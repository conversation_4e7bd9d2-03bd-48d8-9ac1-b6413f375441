<template>
  <div
    class="toolbar"
    v-if="showLegend || showSearch"
  >
    <!-- 图例 -->
    <div
      class="legend-container"
      v-if="showLegend && Object.keys(filteredCategoryStats).length > 0"
    >
      <div
        v-for="(stat, category) in filteredCategoryStats"
        :key="category"
        class="legend-item"
        @click="selectCategory(category)"
      >
        <template v-if="category === 'loss_pressure_half'">
          <!-- 非全站失压 - 半色半灰图标 -->
          <div
            class="legend-special-icon loss-pressure-half-icon"
            :class="{ active: selectedCategory === category }"
            :style="{ '--category-color': getCategoryColor(category) }"
          >
            <div class="half-left"></div>
            <div class="half-right"></div>
          </div>
          <span class="legend-text">{{ stat.name }}: {{ stat.count }}</span>
        </template>
        <template v-else-if="category === 'disconnection'">
          <!-- 解列运行 - 双半圆同色图标 -->
          <div
            class="legend-special-icon disconnection-unified-icon"
            :class="{ active: selectedCategory === category }"
            :style="{ '--category-color': getCategoryColor(0) }"
          >
            <div class="half-left"></div>
            <div class="half-right"></div>
          </div>
          <span class="legend-text">{{ stat.name }}: {{ stat.count }}</span>
        </template>
        <template v-else-if="category === 'loss_pressure'">
          <!-- 全站失压 - 灰色图标 -->
          <span
            class="legend-dot loss-pressure-icon"
            :style="{ backgroundColor: stat.color }"
            :class="{ active: selectedCategory === category }"
          ></span>
          <span class="legend-text">{{ stat.name }}: {{ stat.count }}</span>
        </template>
        <template v-else>
          <!-- 普通类别 -->
          <span
            class="legend-dot"
            :style="{ backgroundColor: getCategoryColor(category) }"
            :class="{ active: selectedCategory === category }"
          ></span>
          <span class="legend-text">{{ getCategoryName(category) }}: {{ stat.count }}</span>
        </template>
      </div>
    </div>

    <!-- 搜索容器 -->
    <div
      class="search-container"
      v-if="showSearch"
    >
      <div class="search-input-wrapper">
        <div class="search-icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle
              cx="11"
              cy="11"
              r="8"
            ></circle>
            <line
              x1="21"
              y1="21"
              x2="16.65"
              y2="16.65"
            ></line>
          </svg>
        </div>
        <input
          v-model="searchQuery"
          placeholder="搜索节点或连接线..."
          @input="updateSearchResults"
          @focus="showSearchResults = true"
          @blur="hideSearchResultsDelayed"
        />
        <!-- 搜索结果下拉框 -->
        <div
          class="search-results"
          v-show="showSearchResults && (filteredNodes.length > 0 || filteredLinks.length > 0)"
        >
          <!-- 节点搜索结果 -->
          <div
            v-if="filteredNodes.length > 0"
            class="search-result-section"
          >
            <div class="search-result-section-title">节点</div>
            <div
              v-for="node in filteredNodes"
              :key="'node-' + node.id"
              class="search-result-item"
              @click="selectSearchResult(node)"
            >
              <div
                class="search-result-color"
                :style="{ backgroundColor: getCategoryColor(node.category) }"
              ></div>
              <div class="search-result-content">
                <div
                  class="search-result-id"
                  v-html="highlightMatch(node.id, searchQuery)"
                ></div>
                <div
                  class="search-result-name"
                  v-html="highlightMatch(node.des || node.name, searchQuery)"
                ></div>
              </div>
            </div>
          </div>

          <!-- 连接线搜索结果 -->
          <div
            v-if="filteredLinks.length > 0"
            class="search-result-section"
          >
            <div class="search-result-section-title">连接线</div>
            <div
              v-for="link in filteredLinks"
              :key="'link-' + (link.id || link.source.id + '-' + link.target.id)"
              class="search-result-item"
              @click="selectSearchLink(link)"
            >
              <div
                class="search-result-color search-result-line"
                :style="{
                  background: 'linear-gradient(to right, #9E9E9E, #9E9E9E)'
                }"
              ></div>
              <div class="search-result-content">
                <div
                  class="search-result-id"
                  v-html="highlightMatch(link.id || '连接线', searchQuery)"
                ></div>
                <div class="search-result-name">
                  <span v-html="highlightMatch(link.name || '无名称', searchQuery)"></span>
                  <small class="search-result-endpoints">
                    {{ typeof link.source === "object" ? link.source.name || link.source.id : link.source }}
                    →
                    {{ typeof link.target === "object" ? link.target.name || link.target.id : link.target }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="filteredNodes.length === 0 && filteredLinks.length === 0 && showSearchResults && searchQuery"
          class="no-results"
        >
          无匹配结果
        </div>
      </div>
      <button @click="search">搜索</button>
    </div>
  </div>
</template>

<script>
export default {
  name: "FlowGraphToolbar",
  props: {
    categoryStats: {
      type: Object,
      default: () => ({})
    },
    getCategoryColor: {
      type: Function,
      required: true
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchQuery: "",
      showSearchResults: false,
      filteredNodes: [],
      filteredLinks: [],
      selectedCategory: null
    };
  },
  computed: {
    filteredCategoryStats() {
      return Object.entries(this.categoryStats).reduce((acc, [category, stat]) => {
        if (stat.count > 0) {
          acc[category] = stat;
        }
        return acc;
      }, {});
    }
  },
  methods: {
    search() {
      this.$emit("search", this.searchQuery);
    },
    updateSearchResults() {
      this.$emit("update-search-results", this.searchQuery);
    },
    hideSearchResultsDelayed() {
      setTimeout(() => {
        this.showSearchResults = false;
      }, 200);
    },
    selectSearchResult(node) {
      // 更新搜索框显示为节点描述，如果没有则显示名称
      this.searchQuery = node.des || node.name || node.id;
      this.showSearchResults = false;
      this.$emit("select-node", node);
    },
    selectSearchLink(link) {
      this.showSearchResults = false;
      this.$emit("select-link", link);
    },
    highlightMatch(text, query) {
      if (!query || !text) return text;
      const q = query.trim().toLowerCase();
      const t = String(text);
      if (!q || !t) return t;

      const index = t.toLowerCase().indexOf(q);
      if (index === -1) return t;

      return t.substring(0, index) + `<span class="highlight">${t.substring(index, index + q.length)}</span>` + t.substring(index + q.length);
    },
    setFilteredData(nodes, links) {
      this.filteredNodes = nodes;
      this.filteredLinks = links;
      this.showSearchResults = true;
    },
    getCategoryName(category) {
      // 判断是否为数字类型的字符串
      if (typeof category === "string" && !isNaN(category)) {
        // 如果是数字字符串，尝试从父组件获取类别名称
        return this.categoryStats[category].name || `类别${category}`;
      }
      // 默认返回类别ID
      return category;
    },
    selectCategory(category) {
      // 如果点击的是已选中的类别，则取消选择
      if (this.selectedCategory === category) {
        this.selectedCategory = null;
        this.$emit("select-category", null);
      } else {
        this.selectedCategory = category;
        this.$emit("select-category", category);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  height: 40px;
  width: 100%;
  box-sizing: border-box;
}

.legend-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  padding: 3px 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.legend-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.legend-dot.active,
.legend-special-icon.active {
  box-shadow: 0 0 0 2px #fff;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}

.legend-special-icon {
  width: 12px;
  height: 12px;
  margin-right: 5px;
  position: relative;
  overflow: hidden;
}

.loss-pressure-half-icon {
  .half-left {
    position: absolute;
    width: 50%;
    height: 100%;
    left: 0;
    background-color: var(--category-color, #2196f3);
    border-radius: 12px 0 0 12px;
  }

  .half-right {
    position: absolute;
    width: 50%;
    height: 100%;
    right: 0;
    background-color: #808080; /* 灰色 */
    border-radius: 0 12px 12px 0;
  }
}

.legend-special-icon.disconnection-unified-icon {
  .half-left {
    position: absolute;
    width: 45%;
    height: 100%;
    left: 0;
    background-color: var(--category-color, #007bff) !important;
    border-radius: 12px 0 0 12px;
    display: block !important;
  }

  .half-right {
    position: absolute;
    width: 45%;
    height: 100%;
    right: 0;
    background-color: var(--category-color, #007bff) !important;
    border-radius: 0 12px 12px 0;
    display: block !important;
  }
}

.loss-pressure-icon {
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
}

.legend-text {
  white-space: nowrap;
}

.search-container {
  display: flex;
  align-items: center;
}

.search-input-wrapper {
  position: relative;
  margin-right: 5px;
}

.search-icon {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

input {
  padding: 5px 5px 5px 30px;
  border-radius: 4px;
  border: 1px solid #555;
  background-color: #333;
  color: white;
  width: 200px;
}

button {
  padding: 5px 10px;
  background-color: #2196f3;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

button:hover {
  background-color: #0b7dda;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: #333;
  border: 1px solid #555;
  border-radius: 0 0 4px 4px;
  z-index: 1000;
}

.search-result-section-title {
  padding: 8px;
  font-weight: bold;
  color: white;
  border-bottom: 1px solid #555;
  background-color: #222;
}

.search-result-item {
  display: flex;
  padding: 8px;
  cursor: pointer;
  color: white;
  border-bottom: 1px solid #444;
}

.search-result-item:hover {
  background-color: #444;
}

.search-result-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  margin-top: 5px;
}

.search-result-line {
  border-radius: 0;
  height: 2px;
  margin-top: 10px;
}

.search-result-content {
  flex: 1;
}

.search-result-id {
  font-size: 12px;
  color: #bbb;
}

.search-result-name {
  font-size: 14px;
}

.search-result-endpoints {
  display: block;
  font-size: 11px;
  color: #999;
}

.no-results {
  padding: 10px;
  text-align: center;
  color: #ccc;
  background-color: #333;
  border: 1px solid #555;
  border-radius: 0 0 4px 4px;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 1000;
}

.highlight {
  background-color: #ffd700;
  color: #000;
  font-weight: bold;
  padding: 0 2px;
  border-radius: 2px;
}
</style>
