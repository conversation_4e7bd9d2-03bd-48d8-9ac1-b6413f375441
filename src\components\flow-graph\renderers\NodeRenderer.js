import * as d3 from "d3";
import { ThemeManager } from "../core/ThemeManager.js";

/**
 * 节点渲染器类
 * 负责节点的绘制和更新
 */
export class NodeRenderer {
  constructor(renderManager, dataProcessor) {
    this.renderManager = renderManager;
    this.dataProcessor = dataProcessor;
    this.themeManager = new ThemeManager();
    this.nodeGroups = null;
    this.virtualNodeScale = 2; // 默认虚拟节点放大倍数
  }

  /**
   * 设置主题
   * @param {string} theme 主题名称 ('light' | 'dark')
   */
  setTheme(theme) {
    this.themeManager.setTheme(theme);
  }

  /**
   * 设置虚拟节点缩放倍数
   * @param {number} scale 缩放倍数
   */
  setVirtualNodeScale(scale) {
    this.virtualNodeScale = scale > 0 && scale <= 10 ? scale : 2;
  }

  /**
   * 计算节点半径，考虑虚拟节点的放大
   * @param {Object} nodeData 节点数据
   * @returns {number} 节点半径
   */
  calculateNodeRadius(nodeData) {
    let baseRadius = nodeData.symbolSize ? nodeData.symbolSize / 4 : 3;
    
    // 检查是否是虚拟节点
    if (nodeData.attribute && nodeData.attribute.sub_type === "virtual") {
      baseRadius *= this.virtualNodeScale;
      console.log(`虚拟节点 ${nodeData.id} 放大倍数: ${this.virtualNodeScale}, 基础半径: ${nodeData.symbolSize ? nodeData.symbolSize / 4 : 3}, 放大后半径: ${baseRadius}`);
    }
    
    return baseRadius;
  }

  /**
   * 渲染节点
   * @param {Array} nodes 节点数组
   * @returns {d3.Selection} 节点选择集
   */
  render(nodes) {
    const nodesContainer = this.renderManager.createGroup("nodes");

    const nodeDataSelection = nodesContainer.selectAll("g.node-group").data(nodes, (d) => d.id);

    // 移除不再需要的节点组
    nodeDataSelection.exit().remove();

    // 创建新的节点组
    const nodeGroupsEnter = nodeDataSelection
      .enter()
      .append("g")
      .attr("class", "node-group")
      .attr("transform", (d) => {
        const xPos = d.fx !== null && !isNaN(d.fx) ? d.fx : isNaN(d.x) ? this.renderManager.width / 2 : d.x;
        const yPos = d.fy !== null && !isNaN(d.fy) ? d.fy : isNaN(d.y) ? this.renderManager.height / 2 : d.y;
        return `translate(${xPos}, ${yPos})`;
      });

    // 为每个节点组绘制内部图形
    const self = this;
    nodeGroupsEnter.each(function (d) {
      const group = d3.select(this);
      const radius = self.calculateNodeRadius(d);
      const isBlinking = d.attribute && d.attribute.nodestyle === "blink";

      // 清理任何现有的图形元素 (包括可能存在的旧交互背景)
      group.selectAll(".node-interaction-background").remove();
      group.selectAll(".node-shape").remove();
      group.selectAll(".node-glow-overlay").remove(); // 清理辉光元素
      group.classed("node-blink", false);

      // 添加透明的交互背景
      self.addInteractionBackground(group, d, radius);

      // 绘制辉光和主要形状
      self.drawNodeGlow(group, d, radius, isBlinking);
      self.drawNodeShape(group, d, radius);

      // 如果节点闪烁，添加CSS类以触发动画
      if (isBlinking) {
        group.classed("node-blink", true);
      }
    });

    // 合并选择集
    this.nodeGroups = nodeDataSelection.merge(nodeGroupsEnter);
    this.renderManager.renderedNodes = this.nodeGroups;

    return this.nodeGroups;
  }

  /**
   * 更新节点
   * @param {Array} nodes 节点数组
   * @returns {d3.Selection} 节点选择集
   */
  update(nodes) {
    const self = this;
    const nodesContainer = this.renderManager.container.select("g.nodes");

    // 数据绑定到节点组
    const nodeGroups = nodesContainer.selectAll("g.node-group").data(nodes, (d) => d.id);

    // Exit 处理: 移除不再需要的节点组
    nodeGroups.exit().remove();

    // Enter 处理: 为新节点数据创建节点组
    const enterNodeGroups = nodeGroups
      .enter()
      .append("g")
      .attr("class", "node-group")
      .attr("transform", (d) => `translate(${d.x || this.renderManager.width / 2}, ${d.y || this.renderManager.height / 2})`);

    // 为Enter的节点组内部绘制初始图形
    enterNodeGroups.each(function (d) {
      const group = d3.select(this);
      const radius = self.calculateNodeRadius(d);
      const isBlinking = d.attribute && d.attribute.nodestyle === "blink";

      // 清理 (同上)
      group.selectAll(".node-interaction-background").remove();
      group.selectAll(".node-shape").remove();
      group.selectAll(".node-glow-overlay").remove(); // 清理辉光元素
      group.classed("node-blink", false);

      // 添加透明的交互背景 (同上)
      self.addInteractionBackground(group, d, radius);

      self.drawNodeGlow(group, d, radius, isBlinking);
      self.drawNodeShape(group, d, radius);

      if (isBlinking) {
        group.classed("node-blink", true);
      }
    });

    // Update 处理: 更新已存在的节点组
    nodeGroups.attr("transform", (d) => `translate(${d.x || this.renderManager.width / 2}, ${d.y || this.renderManager.height / 2})`);

    // Merge Enter + Update: 对所有当前节点组重新绘制图形
    const mergedNodeGroups = nodeGroups.merge(enterNodeGroups);
    mergedNodeGroups.each(function (d) {
      const group = d3.select(this);
      const radius = self.calculateNodeRadius(d);
      const isBlinking = d.attribute && d.attribute.nodestyle === "blink";

      // 清理节点内部的旧图形元素，以便重绘 (同上)
      group.selectAll(".node-interaction-background").remove();
      group.selectAll(".node-shape").remove();
      group.selectAll(".node-glow-overlay").remove(); // 清理辉光元素
      group.classed("node-blink", false); // 先移除闪烁类

      // 添加透明的交互背景 (同上)
      self.addInteractionBackground(group, d, radius);

      // 根据最新数据重绘辉光和形状
      self.drawNodeGlow(group, d, radius, isBlinking);
      self.drawNodeShape(group, d, radius);

      // 根据最新数据设置闪烁状态
      if (isBlinking) {
        group.classed("node-blink", true);
      }
    });

    this.nodeGroups = mergedNodeGroups;
    this.renderManager.renderedNodes = this.nodeGroups;

    return this.nodeGroups;
  }

  /**
   * 绘制节点辉光效果
   * @param {d3.Selection} group 节点组
   * @param {Object} nodeData 节点数据
   * @param {number} radius 节点半径
   * @param {boolean} isBlinking 是否闪烁
   */
  drawNodeGlow(group, nodeData, radius, isBlinking) {
    if (!isBlinking || !nodeData.attribute || !nodeData.attribute.blink_color) {
      return;
    }

    const blinkColor = nodeData.attribute.blink_color;
    const filterUrl = this.renderManager.createDynamicGlowFilter(blinkColor);

    if (!filterUrl) return;

    const stationStyle = nodeData.attribute.stationstyle;
    const glowFillColor = this.themeManager.getNodeGlowColor("fill");

    if (stationStyle === "loss_pressure_half" || stationStyle === "disconnection") {
      // 半圆辉光
      group
        .append("path")
        .attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 0 0 ${radius} L 0 0 Z`)
        .attr("class", "node-glow-overlay node-glow-overlay-left")
        .style("filter", filterUrl)
        .style("fill", glowFillColor);

      group
        .append("path")
        .attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 1 0 ${radius} L 0 0 Z`)
        .attr("class", "node-glow-overlay node-glow-overlay-right")
        .style("filter", filterUrl)
        .style("fill", glowFillColor);
    } else {
      // 圆形辉光
      group.append("circle").attr("r", radius).attr("class", "node-glow-overlay node-glow-overlay-circle").style("filter", filterUrl).style("fill", glowFillColor);
    }
  }

  /**
   * 绘制节点主要形状
   * @param {d3.Selection} group 节点组
   * @param {Object} nodeData 节点数据
   * @param {number} radius 节点半径
   */
  drawNodeShape(group, nodeData, radius) {
    // 优先使用闪烁颜色，如果没有则使用类别颜色
    const nodeCategoryColor =
      nodeData.attribute && nodeData.attribute.blink_color ? nodeData.attribute.blink_color : this.dataProcessor.getCategoryColor(nodeData.category !== undefined ? nodeData.category : "default");

    const greyColor = this.themeManager.getNodeFillColor("grey");
    const greenBorderColor = this.themeManager.getNodeStrokeColor("green");
    const borderWidth = 1.5;

    if (nodeData.state === 0) {
      // 空心节点
      group.append("circle").attr("r", radius).attr("fill", "none").attr("stroke", nodeCategoryColor).attr("stroke-width", 2).attr("class", "node-shape node-circle node-state-0");
    } else if (nodeData.attribute && nodeData.attribute.stationstyle === "series_power_supply") {
      // 串供节点
      this.drawSeriesPowerSupplyNode(group, nodeData, radius, nodeCategoryColor);
    } else if (nodeData.attribute && nodeData.attribute.stationstyle === "loss_pressure_half") {
      // 非全站失压节点
      this.drawHalfPressureLossNode(group, radius, nodeCategoryColor, greyColor, greenBorderColor, borderWidth);
    } else if (nodeData.attribute && nodeData.attribute.stationstyle === "disconnection") {
      // 解列运行节点
      this.drawDisconnectionNode(group, radius, nodeCategoryColor, greenBorderColor, borderWidth);
    } else if (nodeData.attribute && nodeData.attribute.stationstyle === "loss_pressure") {
      // 全站失压节点
      group.append("circle").attr("r", radius).attr("fill", greyColor).attr("class", "node-shape node-circle");
    } else {
      // 默认圆形节点
      group.append("circle").attr("r", radius).attr("fill", nodeCategoryColor).attr("class", "node-shape node-circle");
    }
  }

  /**
   * 绘制串供节点
   */
  drawSeriesPowerSupplyNode(group, nodeData, radius, nodeCategoryColor) {
    const separationGap = 3;
    const transparentBorderColor = this.themeManager.getNodeStrokeColor("transparent");
    const thickBorderWidth = 3;
    const nodeType = nodeData.attribute.nodetype;

    if (nodeType === "left-half") {
      // 只绘制左半圆，不绘制透明的右半圆
      group
        .append("path")
        .attr("d", `M ${-separationGap} ${-radius} A ${radius} ${radius} 0 0 0 ${-separationGap} ${radius} L ${-separationGap} 0 Z`)
        .attr("fill", nodeCategoryColor)
        .attr("stroke", transparentBorderColor)
        .attr("stroke-width", thickBorderWidth)
        .attr("class", "node-shape node-half-left node-series-power-supply");

      // 透明半圆不绘制，但保留占位元素用于事件处理和选择器兼容性
      group
        .append("path")
        .attr("d", `M ${separationGap} ${-radius} A ${radius} ${radius} 0 0 1 ${separationGap} ${radius} L ${separationGap} 0 Z`)
        .attr("fill", "none")
        .attr("stroke", "none")
        .attr("stroke-width", 0)
        .style("display", "none")
        .attr("class", "node-shape node-half-right node-series-power-supply");
    } else if (nodeType === "right-half") {
      // 透明半圆不绘制，但保留占位元素用于事件处理和选择器兼容性
      group
        .append("path")
        .attr("d", `M ${-separationGap} ${-radius} A ${radius} ${radius} 0 0 0 ${-separationGap} ${radius} L ${-separationGap} 0 Z`)
        .attr("fill", "none")
        .attr("stroke", "none")
        .attr("stroke-width", 0)
        .style("display", "none")
        .attr("class", "node-shape node-half-left node-series-power-supply");

      // 只绘制右半圆，不绘制透明的左半圆
      group
        .append("path")
        .attr("d", `M ${separationGap} ${-radius} A ${radius} ${radius} 0 0 1 ${separationGap} ${radius} L ${separationGap} 0 Z`)
        .attr("fill", nodeCategoryColor)
        .attr("stroke", transparentBorderColor)
        .attr("stroke-width", thickBorderWidth)
        .attr("class", "node-shape node-half-right node-series-power-supply");
    } else {
      // 兼容原有逻辑：两个半圆都显示
      group
        .append("path")
        .attr("d", `M ${-separationGap} ${-radius} A ${radius} ${radius} 0 0 0 ${-separationGap} ${radius} L ${-separationGap} 0 Z`)
        .attr("fill", nodeCategoryColor)
        .attr("stroke", transparentBorderColor)
        .attr("stroke-width", thickBorderWidth)
        .attr("class", "node-shape node-half-left node-series-power-supply");

      group
        .append("path")
        .attr("d", `M ${separationGap} ${-radius} A ${radius} ${radius} 0 0 1 ${separationGap} ${radius} L ${separationGap} 0 Z`)
        .attr("fill", nodeCategoryColor)
        .attr("stroke", transparentBorderColor)
        .attr("stroke-width", thickBorderWidth)
        .attr("class", "node-shape node-half-right node-series-power-supply");
    }
  }

  /**
   * 绘制非全站失压节点
   */
  drawHalfPressureLossNode(group, radius, nodeCategoryColor, greyColor, greenBorderColor, borderWidth) {
    group
      .append("path")
      .attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 0 0 ${radius} L 0 0 Z`)
      .attr("fill", nodeCategoryColor)
      .attr("stroke", greenBorderColor)
      .attr("stroke-width", borderWidth)
      .attr("class", "node-shape node-half-left");

    group
      .append("path")
      .attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 1 0 ${radius} L 0 0 Z`)
      .attr("fill", greyColor)
      .attr("stroke", greenBorderColor)
      .attr("stroke-width", borderWidth)
      .attr("class", "node-shape node-half-right");
  }

  /**
   * 绘制解列运行节点
   */
  drawDisconnectionNode(group, radius, nodeCategoryColor, greenBorderColor, borderWidth) {
    group
      .append("path")
      .attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 0 0 ${radius} L 0 0 Z`)
      .attr("fill", nodeCategoryColor)
      .attr("stroke", greenBorderColor)
      .attr("stroke-width", borderWidth)
      .attr("class", "node-shape node-half-left");

    group
      .append("path")
      .attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 1 0 ${radius} L 0 0 Z`)
      .attr("fill", nodeCategoryColor)
      .attr("stroke", greenBorderColor)
      .attr("stroke-width", borderWidth)
      .attr("class", "node-shape node-half-right");
  }

  /**
   * 应用节点高亮样式
   * @param {Object} selectedNode 选中的节点
   * @param {Set} connectedNodeIds 相连节点ID集合
   */
  applyHighlightStyles(selectedNode, connectedNodeIds) {
    if (!this.nodeGroups) return;

    const self = this;
    this.nodeGroups.each(function (d) {
      const group = d3.select(this);
      let currentRadius;
      let fillOpacity = 0.2;
      let strokeColor = null;
      let strokeWidth = null;

      if (d.id === selectedNode.id) {
        // 当前选中节点 - 使用主题感知的边框颜色
        fillOpacity = 1;
        const baseRadius = d.symbolSize ? d.symbolSize * 0.3 : 10;
        currentRadius = d.attribute && d.attribute.sub_type === "virtual" ? baseRadius * self.virtualNodeScale : baseRadius;
        strokeColor = self.themeManager.getNodeHighlightColor("selected");
        strokeWidth = 3;
      } else if (connectedNodeIds.has(d.id)) {
        // 相连节点 - 使用主题感知的边框颜色
        fillOpacity = 0.8;
        const baseRadius = d.symbolSize ? d.symbolSize * 0.25 : 7;
        currentRadius = d.attribute && d.attribute.sub_type === "virtual" ? baseRadius * self.virtualNodeScale : baseRadius;
        strokeColor = self.themeManager.getNodeHighlightColor("connected");
        strokeWidth = 2;
      } else {
        // 其他节点
        currentRadius = self.calculateNodeRadius(d);
      }

      group.attr("fill-opacity", fillOpacity);
      self.updateNodeShapeForHighlight(group, d, currentRadius, strokeColor, strokeWidth);
    });
  }

  /**
   * 更新节点形状用于高亮
   */
  updateNodeShapeForHighlight(group, nodeData, radius, strokeColor, strokeWidth) {
    if (nodeData.attribute && nodeData.attribute.stationstyle === "loss_pressure_half") {
      // loss_pressure_half节点：只对左半圆（分类色半圆）应用高亮
      group.select(".node-half-left").attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 0 0 ${radius} L 0 0 Z`).attr("stroke", strokeColor).attr("stroke-width", strokeWidth);
      group.select(".node-half-right").attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 1 0 ${radius} L 0 0 Z`).attr("stroke", strokeColor).attr("stroke-width", strokeWidth);
    } else if (nodeData.attribute && nodeData.attribute.stationstyle === "disconnection") {
      group.select(".node-half-left").attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 0 0 ${radius} L 0 0 Z`).attr("stroke", strokeColor).attr("stroke-width", strokeWidth);
      group.select(".node-half-right").attr("d", `M 0 ${-radius} A ${radius} ${radius} 0 0 1 0 ${radius} L 0 0 Z`).attr("stroke", strokeColor).attr("stroke-width", strokeWidth);
    } else if (nodeData.attribute && nodeData.attribute.stationstyle === "series_power_supply") {
      this.updateSeriesPowerSupplyHighlight(group, nodeData, radius, strokeColor, strokeWidth);
    } else {
      // 其他节点类型
      group.selectAll(".node-shape").attr("stroke", strokeColor).attr("stroke-width", strokeWidth);
    }
  }

  /**
   * 更新串供节点高亮
   */
  updateSeriesPowerSupplyHighlight(group, nodeData, radius, strokeColor, strokeWidth) {
    const separationGap = 3;
    const nodeType = nodeData.attribute.nodetype;

    if (nodeType === "left-half") {
      group
        .select(".node-half-left")
        .attr("d", `M ${-separationGap} ${-radius} A ${radius} ${radius} 0 0 0 ${-separationGap} ${radius} L ${-separationGap} 0 Z`)
        .attr("stroke", strokeColor)
        .attr("stroke-width", strokeWidth);
      group
        .select(".node-half-right")
        .attr("d", `M ${separationGap} ${-radius} A ${radius} ${radius} 0 0 1 ${separationGap} ${radius} L ${separationGap} 0 Z`)
        .attr("stroke", "none")
        .attr("stroke-width", 0);
    } else if (nodeType === "right-half") {
      group
        .select(".node-half-left")
        .attr("d", `M ${-separationGap} ${-radius} A ${radius} ${radius} 0 0 0 ${-separationGap} ${radius} L ${-separationGap} 0 Z`)
        .attr("stroke", "none")
        .attr("stroke-width", 0);
      group
        .select(".node-half-right")
        .attr("d", `M ${separationGap} ${-radius} A ${radius} ${radius} 0 0 1 ${separationGap} ${radius} L ${separationGap} 0 Z`)
        .attr("stroke", strokeColor)
        .attr("stroke-width", strokeWidth);
    } else {
      group.selectAll(".node-shape").attr("stroke", strokeColor).attr("stroke-width", strokeWidth);
    }
  }

  /**
   * 清除高亮样式
   */
  clearHighlightStyles() {
    if (!this.nodeGroups) return;

    const self = this;
    this.nodeGroups.each(function (d) {
      const group = d3.select(this);
      const radius = self.calculateNodeRadius(d);

      group.attr("fill-opacity", 1);
      self.restoreNodeDefaultStyle(group, d, radius);
    });
  }

  /**
   * 恢复节点默认样式
   */
  restoreNodeDefaultStyle(group, nodeData, radius) {
    // 优先使用闪烁颜色，如果没有则使用类别颜色
    const categoryColor =
      nodeData.attribute && nodeData.attribute.blink_color ? nodeData.attribute.blink_color : this.dataProcessor.getCategoryColor(nodeData.category !== undefined ? nodeData.category : "default");

    const greenBorderColor = this.themeManager.getNodeStrokeColor("green");

    const self = this;
    group.selectAll(".node-shape").each(function () {
      const shapeElement = d3.select(this);

      if (nodeData.state === 0) {
        shapeElement.attr("stroke", categoryColor).attr("stroke-width", 2);
      } else if (nodeData.attribute && nodeData.attribute.stationstyle === "series_power_supply") {
        self.restoreSeriesPowerSupplyStyle(shapeElement, nodeData, radius);
      } else if (nodeData.attribute && (nodeData.attribute.stationstyle === "loss_pressure_half" || nodeData.attribute.stationstyle === "disconnection")) {
        shapeElement.attr("stroke", greenBorderColor).attr("stroke-width", 1.5);
      } else {
        shapeElement.attr("stroke", null).attr("stroke-width", null);
      }
    });
  }

  /**
   * 恢复串供节点默认样式
   */
  restoreSeriesPowerSupplyStyle(shapeElement, nodeData, radius) {
    const separationGap = 3;
    // 优先使用闪烁颜色，如果没有则使用类别颜色
    const categoryColor =
      nodeData.attribute && nodeData.attribute.blink_color ? nodeData.attribute.blink_color : this.dataProcessor.getCategoryColor(nodeData.category !== undefined ? nodeData.category : "default");

    const nodeType = nodeData.attribute.nodetype;
    const defaultVisibleStroke = this.themeManager.getNodeStrokeColor("transparent");
    const defaultVisibleStrokeWidth = 3;

    if (nodeType === "left-half") {
      if (shapeElement.classed("node-half-left")) {
        shapeElement
          .attr("d", `M ${-separationGap} ${-radius} A ${radius} ${radius} 0 0 0 ${-separationGap} ${radius} L ${-separationGap} 0 Z`)
          .attr("fill", categoryColor)
          .attr("stroke", defaultVisibleStroke)
          .attr("stroke-width", defaultVisibleStrokeWidth)
          .style("display", "block");
      } else if (shapeElement.classed("node-half-right")) {
        shapeElement
          .attr("d", `M ${separationGap} ${-radius} A ${radius} ${radius} 0 0 1 ${separationGap} ${radius} L ${separationGap} 0 Z`)
          .attr("fill", "none")
          .attr("stroke", "none")
          .attr("stroke-width", 0)
          .style("display", "none");
      }
    } else if (nodeType === "right-half") {
      if (shapeElement.classed("node-half-left")) {
        shapeElement
          .attr("d", `M ${-separationGap} ${-radius} A ${radius} ${radius} 0 0 0 ${-separationGap} ${radius} L ${-separationGap} 0 Z`)
          .attr("fill", "none")
          .attr("stroke", "none")
          .attr("stroke-width", 0)
          .style("display", "none");
      } else if (shapeElement.classed("node-half-right")) {
        shapeElement
          .attr("d", `M ${separationGap} ${-radius} A ${radius} ${radius} 0 0 1 ${separationGap} ${radius} L ${separationGap} 0 Z`)
          .attr("fill", categoryColor)
          .attr("stroke", defaultVisibleStroke)
          .attr("stroke-width", defaultVisibleStrokeWidth)
          .style("display", "block");
      }
    }
  }

  /**
   * 添加交互背景
   * @param {d3.Selection} group 节点组
   * @param {Object} nodeData 节点数据
   * @param {number} radius 节点半径
   */
  addInteractionBackground(group, nodeData, radius) {
    const expandRadius = radius + 8; // 扩展的交互区域半径

    // 特殊处理 loss_pressure_half 节点
    if (nodeData.attribute && nodeData.attribute.stationstyle === "loss_pressure_half") {
      // 只为左半圆（分类色半圆）创建交互背景
      group
        .append("path")
        .attr("d", `M 0 ${-expandRadius} A ${expandRadius} ${expandRadius} 0 0 0 0 ${expandRadius} L 0 0 Z`)
        .attr("fill", "transparent")
        .attr("stroke", "none")
        .attr("class", "node-interaction-background")
        .style("pointer-events", "all");
    } else if (nodeData.attribute && nodeData.attribute.stationstyle === "series_power_supply") {
      // 特殊处理 series_power_supply 节点
      const separationGap = 3;
      const nodeType = nodeData.attribute.nodetype;

      if (nodeType === "left-half") {
        // 只为左半圆创建交互背景
        group
          .append("path")
          .attr("d", `M ${-separationGap} ${-expandRadius} A ${expandRadius} ${expandRadius} 0 0 0 ${-separationGap} ${expandRadius} L ${-separationGap} 0 Z`)
          .attr("fill", "transparent")
          .attr("stroke", "none")
          .attr("class", "node-interaction-background")
          .style("pointer-events", "all");
      } else if (nodeType === "right-half") {
        // 只为右半圆创建交互背景
        group
          .append("path")
          .attr("d", `M ${separationGap} ${-expandRadius} A ${expandRadius} ${expandRadius} 0 0 1 ${separationGap} ${expandRadius} L ${separationGap} 0 Z`)
          .attr("fill", "transparent")
          .attr("stroke", "none")
          .attr("class", "node-interaction-background")
          .style("pointer-events", "all");
      } else {
        // 默认情况：两个半圆都创建交互背景
        group
          .append("path")
          .attr("d", `M ${-separationGap} ${-expandRadius} A ${expandRadius} ${expandRadius} 0 0 0 ${-separationGap} ${expandRadius} L ${-separationGap} 0 Z`)
          .attr("fill", "transparent")
          .attr("stroke", "none")
          .attr("class", "node-interaction-background node-interaction-left")
          .style("pointer-events", "all");

        group
          .append("path")
          .attr("d", `M ${separationGap} ${-expandRadius} A ${expandRadius} ${expandRadius} 0 0 1 ${separationGap} ${expandRadius} L ${separationGap} 0 Z`)
          .attr("fill", "transparent")
          .attr("stroke", "none")
          .attr("class", "node-interaction-background node-interaction-right")
          .style("pointer-events", "all");
      }
    } else {
      // 其他节点类型使用圆形交互背景
      group.append("circle").attr("r", expandRadius).attr("fill", "transparent").attr("stroke", "none").attr("class", "node-interaction-background").style("pointer-events", "all");
    }
  }
}
