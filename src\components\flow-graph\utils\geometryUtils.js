/**
 * 几何计算工具类
 * 提供节点边缘坐标计算等功能
 */
export class GeometryUtils {
  /**
   * 获取连接线从节点边缘开始的坐标
   * @param {Object} nodeFrom 起始节点
   * @param {Object} nodeTo 目标节点
   * @returns {Object} 边缘坐标 {x, y}
   */
  getEdgeCoordinates(nodeFrom, nodeTo) {
    const radiusFrom = nodeFrom.symbolSize ? nodeFrom.symbolSize / 4 : 3;
    const fromX = nodeFrom.x || 0;
    const fromY = nodeFrom.y || 0;
    const toX = nodeTo.x || 0;
    const toY = nodeTo.y || 0;

    if (nodeFrom.attribute && nodeFrom.attribute.stationstyle === "series_power_supply" && nodeFrom.attribute.nodetype) {
      return this.getSeriesPowerSupplyEdgeCoordinates(nodeFrom, nodeTo, radiusFrom, fromX, fromY, toX, toY);
    }

    if (nodeFrom.attribute && nodeFrom.attribute.stationstyle === "loss_pressure_half") {
      return this.getLossPressureHalfEdgeCoordinates(nodeFrom, nodeTo, radiusFrom, fromX, fromY, toX, toY);
    }

    // 标准圆形节点的计算逻辑
    return this.getCircularNodeEdgeCoordinates(fromX, fromY, toX, toY, radiusFrom);
  }

  /**
   * 获取串供节点的边缘坐标
   */
  getSeriesPowerSupplyEdgeCoordinates(nodeFrom, nodeTo, radiusFrom, fromX, fromY, toX, toY) {
    const nodeType = nodeFrom.attribute.nodetype;
    const R = radiusFrom;
    const separationGap = 3; // 与节点绘制中保持一致

    // 计算从节点中心到目标节点的方向向量
    const dx = toX - fromX;
    const dy = toY - fromY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance === 0) {
      return { x: fromX, y: fromY };
    }

    // 计算角度（以节点中心为原点，右侧为0度，逆时针为正）
    const angle = Math.atan2(dy, dx);

    if (nodeType === "left-half") {
      // 左半圆：有效角度范围是 π/2 到 3π/2（左半圆弧）
      let finalAngle = angle;
      const safeOffset = 0.1; // 约5.7度的安全偏移，避免连接到竖边和圆弧的连接点

      // 如果角度指向右侧（透明部分），将其调整到左半圆弧的有效范围
      if (angle >= -Math.PI / 2 && angle <= Math.PI / 2) {
        // 角度在右半圆范围内，需要调整到左半圆边界，但避开边界点
        if (angle >= 0) {
          finalAngle = Math.PI / 2 + safeOffset; // 上边界，加上安全偏移
        } else {
          finalAngle = -Math.PI / 2 - safeOffset; // 下边界，减去安全偏移
        }
      } else {
        // 角度已经在左半圆范围内，但仍需避开边界点
        if (Math.abs(angle - Math.PI / 2) < safeOffset) {
          finalAngle = Math.PI / 2 + safeOffset;
        } else if (Math.abs(angle + Math.PI / 2) < safeOffset) {
          finalAngle = -Math.PI / 2 - safeOffset;
        }
      }

      // 计算连接点，考虑separationGap
      const baseX = fromX - separationGap;
      const edgeX = baseX + Math.cos(finalAngle) * R;
      const edgeY = fromY + Math.sin(finalAngle) * R;
      return { x: edgeX, y: edgeY };
    } else if (nodeType === "right-half") {
      // 右半圆：有效角度范围是 -π/2 到 π/2（右半圆弧）
      let finalAngle = angle;
      const safeOffset = 0.1; // 约5.7度的安全偏移

      // 如果角度指向左侧（透明部分），将其调整到右半圆弧的有效范围
      if (angle > Math.PI / 2 || angle < -Math.PI / 2) {
        // 角度在左半圆范围内，需要调整到右半圆边界，但避开边界点
        if (angle > Math.PI / 2) {
          finalAngle = Math.PI / 2 - safeOffset; // 上边界，减去安全偏移
        } else {
          finalAngle = -Math.PI / 2 + safeOffset; // 下边界，加上安全偏移
        }
      } else {
        // 角度已经在右半圆范围内，但仍需避开边界点
        if (Math.abs(angle - Math.PI / 2) < safeOffset) {
          finalAngle = Math.PI / 2 - safeOffset;
        } else if (Math.abs(angle + Math.PI / 2) < safeOffset) {
          finalAngle = -Math.PI / 2 + safeOffset;
        }
      }

      // 计算连接点，考虑separationGap
      const baseX = fromX + separationGap;
      const edgeX = baseX + Math.cos(finalAngle) * R;
      const edgeY = fromY + Math.sin(finalAngle) * R;
      return { x: edgeX, y: edgeY };
    } else {
      // 未定义的 nodetype，使用标准圆形逻辑
      return this.getCircularNodeEdgeCoordinates(fromX, fromY, toX, toY, R);
    }
  }

  /**
   * 获取圆形节点的边缘坐标
   */
  getCircularNodeEdgeCoordinates(fromX, fromY, toX, toY, radius) {
    const dx = toX - fromX;
    const dy = toY - fromY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance === 0) {
      return { x: fromX, y: fromY };
    }

    const offsetX = (dx / distance) * radius;
    const offsetY = (dy / distance) * radius;
    const edgeX = fromX + offsetX;
    const edgeY = fromY + offsetY;

    return { x: edgeX, y: edgeY };
  }

  /**
   * 计算两点之间的距离
   * @param {number} x1 第一个点的x坐标
   * @param {number} y1 第一个点的y坐标
   * @param {number} x2 第二个点的x坐标
   * @param {number} y2 第二个点的y坐标
   * @returns {number} 距离
   */
  calculateDistance(x1, y1, x2, y2) {
    const dx = x2 - x1;
    const dy = y2 - y1;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 计算角度
   * @param {number} x1 起点x坐标
   * @param {number} y1 起点y坐标
   * @param {number} x2 终点x坐标
   * @param {number} y2 终点y坐标
   * @returns {number} 角度（弧度）
   */
  calculateAngle(x1, y1, x2, y2) {
    return Math.atan2(y2 - y1, x2 - x1);
  }

  /**
   * 计算角度（度数）
   * @param {number} x1 起点x坐标
   * @param {number} y1 起点y坐标
   * @param {number} x2 终点x坐标
   * @param {number} y2 终点y坐标
   * @returns {number} 角度（度数）
   */
  calculateAngleDegrees(x1, y1, x2, y2) {
    const radians = this.calculateAngle(x1, y1, x2, y2);
    return radians * (180 / Math.PI);
  }

  /**
   * 计算贝塞尔曲线上的点
   * @param {number} t 参数值（0-1）
   * @param {Object} start 起点 {x, y}
   * @param {Object} control 控制点 {x, y}
   * @param {Object} end 终点 {x, y}
   * @returns {Object} 曲线上的点 {x, y}
   */
  calculateBezierPoint(t, start, control, end) {
    const x = Math.pow(1 - t, 2) * start.x + 2 * (1 - t) * t * control.x + Math.pow(t, 2) * end.x;
    const y = Math.pow(1 - t, 2) * start.y + 2 * (1 - t) * t * control.y + Math.pow(t, 2) * end.y;
    return { x, y };
  }

  /**
   * 计算贝塞尔曲线的切线角度
   * @param {number} t 参数值（0-1）
   * @param {Object} start 起点 {x, y}
   * @param {Object} control 控制点 {x, y}
   * @param {Object} end 终点 {x, y}
   * @returns {number} 切线角度（度数）
   */
  calculateBezierTangentAngle(t, start, control, end) {
    // 计算切线方向: -2(1-t)*P0 + 2(1-2t)*P1 + 2t*P2
    const tangentX = -2 * (1 - t) * start.x + 2 * (1 - 2 * t) * control.x + 2 * t * end.x;
    const tangentY = -2 * (1 - t) * start.y + 2 * (1 - 2 * t) * control.y + 2 * t * end.y;

    // 计算角度
    const angle = Math.atan2(tangentY, tangentX);
    let degrees = (angle * 180) / Math.PI;

    // 验证角度计算
    if (!Number.isFinite(degrees)) {
      degrees = 0;
    }

    // 确保文字贴合连接线但避免反转
    if (degrees > 90 || degrees < -90) {
      degrees += 180;
    }

    return degrees;
  }

  /**
   * 判断点是否在矩形内
   * @param {number} x 点的x坐标
   * @param {number} y 点的y坐标
   * @param {Object} rect 矩形 {x, y, width, height}
   * @returns {boolean} 是否在矩形内
   */
  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width && y >= rect.y && y <= rect.y + rect.height;
  }

  /**
   * 判断点是否在圆内
   * @param {number} x 点的x坐标
   * @param {number} y 点的y坐标
   * @param {Object} circle 圆 {x, y, radius}
   * @returns {boolean} 是否在圆内
   */
  isPointInCircle(x, y, circle) {
    const distance = this.calculateDistance(x, y, circle.x, circle.y);
    return distance <= circle.radius;
  }

  /**
   * 计算点到直线的距离
   * @param {number} px 点的x坐标
   * @param {number} py 点的y坐标
   * @param {Object} line 直线 {x1, y1, x2, y2}
   * @returns {number} 距离
   */
  distancePointToLine(px, py, line) {
    const { x1, y1, x2, y2 } = line;
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) {
      // 线段退化为点
      return this.calculateDistance(px, py, x1, y1);
    }

    const param = dot / lenSq;

    let xx, yy;
    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }

    return this.calculateDistance(px, py, xx, yy);
  }

  /**
   * 限制值在指定范围内
   * @param {number} value 值
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @returns {number} 限制后的值
   */
  clamp(value, min, max) {
    return Math.min(Math.max(value, min), max);
  }

  /**
   * 线性插值
   * @param {number} a 起始值
   * @param {number} b 结束值
   * @param {number} t 插值参数（0-1）
   * @returns {number} 插值结果
   */
  lerp(a, b, t) {
    return a + (b - a) * t;
  }

  /**
   * 将角度标准化到 [-π, π] 范围
   * @param {number} angle 角度（弧度）
   * @returns {number} 标准化后的角度
   */
  normalizeAngle(angle) {
    while (angle > Math.PI) angle -= 2 * Math.PI;
    while (angle < -Math.PI) angle += 2 * Math.PI;
    return angle;
  }

  /**
   * 获取非全站失压节点(loss_pressure_half)的边缘坐标
   * 确保连接线只连接到分类色的半圆（左半圆）上
   */
  getLossPressureHalfEdgeCoordinates(nodeFrom, nodeTo, radiusFrom, fromX, fromY, toX, toY) {
    const R = radiusFrom;

    // 计算从节点中心到目标节点的方向向量
    const dx = toX - fromX;
    const dy = toY - fromY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance === 0) {
      return { x: fromX, y: fromY };
    }

    // 计算角度（以节点中心为原点，右侧为0度，逆时针为正）
    const angle = Math.atan2(dy, dx);

    // loss_pressure_half节点：左半圆为分类色，右半圆为灰色
    // 连接线必须连接到左半圆（有效角度范围是 π/2 到 3π/2）
    let finalAngle = angle;
    const safeOffset = 0.1; // 约5.7度的安全偏移，避免连接到竖边和圆弧的连接点

    // 如果角度指向右侧（灰色部分），将其调整到左半圆弧的有效范围
    if (angle >= -Math.PI / 2 && angle <= Math.PI / 2) {
      // 角度在右半圆范围内，需要调整到左半圆边界，但避开边界点
      if (angle >= 0) {
        finalAngle = Math.PI / 2 + safeOffset; // 上边界，加上安全偏移
      } else {
        finalAngle = -Math.PI / 2 - safeOffset; // 下边界，减去安全偏移
      }
    } else {
      // 角度已经在左半圆范围内，但仍需避开边界点
      if (Math.abs(angle - Math.PI / 2) < safeOffset) {
        finalAngle = Math.PI / 2 + safeOffset;
      } else if (Math.abs(angle + Math.PI / 2) < safeOffset) {
        finalAngle = -Math.PI / 2 - safeOffset;
      }
    }

    // 计算连接点坐标
    const edgeX = fromX + Math.cos(finalAngle) * R;
    const edgeY = fromY + Math.sin(finalAngle) * R;
    return { x: edgeX, y: edgeY };
  }
}
