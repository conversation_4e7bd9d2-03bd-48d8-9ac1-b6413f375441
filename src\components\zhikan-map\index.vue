<!-- eslint-disable no-undef -->
<template>
  <div
    id="map"
    class="zhikan-map"
  >
    <div
      id="box"
      style="position: absolute"
    ></div>
    <div
      id="mapDialog"
      class="mapDialog"
      style="max-width: 800px; position: absolute; background: #fff; z-index: 1; padding: 12px"
      v-if="showMapDialog"
      v-drag
    >
      <span @click="showMapDialog = false">X</span>
      <div
        v-html="mapDialogHtml"
        @click="handleMapDialogClick"
      ></div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
let stationPoints = []; // 变电站点
let stationTexts = []; // 变电站光伏文字
let pbPoints = []; // 配变设备点
let pbTexts = []; // 配变设备光伏文字
let textPoints = [];

let red = "#ff3333";
let yellow = "#ffff00";
let green = "#29df7e";
let blue = "#0073e6";

export default {
  name: "ZhikanMap",
  props: {
    AreaAndDeptData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    stations: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      map: null,
      equipId: "",
      areaInfo: [
        {
          name: "佛山供电局",
          orgCode: "0306",
          num: 4,
          children: [
            {
              name: "禅城供电局",
              parent: "佛山供电局",
              orgCode: "030606",
              num: 6,
              level: "country"
            },
            {
              name: "南海供电局",
              parent: "佛山供电局",
              orgCode: "030607",
              num: 6,
              level: "country"
            },
            {
              name: "顺德供电局",
              parent: "佛山供电局",
              orgCode: "030608",
              num: 6,
              level: "country"
            },
            {
              name: "三水供电局",
              parent: "佛山供电局",
              orgCode: "030609",
              num: 6,
              level: "country"
            },
            {
              name: "高明供电局",
              parent: "佛山供电局",
              orgCode: "030610",
              num: 6,
              level: "country"
            },
            // 禅城供电局
            {
              name: "城区供电所",
              parent: "禅城供电局",
              orgCode: "03060603",
              num: 8,
              level: "town"
            },
            {
              name: "南庄供电所",
              parent: "禅城供电局",
              orgCode: "03060602",
              num: 8,
              level: "town"
            },
            // 南海供电局
            {
              name: "桂城供电所",
              parent: "南海供电局",
              orgCode: "03060701",
              num: 8,
              level: "town"
            },
            {
              name: "大沥供电所",
              parent: "南海供电局",
              orgCode: "03060702",
              num: 8,
              level: "town"
            },
            {
              name: "西樵供电所",
              parent: "南海供电局",
              orgCode: "03060703",
              num: 8,
              level: "town"
            },
            {
              name: "狮山供电所",
              parent: "南海供电局",
              orgCode: "03060704",
              num: 8,
              level: "town"
            },
            {
              name: "九江供电所",
              parent: "南海供电局",
              orgCode: "03060705",
              num: 8,
              level: "town"
            },
            {
              name: "里水供电所",
              parent: "南海供电局",
              orgCode: "03060706",
              num: 8,
              level: "town"
            },
            {
              name: "罗村供电所",
              parent: "南海供电局",
              orgCode: "03060708",
              num: 8,
              level: "town"
            },
            {
              name: "丹灶供电所",
              parent: "南海供电局",
              orgCode: "03060707",
              num: 8,
              level: "town"
            },
            // 顺德供电局
            {
              name: "大良供电所",
              parent: "顺德供电局",
              orgCode: "03060801",
              num: 8,
              level: "town"
            },
            {
              name: "容桂供电所",
              parent: "顺德供电局",
              orgCode: "03060802",
              num: 8,
              level: "town"
            },
            {
              name: "伦教供电所",
              parent: "顺德供电局",
              orgCode: "03060803",
              num: 8,
              level: "town"
            },
            {
              name: "北滘供电所",
              parent: "顺德供电局",
              orgCode: "03060804",
              num: 8,
              level: "town"
            },
            {
              name: "陈村供电所",
              parent: "顺德供电局",
              orgCode: "03060805",
              num: 8,
              level: "town"
            },
            {
              name: "乐从供电所",
              parent: "顺德供电局",
              orgCode: "03060806",
              num: 8,
              level: "town"
            },
            {
              name: "龙江供电所",
              parent: "顺德供电局",
              orgCode: "03060807",
              num: 8,
              level: "town"
            },
            {
              name: "勒流供电所",
              parent: "顺德供电局",
              orgCode: "03060808",
              num: 8,
              level: "town"
            },
            {
              name: "杏坛供电所",
              parent: "顺德供电局",
              orgCode: "03060809",
              num: 8,
              level: "town"
            },
            {
              name: "均安供电所",
              parent: "顺德供电局",
              orgCode: "03060810",
              num: 8,
              level: "town"
            },
            // 三水供电局
            {
              name: "西南供电所",
              parent: "三水供电局",
              orgCode: "03060903",
              num: 8,
              level: "town"
            },
            {
              name: "乐平供电所",
              parent: "三水供电局",
              orgCode: "03060911",
              num: 8,
              level: "town"
            },
            {
              name: "白坭供电所",
              parent: "三水供电局",
              orgCode: "03060914",
              num: 8,
              level: "town"
            },
            {
              name: "芦苞供电所",
              parent: "三水供电局",
              orgCode: "03060915",
              num: 8,
              level: "town"
            },
            {
              name: "南山供电所",
              parent: "三水供电局",
              orgCode: "03060916",
              num: 8,
              level: "town"
            },
            {
              name: "大塘供电所",
              parent: "三水供电局",
              orgCode: "03060925",
              num: 8,
              level: "town"
            },
            // 高明供电局
            {
              name: "荷城供电所",
              parent: "高明供电局",
              orgCode: "03061005",
              num: 8,
              level: "town"
            },
            {
              name: "杨和供电所",
              parent: "高明供电局",
              orgCode: "03061006",
              num: 8,
              level: "town"
            },
            {
              name: "明城供电所",
              parent: "高明供电局",
              orgCode: "03061007",
              num: 8,
              level: "town"
            },
            {
              name: "更合供电所",
              parent: "高明供电局",
              orgCode: "03061008",
              num: 8,
              level: "town"
            }
          ]
        },
        {
          name: "禅城供电局",
          orgCode: "030606",
          num: 6,
          children: [
            { name: "城区供电所", orgCode: "03060603", num: 8 },
            { name: "南庄供电所", orgCode: "03060602", num: 8 }
          ]
        },
        {
          name: "南海供电局",
          orgCode: "030607",
          num: 6,
          children: [
            { name: "桂城供电所", orgCode: "03060701", num: 8 },
            { name: "大沥供电所", orgCode: "03060702", num: 8 },
            { name: "西樵供电所", orgCode: "03060703", num: 8 },
            { name: "狮山供电所", orgCode: "03060704", num: 8 },
            { name: "九江供电所", orgCode: "03060705", num: 8 },
            { name: "里水供电所", orgCode: "03060706", num: 8 },
            { name: "罗村供电所", orgCode: "03060708", num: 8 },
            { name: "丹灶供电所", orgCode: "03060707", num: 8 }
          ]
        },
        {
          name: "顺德供电局",
          orgCode: "030608",
          num: 6,
          children: [
            { name: "大良供电所", orgCode: "03060801", num: 8 },
            { name: "容桂供电所", orgCode: "03060802", num: 8 },
            { name: "伦教供电所", orgCode: "03060803", num: 8 },
            { name: "北滘供电所", orgCode: "03060804", num: 8 },
            { name: "陈村供电所", orgCode: "03060805", num: 8 },
            { name: "乐从供电所", orgCode: "03060806", num: 8 },
            { name: "龙江供电所", orgCode: "03060807", num: 8 },
            { name: "勒流供电所", orgCode: "03060808", num: 8 },
            { name: "杏坛供电所", orgCode: "03060809", num: 8 },
            { name: "均安供电所", orgCode: "03060810", num: 8 }
          ]
        },
        {
          name: "三水供电局",
          orgCode: "030609",
          num: 6,
          children: [
            { name: "西南供电所", orgCode: "03060903", num: 8 },
            { name: "乐平供电所", orgCode: "03060911", num: 8 },
            { name: "白坭供电所", orgCode: "03060914", num: 8 },
            { name: "芦苞供电所", orgCode: "03060915", num: 8 },
            { name: "南山供电所", orgCode: "03060916", num: 8 },
            { name: "大塘供电所", orgCode: "03060925", num: 8 }
          ]
        },
        {
          name: "高明供电局",
          orgCode: "030610",
          num: 6,
          children: [
            { name: "荷城供电所", orgCode: "03061005", num: 8 },
            { name: "杨和供电所", orgCode: "03061006", num: 8 },
            { name: "明城供电所", orgCode: "03061007", num: 8 },
            { name: "更合供电所", orgCode: "03061008", num: 8 }
          ]
        }
      ],
      areaDeptInfo: [
        { name: "佛山供电局", orgCode: "0306", num: 4, level: "province" },
        {
          name: "禅城供电局",
          parent: "佛山供电局",
          orgCode: "030606",
          num: 6,
          level: "country"
        },
        {
          name: "南海供电局",
          parent: "佛山供电局",
          orgCode: "030607",
          num: 6,
          level: "country"
        },
        {
          name: "顺德供电局",
          parent: "佛山供电局",
          orgCode: "030608",
          num: 6,
          level: "country"
        },
        {
          name: "三水供电局",
          parent: "佛山供电局",
          orgCode: "030609",
          num: 6,
          level: "country"
        },
        {
          name: "高明供电局",
          parent: "佛山供电局",
          orgCode: "030610",
          num: 6,
          level: "country"
        },
        // 禅城供电局
        {
          name: "城区供电所",
          parent: "禅城供电局",
          orgCode: "03060603",
          num: 8,
          level: "town"
        },
        {
          name: "南庄供电所",
          parent: "禅城供电局",
          orgCode: "03060602",
          num: 8,
          level: "town"
        },
        // 南海供电局
        {
          name: "桂城供电所",
          parent: "南海供电局",
          orgCode: "03060701",
          num: 8,
          level: "town"
        },
        {
          name: "大沥供电所",
          parent: "南海供电局",
          orgCode: "03060702",
          num: 8,
          level: "town"
        },
        {
          name: "西樵供电所",
          parent: "南海供电局",
          orgCode: "03060703",
          num: 8,
          level: "town"
        },
        {
          name: "狮山供电所",
          parent: "南海供电局",
          orgCode: "03060704",
          num: 8,
          level: "town"
        },
        {
          name: "九江供电所",
          parent: "南海供电局",
          orgCode: "03060705",
          num: 8,
          level: "town"
        },
        {
          name: "里水供电所",
          parent: "南海供电局",
          orgCode: "03060706",
          num: 8,
          level: "town"
        },
        {
          name: "罗村供电所",
          parent: "南海供电局",
          orgCode: "03060708",
          num: 8,
          level: "town"
        },
        {
          name: "丹灶供电所",
          parent: "南海供电局",
          orgCode: "03060707",
          num: 8,
          level: "town"
        },
        // 顺德供电局
        {
          name: "大良供电所",
          parent: "顺德供电局",
          orgCode: "03060801",
          num: 8,
          level: "town"
        },
        {
          name: "容桂供电所",
          parent: "顺德供电局",
          orgCode: "03060802",
          num: 8,
          level: "town"
        },
        {
          name: "伦教供电所",
          parent: "顺德供电局",
          orgCode: "03060803",
          num: 8,
          level: "town"
        },
        {
          name: "北滘供电所",
          parent: "顺德供电局",
          orgCode: "03060804",
          num: 8,
          level: "town"
        },
        {
          name: "陈村供电所",
          parent: "顺德供电局",
          orgCode: "03060805",
          num: 8,
          level: "town"
        },
        {
          name: "乐从供电所",
          parent: "顺德供电局",
          orgCode: "03060806",
          num: 8,
          level: "town"
        },
        {
          name: "龙江供电所",
          parent: "顺德供电局",
          orgCode: "03060807",
          num: 8,
          level: "town"
        },
        {
          name: "勒流供电所",
          parent: "顺德供电局",
          orgCode: "03060808",
          num: 8,
          level: "town"
        },
        {
          name: "杏坛供电所",
          parent: "顺德供电局",
          orgCode: "03060809",
          num: 8,
          level: "town"
        },
        {
          name: "均安供电所",
          parent: "顺德供电局",
          orgCode: "03060810",
          num: 8,
          level: "town"
        },
        // 三水供电局
        {
          name: "西南供电所",
          parent: "三水供电局",
          orgCode: "03060903",
          num: 8,
          level: "town"
        },
        {
          name: "乐平供电所",
          parent: "三水供电局",
          orgCode: "03060911",
          num: 8,
          level: "town"
        },
        {
          name: "白坭供电所",
          parent: "三水供电局",
          orgCode: "03060914",
          num: 8,
          level: "town"
        },
        {
          name: "芦苞供电所",
          parent: "三水供电局",
          orgCode: "03060915",
          num: 8,
          level: "town"
        },
        {
          name: "南山供电所",
          parent: "三水供电局",
          orgCode: "03060916",
          num: 8,
          level: "town"
        },
        {
          name: "大塘供电所",
          parent: "三水供电局",
          orgCode: "03060925",
          num: 8,
          level: "town"
        },
        // 高明供电局
        {
          name: "荷城供电所",
          parent: "高明供电局",
          orgCode: "03061005",
          num: 8,
          level: "town"
        },
        {
          name: "杨和供电所",
          parent: "高明供电局",
          orgCode: "03061006",
          num: 8,
          level: "town"
        },
        {
          name: "明城供电所",
          parent: "高明供电局",
          orgCode: "03061007",
          num: 8,
          level: "town"
        },
        {
          name: "更合供电所",
          parent: "高明供电局",
          orgCode: "03061008",
          num: 8,
          level: "town"
        }
      ],
      areaInfoMap: null, //
      areaDeptDataMap: new Map(), // 请求过来的数据map
      areaDeptInfoMap: new Map(), // 区局供电所信息info map  --> areaDeptInfo key:level
      textMap: new Map(),
      power: null,
      lastLevel: "",
      lastZoom: 9,
      lastAreaInfo: {}, // 当前范围信息
      areaOfmouse: "", // 鼠标所在区域
      dblclick: false,
      dateValue: new Date(),
      showMapDialog: false,
      mapDialogHtml: ""
    };
  },
  // 拖拽
  directives: {
    // 注册指令
    drag: function (el, binding) {
      const dragBox = el; // 获取当前元素
      dragBox.onmousedown = (e) => {
        // 当选中对象为输入框或者文本框不执行拖拽操作
        if (e.target.nodeName === "INPUT" || e.target.nodeName === "TEXTAREA") {
          return;
        }
        e.preventDefault();
        // 算出鼠标相对元素的位置
        const disX = e.clientX - dragBox.offsetLeft;
        const disY = e.clientY - dragBox.offsetTop;
        document.onmousemove = (e) => {
          // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
          e.preventDefault();
          const left = e.clientX - disX;
          const top = e.clientY - disY;
          // 移动当前元素
          dragBox.style.left = left + "px";
          dragBox.style.top = top + "px";
          // binding.value.set(left, top);
        };
        document.onmouseup = (e) => {
          e.preventDefault();
          // 鼠标弹起来的时候不再移动
          document.onmousemove = null;
          // 预防鼠标弹起来后还会循环（即预防鼠标放上去的时候还会移动）
          document.onmouseup = null;
        };
      };
    }
  },
  mounted() {
    this.initToken();
  },
  destroyed() {
    stationPoints = [];
    stationTexts = [];
    textPoints = [];
    pbPoints = [];
    pbTexts = [];
  },
  methods: {
    initToken() {
      let self = this;
      let params = {
        appId: "a242f810c3d64ed0a318b982daa6907d",
        nonce: "",
        secretKey: "sKyEZNkrYvt79vI938EC0opnlpm7FqrDKWFa0f88GEY="
      };
      axios.post("/solor/dispatch/nari/zk/nwzk/gateway/gw-security/permission/getSign", params).then((res) => {
        console.log(`sign: ${res.data.sign}`);
        axios({
          method: "post",
          url: "/solor/dispatch/nari/zk/nwzk/gateway/gw-security/permission/token",
          data: {},
          headers: {
            Authorization: "Bearer " + res.data.sign
            // Acceptcode : localStorage.getItem('acceptcode')
          }
        }).then(function (response) {
          console.log(response);
          if (response.data && response.data.data && response.data.data.accessToken) {
            console.log(`token: ${response.data.data.accessToken}`);
            self.initMap(response.data.data.accessToken);
          }
        });
      });
    },
    initMap(accessToken) {
      let _this = this;
      WebGIS.accessToken = accessToken;
      _this.map = new WebGIS.Map({
        container: "map",
        zoom: 9,
        minZoom: 9,
        maxZoom: 20,
        center: [112.940577, 23.032441],
        style: "webgis://styles/normal", // 地图样式
        psrVisible: false,
        psr: {
          orgCode: "0306",
          visible: true
        },
        controls: ["coordctrl", "scale", { name: "auxiliary", position: "bottom-right" }]
      });

      // 地图加载完执行 实例化行政区域对象
      this.map.on("load", () => {
        let power = new WebGIS.GeographyTheme.PowerSupplyStation(_this.map);
        this.power = power;
        this.initPower();

        // 加载完成后事件
        power.onLoaded(async () => {
          // 供电局，供电所直接显示
          this.showArea("country", 9, 10, { color: "#333" });
        });

        // 监听鼠标双击事件
        power.dblclick((name, data, e) => {
          this.dblclick = true;
          let gisName = data.properties.gis_name;
          this.lastAreaInfo = { name: gisName }; // 保存当前范围信息
          this.dblDown(gisName, data.properties.org_code);
        });
        power.mouseenter((name, data, e) => {
          this.areaOfmouse = data.properties.gis_name;
        });

        // 监听鼠标滚动
        power.registerScrollEvent(async (res) => {
          let zoom = this.map.getZoom();
          let level = this.power.getZoomLevel().toLocaleLowerCase();
          // console.log('this.textMap ',this.textMap );

          // 显示隐藏柱状图
          if (zoom >= 14) {
            this.$emit("hideBarChart", false);
          } else {
            this.$emit("hideBarChart", true);
          }
          //   let filter = new WebGIS.Display.Filter(this.map);
          new WebGIS.Display.Filter(this.map);
          if (res) {
            this.lastAreaInfo = { name: res.name }; // 保存当前范围信息
          }

          console.log("当前zoom:", zoom, level, this.lastAreaInfo.name);
          this.lastLevel = level;
          if (level === "province" || zoom <= 10) {
            this.setProvinArea();
          } else if (zoom <= 12) {
            this.showDept(level, 10, 12, { color: "#333" }, this.lastAreaInfo.name);
            this.findParentCode(level);
          } else {
            this.findParentCode(level);
            this.showStation(level, this.lastAreaInfo.name, 12, 22);
          }
        });
      });
    },
    setProvinArea() {
      this.power.specifiedArea("0306");
    },
    findParentCode(level) {
      let lastObj = this.areaDeptInfo.find((item) => item.name === this.lastAreaInfo.name);
      if (lastObj) {
        let findObj = this.areaDeptInfo.find((item) => item.name === lastObj.parent);
        if (level === "town" && lastObj.orgCode.length === 6) {
          this.power.specifiedArea(lastObj.orgCode);
        } else {
          this.power.specifiedArea(findObj.orgCode);
        }
      }
    },
    // 初始化供电所
    initPower() {
      // "031209"
      this.power.initialize({
        initArea: "0306", // 初始化显示供电所,
        hideArea: [], // 供电所初始化隐藏区域
        textcolor: "#333", // 供电所字体颜色
        fillcolor: "#fff", // 供电所颜色
        fillcoloroutline: "#05dcfa", // 供电所轮廓颜色
        mouseincolor: "#fff", // 供电所进入高亮颜色
        mouseinfillcoloroutline: "#fff", // 供电所进入时高亮轮廓颜色
        selectedcolor: "#fff", // 供电所选中时颜色
        selectedfillcoloroutline: "#fff", // 供电所选中时轮廓颜色
        fillOpacity: 0.7,
        patrolTextAllowOverlap: false,
        townTextAllowOverlap: false,
        countryTextAllowOverlap: false,
        cityTextAllowOverlap: false,
        provinceTextAllowOverlap: false,
        zoomlevel: [8, 9, 9, 10, 12],
        simpleName: true
      });
    },
    // 显示供电局
    showArea(level, minZoom, maxZoom, style) {
      if (!this.power) return;
      let data = this.AreaAndDeptData;
      // 存数据Map
      this.setAreaDeptDataMap(data);
      this.setAreaDeptMap();

      // 设置地图光伏颜色样式
      this.setGfStyleByCode(this.power, data, this.areaInfoMap, this.areaDeptDataMap);

      this.lastLevel = this.power.getZoomLevel();
      this.areaInfo.forEach((item) => {
        this.showGfDataByCode(this.power, level, item, [minZoom, maxZoom], style, "noSave");
      });
    },
    // 显示供电所
    showDept(level, minZoom, maxZoom, style, name) {
      this.clearText();
      if (name) {
        let tmpName = name;
        if (name.indexOf("供电所") !== -1) {
          this.areaDeptInfo.forEach((item) => {
            if (item.name === name) {
              tmpName = item.parent;
            }
          });
        }

        let data = [];
        this.areaInfo.forEach((item) => {
          if (item.name === tmpName) {
            data = item.children;
          }
        });
        if (!data) return;
        this.lastLevel = level;
        data.forEach((item) => {
          this.showGfDataByCode(this.power, level, item, [minZoom, maxZoom], style);
        });
      } else {
        // 获取对应等级的供电所
        let data = this.areaDeptInfoMap.get(level);
        if (!data) return;
        this.lastLevel = level;
        data.forEach((item) => {
          this.showGfDataByCode(this.power, level, item, [minZoom, maxZoom], style);
        });
      }
    },
    // 显示站点
    showStation(level, areaName, minZoom, maxZoom, style) {
      let _this = this;
      let powerStyle = [
        { codes: [], color: red, codeType: "CODE" },
        { codes: [], color: yellow, codeType: "CODE" },
        {
          codes: [],
          color: "#09ae4d",
          strokeWidth: 2,
          strokeColor: "#7dfbb2",
          codeType: "CODE"
        },
        { codes: [], color: blue, codeType: "CODE" }
      ];
      let tmpArr = [];
      if (areaName) {
        this.stations.forEach((station) => {
          let _areaName = station.areaName;
          if (_areaName === this.lastAreaInfo.name) {
            station.pluGfRl = station.plu;
            station.conGfRl = station.con;
            station.name = station.stationDesc;
            tmpArr.push(station);
            this.$emit("onLevel", level, tmpArr);
            let x = station.zuoBiaoX * 1;
            let y = station.zuoBiaoY * 1;
            let pluGfRl = station.plu; // 可接入
            let conGfRl = station.con; // 已接入
            let pbRl = pluGfRl + conGfRl; // 总容量
            let precent = conGfRl / pbRl; // 百分
            let style = { color: "#333", codeType: "CODE" };
            if (precent >= 0.8) {
              style = powerStyle[0];
            } else if (precent >= 0.6 && precent < 0.8) {
              style = powerStyle[1];
            } else if (precent < 0.6 && precent > 0) {
              style = powerStyle[2];
            } else {
              style = powerStyle[3];
            }

            let str = `${station.name}\n 可接入:${station.plu ? station.plu : 0}MVA \n 已接入:${station.con ? station.con : 0}MVA`;
            if (!station.plu && !station.con) {
              str = `${station.name}`;
            }
            let info = {
              id: "_point",
              coord: [x, y],
              text: str
            };

            // 渲染point
            let point = this.setPointToMap(info, { ...style, radius: 8 }, [minZoom, maxZoom]);
            point.on("click", () => {
              // 隐藏线路
              let filter = new WebGIS.Display.Filter(this.map);
              filter.setPSRVisibility(false);
              _this.$emit("station-click", {}, station, _this.map);
            });
            stationPoints.push(point);

            // 渲染text
            info.id = "_text";
            let text = this.setTextToMap(info, { ...style, offset: [0, 1] }, [minZoom, maxZoom]);
            stationTexts.push(text);
          }
        });
        return;
      }
      this.stations.forEach((station) => {
        station.pluGfRl = station.plu;
        station.conGfRl = station.con;
        station.name = station.stationDesc;
        tmpArr.push(station);
        this.$emit("onLevel", level, tmpArr);
        let x = station.zuoBiaoX;
        let y = station.zuoBiaoY;
        let pluGfRl = station.plu; // 可接入
        let conGfRl = station.con; // 已接入
        let pbRl = pluGfRl + conGfRl; // 总容量
        let precent = conGfRl / pbRl; // 百分比

        let style = { color: "#59e4ff", codeType: "CODE" };
        if (precent >= 0.8) {
          style = powerStyle[0];
        } else if (precent >= 0.6 && precent < 0.8) {
          style = powerStyle[1];
        } else if (precent < 0.6 && precent > 0) {
          style = powerStyle[2];
        } else {
          style = powerStyle[3];
        }
        let str = `${station.name}\n 可接入:${station.plu ? station.plu : 0}MVA \n 已接入:${station.con ? station.con : 0}MVA`;
        if (!station.plu && !station.con) {
          str = `${station.name}`;
        }
        let info = {
          id: "_point",
          coord: [x, y],
          text: str
        };

        // 渲染point
        let point = this.setPointToMap(info, { ...style, radius: 8 }, [minZoom, maxZoom]);
        point.on("click", () => {
          // 隐藏线路
          let filter = new WebGIS.Display.Filter(this.map);
          filter.setPSRVisibility(false);
          _this.$emit("station-click", {}, station, _this.map);
        });
        stationPoints.push(point);

        // 渲染text
        info.id = "_text";
        let text = this.setTextToMap(info, { ...style, offset: [0, 1] }, [minZoom, maxZoom]);
        stationTexts.push(text);
      });
    },
    // 找到线路并地图平移过去
    showLine(cId, data) {
      let _this = this;
      // 电网显示
      let filter = new WebGIS.Display.Filter(_this.map);
      filter.setPSRVisibility(true);
      this.$emit("hideBarChart", false);
      // 创建一个图层管理实例
      // var layerManager = new WebGIS.Display.LayerManager(this.map);
      // 根据源图层ID隐藏图层
      // layerManager.showAll("PSR");
      filter = new WebGIS.Display.Filter(_this.map);
      filter.filterByDevices(
        [
          {
            ids: [cId]
          }
        ],
        true
      );

      let query = new WebGIS.Query.QueryDevices();
      let psrurlsDom = cId;
      let parmas = {
        devId: psrurlsDom, // 馈线设备ID（例子）
        type: 3 // 查询类型
      };
      query.QueryDeviceByFeeder(parmas, (devicesResult) => {
        console.log("devicesResult", devicesResult);
        let psrDataList = devicesResult.serviceResultData.psr_data_list;
        let projection = WebGIS.Tools.Projection;

        // setTimeout(() => {
        let siteType = devicesResult.siteType;
        let coodMap = new Map();
        psrDataList.forEach((item) => {
          let psrtype = item.psrtype.toString();
          if (siteType.indexOf(psrtype) !== -1) {
            let psr_list = item.psr_list;
            for (let i = 0; i < psr_list.length; i++) {
              let dev_id = psr_list[i].dev_id;
              let wgs84 = projection.MercatorToWgs84(psr_list[i].coordinate.split(" "));
              psr_list[i].center = wgs84;
              psr_list[i].id = psr_list[i].dev_id;
              if (i === 0) {
                const flyObj = {
                  zoom: 17,
                  speed: 2,
                  curve: 1
                };
                if (Array.isArray(wgs84) && wgs84.length === 2) {
                  flyObj.center = wgs84;
                }
                // 地图平移至设备处
                devicesResult.flyTo(_this.map, flyObj);
                devicesResult.render(_this.map); // 显示设备
              }
              if (data) {
                for (let j = 0; j < data.length; j++) {
                  let id = data[j].substationId;
                  if (dev_id === id) {
                    // 创建点，创建text; 只渲染一个点
                    if (!coodMap.has(psr_list[i].coordinate)) {
                      let obj = Object.assign({}, psr_list[i]);
                      obj.data = data[j];
                      let arr = [obj];
                      coodMap.set(obj.coordinate, arr);
                    } else {
                      let arr = coodMap.get(psr_list[i].coordinate);
                      let obj = Object.assign({}, psr_list[i]);
                      obj.data = data[j];
                      arr.push(obj);
                      coodMap.set(obj.coordinate, arr);

                      // 偏移重复点
                      psr_list[i].center[0] += 0.00004;
                    }

                    let info = {
                      id: id,
                      coord: [psr_list[i].center[0], psr_list[i].center[1]]
                    };
                    console.log(info.coord);
                    let color = "";
                    if (data[j].percentage >= 0.8) {
                      color = red;
                    } else if (data[j].percentage >= 0.6 && data[j].percentage < 0.8) {
                      color = yellow;
                    } else if (data[j].percentage < 0.6 && data[j].percentage > 0) {
                      color = green;
                    } else {
                      color = blue;
                    }

                    let point = _this.setPointToMap(info, { color: color, radius: 6 }, [14, 22]);
                    pbPoints.push(point);
                  }
                }
              }
            }
          }
        });
        // eslint-disable-next-line no-unused-vars
        for (const [key, value] of coodMap) {
          let str = "";
          let info = {};
          value.forEach((item) => {
            // let dev_id = item.dev_id;
            let wgs84 = projection.MercatorToWgs84(item.coordinate.split(" "));
            item.center = wgs84;
            data.id = item.dev_id;
            let tmpColor = "";
            if (item.data.percentage >= 0.8) {
              tmpColor = red;
            } else if (item.data.percentage >= 0.6 && item.data.percentage < 0.8) {
              tmpColor = yellow;
            } else if (item.data.percentage < 0.6 && item.data.percentage > 0) {
              tmpColor = green;
            } else {
              tmpColor = blue;
            }
            str += `<tr>
                <td style="padding:3px;text-align:center">
                    <div style="width:12px;height:12px;border-radius:50%;background:${tmpColor}"></div>
                </td>
                <td style="padding:3px;text-align:center">${item.data.trName}</td>
                <td style="padding:3px;text-align:center">${item.data.pluGfRl ? item.data.pluGfRl : 0}kVA</td>
                <td style="padding:3px;text-align:center">${item.data.conGfRl ? item.data.conGfRl : 0}kVA</td>
                <td style="padding:3px;text-align:center">${(item.data.stl * 100).toFixed(5)}%</td>
                <td style="padding:3px;text-align:center"><button type="button" id="${item.data.trId}">查看</button></td>
            </tr>`;
            info = {
              id: item.data.id,
              coord: [item.center[0], item.center[1]],
              text: str
            };
            let color = "";
            if (item.data.percentage >= 0.8) {
              color = red;
            } else if (item.data.percentage >= 0.6 && item.data.percentage < 0.8) {
              color = yellow;
            } else if (item.data.percentage < 0.6 && item.data.percentage > 0) {
              color = green;
            } else {
              color = blue;
            }
            // 设置点文字
            let point = _this.setPointToMap(info, { color: color, radius: 6 }, [14, 22]);
            // pbPoints.push(point);
            // let text = _this.setTextToMap({
            // 	text:item.data.trName,
            // 	id: item.data.id,
            // 	coord: [item.center[0], item.center[1]],
            // }, {color:color, offset:[0, 1]}, [17, 21]);
            // pbTexts.push(text);
            point.on("click", async (e) => {
              this.showMapDialog = true;
              let x = e.originalEvent.offsetX;
              let y = e.originalEvent.offsetY;
              this.$nextTick(() => {
                let dom = document.getElementById("mapDialog");
                dom.style.left = x + "px";
                dom.style.top = y + "px";
              });

              let html = `<table>
                    <colgroup>
                        <col width="5%"></col>
                        <col width="45%"></col>
                        <col width="100px"></col>
                        <col width="100px"></col>
                        <col width="150px"></col>
                        <col width="150px"></col>
                    </colgroup>
                    <tr><th></th><th>名称</th><th>可接入</th><th>已接入</th><th>渗透率</th><th>查看接入光伏用户</th></tr>
                    ${str}
             </table>`;
              this.mapDialogHtml = html;
            });
          });
        }
      });
    },
    handleMapDialogClick(e) {
      //   let target = e.target.tagName;
      //   this.equipId = e.target.id;
      //   if (target === "BUTTON" || target === "button") {
      //   }
    },
    // 设置地图光伏数据
    showGfDataByCode(power, level, item, range, style, save) {
      let _this = this;
      let name = item.name;
      let code = item.orgCode;
      let gisData = _this.areaDeptDataMap.get(name);
      if (gisData) {
        let str = `${name} \n 可接入:${gisData.pluGfRl}MVA \n 已接入:${gisData.conGfRl}MVA`;
        power.getAreaInfoByCode({ code: code, codeType: "ORG_CODE" }, (res) => {
          let info = {
            id: code,
            coord: res.center,
            text: str
          };
          let text = _this.setTextToMap(info, style, range, level);
          save !== "noSave" ? this.textMap.set(code, text) : null;
        });
      }
    },
    // 在地图上设置点
    setPointToMap(info = {}, style = {}, range) {
      // 实例化对象
      const point = new WebGIS.Graphics.Point(this.map);
      // 创建点数据
      const pointData = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              id: info.id ? info.id : "newPoint", // id
              "circle-radius": style.radius ? style.radius : 5, // 半径
              "circle-color": style.color ? style.color : "yellow", // 内环颜色
              "circle-stroke-width": style.strokeWidth ? style.strokeWidth : 0, // 外环宽度
              "circle-stroke-color": style.strokeColor ? style.strokeColor : "yellow" // 外环颜色
            },
            geometry: {
              type: "Point",
              coordinates: info.coord ? info.coord : [130.127285, 23.003367] // 坐标点
            }
          }
        ]
      };
      // 设置样式
      const pointStyle = {
        paint: {
          // 绘制配置
          "circle-color": ["get", "circle-color"],
          "circle-opacity": 1,
          "circle-radius": ["get", "circle-radius"]
        }
      };
      // 设置图层数据
      point.setData(pointData, pointStyle, {});
      range ? point.setZoomRange(range[0], range[1]) : "";
      point.addText("text", { "symbol-placement": "point" });
      return point;
    },
    // 在地图上设置文本
    setTextToMap(info = {}, style = {}, range, level) {
      const text = new WebGIS.Graphics.Text(this.map);
      let step = 0;
      if (level === "country") {
        step = 0.06;
      } else if (level === "town") {
        step = 0.02;
      } else {
        step = 0;
      }
      info.coord[1] = info.coord[1] + step;
      // 追加对象数据
      let newText = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              id: info.id ? info.id : "newTest",
              text: info.text ? info.text : "新增文本",
              "text-offset": style.offset ? style.offset : [0, 2],
              "text-color": style.color ? style.color : "#333",
              "text-size": style.size ? style.size : 14
            },
            geometry: {
              type: "Point",
              coordinates: info.coord ? info.coord : [113.127285, 23.003367]
            }
          }
        ]
      };
      // 样式配置
      let textStyle = {
        text_propertiesName: "text" // 配置图层展示的属性字段
      };
      text.addText(newText, textStyle);
      range ? text.setZoomRange(range[0], range[1]) : "";
      return text;
    },
    // 在地图上设置文本 Layer
    setTextLayerToMap(info = {}, style = {}, range, level) {
      let text = new WebGIS.Graphics.TextLayer(this.map); // 初始化控件
      let data = {
        type: "Feature",
        properties: {
          name: info.text
        },
        geometry: {
          type: "Point",
          coordinates: [info.coord[0], info.coord[1], 20000]
        }
      };
      // 设置数据
      text.setData(data, { textField: "name" });
      return text;
    },
    // 获取地图等级
    getLevel() {
      let zoom = this.map.getZoom();
      let level = "";
      if (zoom > 12 && zoom <= 14) {
        level = "station";
      } else if (zoom >= 14) {
        level = "line";
      } else {
        level = this.power.getZoomLevel();
      }

      return level;
    },
    // 图层钻取
    async drillDown(name) {
      this.dblclick = true;
      this.lastLevel = "";
      setTimeout(async () => {
        let data = this.areaInfoMap.get(name);
        // eslint-disable-next-line no-unused-vars
        let level = this.getLevel();
        if (!data) {
          this.$emit("station-click", null, station, this.map);
        } else {
          let orgCode = data.orgCode;
          await this.power.drillDown(orgCode);
          if (name.indexOf("供电局") !== -1) {
            if (name === "三水供电局") {
              this.map.setZoom(1);
            } else {
              this.map.setZoom(10);
            }
          } else if (name.indexOf("供电所") !== -1) {
            this.lastLevel = "station";
            this.map.setZoom(12.1);
          }
        }
      }, 1000);
    },
    // 双击跳转
    async dblDown(name, orgCode) {
      this.power.specifiedArea(orgCode);
      // await this.power.drillDown(orgCode);
      setTimeout(() => {
        if (name.includes("供电所")) {
          new WebGIS.Display.Filter(this.map).setPSRVisibility(false);
          this.map.setZoom(12.1);
        } else {
          this.map.setZoom(10.1);
        }
      }, 1000);
    },
    // 清除站点（隐藏）
    clearStation() {
      if (!stationPoints.length && !stationTexts.length) return;

      stationPoints.forEach((p) => {
        p.removeLayer();
      });
      stationTexts.forEach((t) => {
        t.removeLayer();
      });
      stationPoints = [];
      stationTexts = [];
    },
    // 清除配变设备（隐藏）
    clearPb() {
      if (!pbPoints.length && !pbTexts.length) return;
      pbPoints.forEach((p) => {
        p.removeLayer();
      });
      pbTexts.forEach((t) => {
        t.removeLayer();
      });
      pbPoints = [];
      pbTexts = [];
    },
    setAreaDeptMap() {
      let map = new Map();
      let areaDeptInfoMap = new Map();
      this.areaDeptInfo.forEach((item) => {
        let data = this.areaDeptDataMap.get(item.name);
        let obj = Object.assign({}, item, data);
        map.set(item.name, obj);
        if (!areaDeptInfoMap.has(item.level)) {
          // 将光伏数据也存到里面
          let arr = [obj];
          areaDeptInfoMap.set(item.level, arr);
        } else {
          let arr = areaDeptInfoMap.get(item.level);
          arr.push(obj);
          areaDeptInfoMap.set(item.level, arr);
        }
      });
      this.areaInfoMap = map;
      this.areaDeptInfoMap = areaDeptInfoMap;
    },
    setAreaDeptDataMap(data) {
      for (let key in data) {
        this.areaDeptDataMap.set(key, data[key]);
        if (data[key].deptVo) {
          this.setAreaDeptDataMap(data[key].deptVo);
        }
      }
    },
    // 设置地图光伏颜色样式
    setGfStyleByCode(power, data, areaInfoMap, areaDeptDataMap) {
      let powerStyle = [
        { codes: [], color: red, codeType: "CODE" },
        { codes: [], color: yellow, codeType: "CODE" },
        { codes: [], color: green, codeType: "CODE" },
        { codes: [], color: blue, codeType: "CODE" }
      ];
      for (const [key, value] of areaDeptDataMap) {
        if (areaInfoMap.has(key)) {
          let orgCode = this.areaInfoMap.get(key).orgCode;
          let pbRl = value.pbRl; // 总容量
          //   let pluGfRl = value.pluGfRl; // 可接入
          let conGfRl = value.conGfRl; // 已接入
          let precent = conGfRl / pbRl; // 百分比
          if (precent >= 0.8) {
            powerStyle[0].codes.push(orgCode);
          } else if (precent >= 0.6 && precent < 0.8) {
            powerStyle[1].codes.push(orgCode);
          } else if (precent < 0.6 && precent > 0) {
            powerStyle[2].codes.push(orgCode);
          } else {
            powerStyle[3].codes.push(orgCode);
          }
        }
      }
      power.setStyleByCode(powerStyle);
    },
    // 设置文字
    setText(power, info, range, style = {}) {
      let text = new WebGIS.Graphics.Text(this.map); // 创建text
      let center = info.center;

      // 样式配置
      let textStyle = {
        text_propertiesName: "text" // 配置图层展示的属性字段
      };

      let step = 0.04;
      let level = power.getZoomLevel();
      if (level === "town") {
        step = 0;
      }
      let tmpCenter = JSON.parse(JSON.stringify(center));
      tmpCenter[1] = tmpCenter[1] + step;
      let newText = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              id: "newTest",
              text: info.text,
              "text-offset": [0, 2],
              "text-color": style.color ? style.color : "#091934",
              "text-size": 13
            },
            geometry: {
              type: "Point",
              coordinates: tmpCenter
            }
          }
        ]
      };
      text.addText(newText, textStyle);
      range ? text.setZoomRange(range[0], range[1]) : "";
    },
    setPoint(point, info, style) {
      // 创建点数据
      let pointData = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              id: info.id, // id
              "circle-radius": 6, // 半径
              "circle-color": "yellow", // 内环颜色
              "circle-stroke-width": 0, // 外环宽度
              "circle-stroke-color": "yellow" // 外环颜色
            },
            geometry: {
              type: "Point",
              coordinates: info.center // 坐标点
            }
          }
        ]
      };
      // 设置样式
      let pointStyle = {
        paint: {
          // 绘制配置
          "circle-color": ["get", "circle-color"],
          "circle-opacity": 1,
          "circle-radius": ["get", "circle-radius"]
        }
      };
      // 设置图层数据
      point.setData(pointData, pointStyle, {});
    },
    setStation() {
      let powerStyle = [
        { codes: [], color: red, codeType: "CODE" },
        { codes: [], color: yellow, codeType: "CODE" },
        { codes: [], color: green, codeType: "CODE" },
        { codes: [], color: blue, codeType: "CODE" }
      ];
      let level = this.power.getZoomLevel();
      let tmpArr = [];
      this.stations.forEach((station) => {
        let areaName = station.areaName;
        if (areaName === this.lastAreaInfo.name) {
          station.pluGfRl = station.plu;
          station.conGfRl = station.con;
          station.name = station.stationDesc;
          tmpArr.push(station);
          this.$emit("onLevel", level, tmpArr);
          let x = station.zuoBiaoX;
          let y = station.zuoBiaoY;
          let pluGfRl = station.plu; // 可接入
          let conGfRl = station.con; // 已接入
          let pbRl = pluGfRl + conGfRl; // 总容量
          let precent = conGfRl / pbRl; // 百分比
          // 实例化对象
          let point = new WebGIS.Graphics.Point(this.map);
          let text = new WebGIS.Graphics.Text(this.map);
          stationPoints.push(point);
          textPoints.push(text);
          let _this = this;
          point.on("click", () => {
            _this.$emit("station-click", null, station, _this.map);
          });

          let style = { codes: [], color: "#59e4ff", codeType: "CODE" };
          if (precent >= 0.8) {
            style = powerStyle[0];
          } else if (precent >= 0.6 && precent < 0.8) {
            style = powerStyle[1];
          } else if (precent < 0.6 && precent > 0) {
            style = powerStyle[2];
          } else {
            style = powerStyle[3];
          }
          this.setStationPoint(point, station, x, y, style);
          this.setStationText(text, station, x, y, style);
          this.setStationText(text, station, x, y, style);
        }
      });
    },
    setStationPoint(point, station, x, y, style, range) {
      // 创建点数据
      let pointData = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              id: station.stationId, // id
              "circle-radius": 8, // 半径
              "circle-color": style.color, // 内环颜色
              "circle-stroke-width": 0, // 外环宽度
              "circle-stroke-color": "yellow" // 外环颜色
            },
            geometry: {
              type: "Point",
              coordinates: [x, y] // 坐标点
            }
          }
        ]
      };
      // 设置样式
      let pointStyle = {
        paint: {
          // 绘制配置
          "circle-color": ["get", "circle-color"],
          "circle-opacity": 1,
          "circle-radius": ["get", "circle-radius"]
        }
      };
      // 设置图层数据
      point.setData(pointData, pointStyle, {});
      //   range ? text.setZoomRange(range[0], range[1]) : "";
    },
    setStationText(text, station, x, y, style) {
      let str = `\n 可接入:${station.plu}kVA \n 已接入:${station.con}kVA`;
      if (station.plu === null && station.con === null) {
        str = "";
      }
      // 追加对象数据
      let newText = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              id: "newTest",
              text: `${station.stationDesc} ${str}`,
              "text-offset": [0, 0.8],
              "text-color": style.color,
              "text-size": 14,
              "text-weight": "bold"
            },
            geometry: {
              type: "Point",
              coordinates: [x, y]
            }
          }
        ]
      };
      // 样式配置
      let textStyle = {
        text_propertiesName: "text" // 配置图层展示的属性字段
      };
      //   let layerid = text.addText(newText, textStyle);
      text.addText(newText, textStyle);
    },
    clearStationPoint() {
      stationPoints.forEach((p) => {
        p.removeLayer();
      });
      textPoints.forEach((t) => {
        t.removeLayer();
      });
      textPoints = [];
      stationPoints = [];
    },
    // 清除文字
    clearText() {
      if (this.textMap) {
        // eslint-disable-next-line no-unused-vars
        for (const [key, value] of this.textMap) {
          value.removeLayer();
        }

        this.textMap = new Map();
      }
    }
  }
};
</script>
<style lang="scss">
.mapboxgl-control-container {
  .mapboxgl-coordinate-control,
  .mapboxgl-ctrl-scale {
    display: none;
  }
}
</style>
<style lang="scss" scoped>
.zhikan-map {
  height: 100%;
  width: 100%;
}

::v-deep {
  .mapboxgl-popup {
    .mapboxgl-popup-tip {
      border-top-color: #fff;
    }
    .mapboxgl-popup-content {
      background: #fff;
      color: #333;
      padding: 12px 8px 8px 8px;
      .mapboxgl-popup-close-button {
        right: 4px;
        font-size: 18px;
      }
    }
  }

  th {
    background: #eaf8ff;
    padding: 5px;
  }
  th,
  td {
    border: 1px solid #eee;
  }
  button {
    padding: 3px 8px;
    border: 1px solid #eee;
    border-radius: 5px;
  }
}
</style>
