import { Message } from "element-ui";

export const themeColors = [
  /* 道奇蓝（默认） */
  { color: "#001237", themeColor: "default", title: "道奇蓝" },
  /* 亮白色 */
  { color: "#FFFFFF", themeColor: "light", title: "亮白" },
  /* 猩红色 */
  { color: "#F5222D", themeColor: "dusk", title: "猩红" },
  /* 橙红色 */
  { color: "#FA541C", themeColor: "volcano", title: "橙红" },
  /* 金色 */
  { color: "#FADB14", themeColor: "yellow", title: "金色" },
  /* 绿宝石 */
  { color: "#13C2C2", themeColor: "emerald", title: "绿宝石" },
  /* 酸橙绿 */
  { color: "#52C41A", themeColor: "auroraGreen", title: "酸橙绿" },
  /* 深粉色 */
  { color: "#EB2F96", themeColor: "pink", title: "深粉色" },
  /* 深紫罗兰色 */
  { color: "#722ED1", themeColor: "saucePurple", title: "深紫罗兰" }
];

/**
 * 颜色转换函数
 * @method hexToRgb hex 颜色转 rgb 颜色
 * @method rgbToHex rgb 颜色转 Hex 颜色
 * @method getDarkColor 加深颜色值
 * @method getLightColor 变浅颜色值
 */
export function useChangeColor() {
  // str 颜色值字符串
  const hexToRgb = (str) => {
    let hexs = "";
    const reg = /^#?[0-9A-Fa-f]{6}$/;
    if (!reg.test(str)) {
      Message.warning("输入错误的hex");
      return "";
    }
    str = str.replace("#", "");
    hexs = str.match(/../g);
    for (let i = 0; i < 3; i++) hexs[i] = parseInt(hexs[i], 16);
    return hexs;
  };
  // r 代表红色 | g 代表绿色 | b 代表蓝色
  const rgbToHex = (r, g, b) => {
    const reg = /^\d{1,3}$/;
    if (!reg.test(r) || !reg.test(g) || !reg.test(b)) {
      Message.warning("输入错误的rgb颜色值");
      return "";
    }
    const hexs = [r.toString(16), g.toString(16), b.toString(16)];
    for (let i = 0; i < 3; i++) {
      if (hexs[i].length === 1) hexs[i] = `0${hexs[i]}`;
    }
    return `#${hexs.join("")}`;
  };
  // color 颜色值字符串 | level 变浅的程度，限0-1之间
  const getDarkColor = (color, level) => {
    const reg = /^#?[0-9A-Fa-f]{6}$/;
    if (!reg.test(color)) {
      Message.warning("输入错误的hex颜色值");
      return "";
    }
    const rgb = useChangeColor().hexToRgb(color);
    for (let i = 0; i < 3; i++) rgb[i] = Math.floor(rgb[i] * (1 - level));
    return useChangeColor().rgbToHex(rgb[0], rgb[1], rgb[2]);
  };
  // color 颜色值字符串 | level 加深的程度，限0-1之间
  const getLightColor = (color, level) => {
    const reg = /^#?[0-9A-Fa-f]{6}$/;
    if (!reg.test(color)) {
      Message.warning("输入错误的hex颜色值");
      return "";
    }
    const rgb = useChangeColor().hexToRgb(color);
    for (let i = 0; i < 3; i++) {
      rgb[i] = Math.floor((255 - rgb[i]) * level + rgb[i]);
    }
    return useChangeColor().rgbToHex(rgb[0], rgb[1], rgb[2]);
  };
  return {
    hexToRgb,
    rgbToHex,
    getDarkColor,
    getLightColor
  };
}
