<template>
  <div style="width: 100%; height: 100%">
    <div class="tabs-container">
      <el-tabs
        v-model="active"
        type="card"
        @tab-click="changeModule"
      >
        <el-tab-pane
          v-for="item in moduleList"
          :key="item.key"
          :label="item.moduleName"
          :name="item.key"
        >
          <span slot="label">
            {{ item.moduleName }}
            <span style="margin-left: 5px">{{ item.count }}</span>
          </span>
          {{ item.content }}
        </el-tab-pane>
      </el-tabs>
    </div>
    <div style="height: calc(100% - 40px); width: 100%">
      <transition name="fade">
        <nari-table
          ref="orderTable"
          :key="active"
          :columns="columns"
          :data="listData"
          size="medium"
          height="100%"
          :summary-method="getSummaries"
          :show-summary="showSummary"
        >
          <template #decreaseLoad="{ row }">
            <span>{{ row.decreaseLoad }} MV</span>
          </template>
        </nari-table>
      </transition>
    </div>
  </div>
</template>
<script>
import { AssistantApi } from "@/api/assistant-message";
import equipmentData from "@/mock/equipment.json";
export default {
  data() {
    return {
      active: "section",
      moduleList: [
        {
          moduleName: "断面越限",
          key: "section",
          count: 0
        },
        {
          moduleName: "失压设备",
          key: "lossEquipment",
          count: 0
        },
        // {
        //   moduleName: "故障设备信息",
        //   key: "lineInfo",
        //   count: 0
        // },
        {
          moduleName: "新增风险",
          key: "risk",
          count: 0
        },
        {
          moduleName: "负荷情况",
          key: "load",
          count: 0
        },
        {
          moduleName: "信息报送",
          key: "infoReport",
          count: 0
        }
      ],
      section_columns: [
        { label: "断面越限", prop: "sectionName", tooltip: true },
        { label: "当前值", prop: "ppValue" },
        { label: "限值", prop: "ttcValue" },
        { label: "调控策略", prop: "ddValue" }
      ],
      section_listData: [],
      lossEquipment_columns: [
        {
          label: "厂站",
          prop: "STATIONNAME"
        },
        {
          label: "电压等级",
          prop: "VOLTAGE"
        },
        {
          label: "设备名称",
          prop: "BUSNAME"
        }
      ],
      lossEquipment_listData: [],
      lineInfo_columns: [
        {
          label: "线路名称",
          prop: "lineName"
        },
        {
          label: "线路类型",
          prop: "lineType"
        },
        {
          label: "线路状态",
          prop: "lineState"
        }
      ],
      lineInfo_listData: [],
      risk_columns: [
        {
          label: "故障设备",
          prop: "equipName"
        },
        {
          label: "事件等级",
          prop: "accidentLevel"
        },
        {
          label: "失压厂站",
          prop: "stationName"
        }
      ],
      risk_listData: [],
      load_columns: [
        {
          label: "厂站",
          prop: "stationName"
        },
        {
          label: "类别",
          prop: "loadSituationType"
        },
        {
          label: "受影响负荷",
          prop: "decreaseLoad"
        }
      ],
      load_listData: [],
      infoReport_columns: [
        {
          label: "报送对象",
          prop: "reportObject",
          align: "left",
          "show-overflow-tooltip": false
        },
        {
          label: "报送内容",
          prop: "reportContent",
          align: "left",
          minWidth: 150,
          "show-overflow-tooltip": false
        }
      ],
      infoReport_listData: [],
      columns: [
        {
          label: "厂站",
          prop: "STATIONNAME"
        },
        {
          label: "电压等级",
          prop: "VOLTAGE"
        },
        {
          label: "设备名称",
          prop: "EQUIPMENTNAME"
        }
      ],
      listData: [],
      accidentInfoId: "",
      modelType: true,
      overhaulEquips: []
    };
  },
  computed: {
    showSummary() {
      return this.active === "load";
    }
  },
  mounted() {
    this.initData();
    this.$bus.$on("demo-id", (id) => {
      console.log("demo-id: ", id);
      this.demoId = id;
    });
    this.$bus.$on("accident-id", ({ accidentInfoId, modelType, overhaulEquips }) => {
      console.log("accident-id: ", accidentInfoId, modelType, overhaulEquips);
      this.accidentInfoId = accidentInfoId;
      this.modelType = modelType;
      this.overhaulEquips = overhaulEquips;
      accidentInfoId ? this.fecthAllAssistantInfo() : this.getMockData();
    });
  },
  destroyed() {
    this.$bus.$off("demo-id");
    this.$bus.$off("accident-id");
  },
  methods: {
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index === 1) {
          sums[index] = "";
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          // 格式化数值为保留两位小数的字符串，然后转换为数值类型
          sums[index] = parseFloat(sums[index].toFixed(2));
          sums[index] += " MV";
        } else {
          sums[index] = "N/A";
        }
      });

      return sums;
    },
    // 根据 key 更新 count
    updateCountByKey(data, targetKey, newValue) {
      data.forEach((item) => {
        if (item.key === targetKey && item.count !== newValue) {
          item.count = newValue;
        }
      });
    },
    getMockData() {
      this.lossEquipment_listData = equipmentData.data;
      this.updateCountByKey(this.moduleList, "lossEquipment", this.lossEquipment_listData.length);
    },
    initData() {
      this.listData = this[`${this.active}_listData`];
      this.columns = this[`${this.active}_columns`];
    },
    changeModule(module) {
      this.active = module.name;
      this.initData();
    },
    // 获取负荷情况列表
    getLoadSituationList() {
      const params = {
        accidentInfoId: this.accidentInfoId,
        demoId: this.demoId,
        modelType: this.modelType,
        overhaulEquips: this.overhaulEquips
      };
      AssistantApi.fetchLoadSituationList(params).then((res) => {
        if (res.code === 200) {
          this.load_listData = res.data;
          this.updateCountByKey(this.moduleList, "load", this.load_listData.length);
        }
      });
    },
    // 获取风险列表
    getRiskList() {
      const params = {
        accidentInfoId: this.accidentInfoId,
        demoId: this.demoId,
        modelType: this.modelType,
        overhaulEquips: this.overhaulEquips
      };
      AssistantApi.fetchRiskList(params).then((res) => {
        if (res.code === 200) {
          this.risk_listData = res.data;
          this.updateCountByKey(this.moduleList, "risk", this.risk_listData.length);
        }
      });
    },
    // 获取故障设备信息列表
    getEquipmentTroubleList() {
      const params = {
        accidentInfoId: this.accidentInfoId,
        demoId: this.demoId,
        modelType: this.modelType,
        overhaulEquips: this.overhaulEquips
      };
      AssistantApi.fetchEquipmentTroubleList(params).then((res) => {
        if (res.code === 200) {
          this.lineInfo_listData = res.data;
          this.updateCountByKey(this.moduleList, "lineInfo", this.lineInfo_listData.length);
        }
      });
    },
    // 获取失压设备列表
    getLoseEnergyEquList() {
      const params = {
        accidentInfoId: this.accidentInfoId,
        demoId: this.demoId,
        modelType: this.modelType
      };
      AssistantApi.fetchLoseEnergyEquList(params).then((res) => {
        if (res.code === 200) {
          this.lossEquipment_listData = res.data;
          this.updateCountByKey(this.moduleList, "lossEquipment", this.lossEquipment_listData.length);
        }
      });
    },
    // 获取断面越限信息列表
    getSectionExceedsLimitList() {
      const params = {
        accidentInfoId: this.accidentInfoId,
        demoId: this.demoId,
        modelType: this.modelType,
        overhaulEquips: this.overhaulEquips
      };
      AssistantApi.fetchSectionExceedsLimitList(params).then((res) => {
        if (res.code === 200) {
          this.section_listData = res.data;
          this.listData = this.section_listData;
          this.updateCountByKey(this.moduleList, "section", this.section_listData.length);
        }
      });
    },
    // 获取信息报送列表
    getReportMessageList() {
      const params = {
        accidentInfoId: this.accidentInfoId,
        demoId: this.demoId,
        modelType: this.modelType,
        overhaulEquips: this.overhaulEquips
      };
      AssistantApi.fetchReportMessageList(params).then((res) => {
        if (res.code === 200) {
          this.infoReport_listData = res.data;
          this.updateCountByKey(this.moduleList, "infoReport", this.infoReport_listData.length);
        }
      });
    },
    fecthAllAssistantInfo() {
      this.getReportMessageList();
      this.getSectionExceedsLimitList();
      this.getLoseEnergyEquList();
      // this.getEquipmentTroubleList();
      this.getRiskList();
      this.getLoadSituationList();
    }
  }
};
</script>
<style lang="scss" scoped>
.raise {
  --color: #acb3e8;
  --hover: #67ff91;
}

button {
  display: inline-block;
  background: none;
  border: 1px solid;
  font-size: 12px;
  line-height: 1;
  margin: 0.2em;
  padding: 0.5em 1em;
}

button {
  color: var(--color);
  -webkit-transition: 0.25s;
  transition: 0.25s;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.raise:hover,
.active {
  border-color: var(--hover);
  color: var(--hover);
  font-weight: 600;
  -webkit-box-shadow: 0 0.5em 0.5em -0.4em var(--hover);
  box-shadow: 0 0.5em 0.5em -0.4em var(--hover);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
