<template>
  <div>
    <!-- 遮罩层 -->
    <div
      class="overlay"
      v-if="loading || error"
    ></div>

    <!-- 加载中提示 -->
    <div
      class="loading-container"
      v-if="loading"
    >
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <div class="loading-text">
        <span>{{ loadingText }}</span>
        <span class="loading-dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </span>
      </div>
    </div>

    <!-- 错误提示 -->
    <div
      class="error-container"
      v-if="error"
    >
      <div class="error-message">
        <span>加载错误: {{ error }}</span>
        <button @click="$emit('retry')">重试</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "FlowGraphLoading",
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: [String, Error],
      default: null
    },
    loadingText: {
      type: String,
      default: "正在加载潮流图数据"
    }
  }
};
</script>

<style lang="scss" scoped>
// 遮罩层
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(2px);
  z-index: 15;
  cursor: wait;
  pointer-events: all;
}

// 加载容器
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 25px 40px;
}

// 加载动画
.loading-spinner {
  display: flex;
  position: relative;
  width: 70px;
  height: 70px;
  margin-bottom: 15px;
}

// 加载文本
.loading-text {
  font-size: 16px;
  display: flex;
  align-items: center;
  letter-spacing: 0.5px;
}

.loading-dots {
  display: inline-flex;
  margin-left: 5px;
}

// 错误提示
.error-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  border-radius: 5px;
  padding: 15px;
  max-width: 80%;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  span {
    margin-bottom: 10px;
    font-size: 14px;
  }

  button {
    border: none;
    padding: 5px 15px;
    border-radius: 3px;
    cursor: pointer;
    font-weight: bold;
  }
}
</style>
