<template>
  <div>
    <el-tooltip
      :content="isCopied ? '已复制' : '复制'"
      placement="top"
      :visible-arrow="false"
    >
      <div
        class="flex items-center justify-center box-border p-1 text-gray-400 hover:text-gray-500 rounded-md cursor-pointer"
        @click="onClickCopy"
      >
        <svg-icon
          style="width: 14px; height: 14px"
          :icon-name="isCopied ? 'copied' : 'copy'"
        ></svg-icon>
      </div>
    </el-tooltip>
  </div>
</template>
<script>
export default {
  name: "CopyBtn",
  data() {
    return {
      isCopied: false
    };
  },
  methods: {
    onClickCopy() {
      this.isCopied = true;
      this.$emit("copy");
      setTimeout(() => {
        this.isCopied = false;
      }, 2000);
    }
  }
};
</script>
<style lang="scss" scoped></style>
