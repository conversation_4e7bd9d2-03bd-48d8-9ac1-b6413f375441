import * as d3 from "d3";
import { GeometryUtils } from "../utils/geometryUtils.js";
import { ThemeManager } from "../core/ThemeManager.js";

/**
 * 标签渲染器类
 * 负责节点标签和连接线标签的渲染
 */
export class LabelRenderer {
  constructor(renderManager, dataProcessor) {
    this.renderManager = renderManager;
    this.dataProcessor = dataProcessor;
    this.themeManager = new ThemeManager();
    this.geometryUtils = new GeometryUtils();
    this.virtualNodeScale = 2; // 默认虚拟节点放大倍数
  }

  /**
   * 设置主题
   * @param {string} theme 主题名称 ('light' | 'dark')
   */
  setTheme(theme) {
    this.themeManager.setTheme(theme);
  }

  /**
   * 设置虚拟节点缩放倍数
   * @param {number} scale 缩放倍数
   */
  setVirtualNodeScale(scale) {
    this.virtualNodeScale = scale > 0 && scale <= 10 ? scale : 2;
  }

  /**
   * 计算节点半径，考虑虚拟节点的放大
   * @param {Object} nodeData 节点数据
   * @returns {number} 节点半径
   */
  calculateNodeRadius(nodeData) {
    let baseRadius = nodeData.symbolSize ? nodeData.symbolSize / 4 : 3;

    // 检查是否是虚拟节点
    if (nodeData.attribute && nodeData.attribute.sub_type === "virtual") {
      baseRadius *= this.virtualNodeScale;
    }

    return baseRadius;
  }

  /**
   * 检查渲染环境是否就绪
   * @returns {boolean} 是否可以安全渲染
   */
  isRenderReady() {
    const safeWidth = this.renderManager.width || 800;
    const safeHeight = this.renderManager.height || 600;

    // 确保容器尺寸有效
    if (safeWidth <= 0 || safeHeight <= 0) {
      console.warn("Container dimensions not ready for label rendering");
      return false;
    }

    // 确保SVG容器存在
    if (!this.renderManager.svg || !this.renderManager.container) {
      console.warn("SVG container not ready for label rendering");
      return false;
    }

    return true;
  }

  /**
   * 渲染节点标签
   * @param {Array} nodes 节点数组
   * @param {boolean} showNodeLabels 是否显示节点标签
   * @returns {d3.Selection} 节点标签选择集
   */
  renderNodeLabels(nodes, showNodeLabels) {
    // 检查渲染环境是否就绪
    if (!this.isRenderReady()) {
      console.warn("Delaying node label rendering - environment not ready");
      // 延迟到下一个渲染帧重试
      requestAnimationFrame(() => {
        if (this.isRenderReady()) {
          this.renderNodeLabels(nodes, showNodeLabels);
        }
      });
      return null;
    }

    const labelsContainer = this.renderManager.createGroup("labels");

    const nodeLabelFillColor = this.themeManager.getLabelColor("node", "fill");
    const nodeLabelStrokeColor = this.themeManager.getLabelColor("node", "stroke");

    const renderedLabels = labelsContainer
      .selectAll("text")
      .data(nodes)
      .enter()
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .text((d) => d.des || d.name)
      .style("font-size", "10px")
      .style("display", showNodeLabels ? "block" : "none")
      .style("pointer-events", "none")
      .style("fill", nodeLabelFillColor)
      .style("text-shadow", `-1px -1px 0 ${nodeLabelStrokeColor}, 1px -1px 0 ${nodeLabelStrokeColor}, -1px 1px 0 ${nodeLabelStrokeColor}, 1px 1px 0 ${nodeLabelStrokeColor}`);

    this.renderManager.renderedLabels = renderedLabels;
    return renderedLabels;
  }

  /**
   * 渲染连接线标签
   * @param {Array} links 连接线数组
   * @param {boolean} showLinkLabels 是否显示连接线标签
   * @returns {d3.Selection} 连接线标签选择集
   */
  renderLinkLabels(links, showLinkLabels) {
    // 检查渲染环境是否就绪
    if (!this.isRenderReady()) {
      console.warn("Delaying link label rendering - environment not ready");
      // 延迟到下一个渲染帧重试
      requestAnimationFrame(() => {
        if (this.isRenderReady()) {
          this.renderLinkLabels(links, showLinkLabels);
        }
      });
      return null;
    }

    const linkLabelsContainer = this.renderManager.createGroup("link-labels");

    const linkLabelFillColor = this.themeManager.getLabelColor("link", "fill");
    const linkLabelStrokeColor = this.themeManager.getLabelColor("link", "stroke");

    const renderedLinkLabels = linkLabelsContainer
      .selectAll("text")
      .data(links, this.getLinkId)
      .enter()
      .append("text")
      .attr("dy", -5)
      .style("font-size", "9px")
      .style("text-anchor", "middle")
      .style("dominant-baseline", "central")
      .style("display", showLinkLabels ? "block" : "none")
      .style("pointer-events", "none")
      .style("fill", linkLabelFillColor)
      .style("font-weight", "bold")
      .style("text-shadow", `-1px -1px 1px ${linkLabelStrokeColor}, 1px -1px 1px ${linkLabelStrokeColor}, -1px 1px 1px ${linkLabelStrokeColor}, 1px 1px 1px ${linkLabelStrokeColor}`)
      .each(function (d) {
        const name = d.name || "";
        const measureValue = d.attribute && d.attribute.measurevalue ? d.attribute.measurevalue : "";

        // 创建完整的文本内容，优先显示名称
        const fullText = name || measureValue || "连接线";

        // 立即创建文本内容，确保有可见的标签
        const textElement = d3.select(this);
        textElement.selectAll("tspan").remove(); // 清除可能存在的旧tspan

        if (fullText && fullText.trim() !== "") {
          textElement.append("tspan").attr("x", 0).attr("dy", 0).text(fullText);
        } else {
          // 如果没有文本内容，显示占位符
          textElement.append("tspan").attr("x", 0).attr("dy", 0).text("—");
          console.warn(`连接线缺少标签文本: ${d.id || "unknown"}`, d);
        }

        // 设置初始宽度估算值
        d.labelComputedWidth = Math.max(fullText.length * 6, 20);

        // 使用多重延迟策略确保BBox获取成功
        const tryGetBBox = (retryCount = 0) => {
          if (retryCount > 3) {
            console.warn(`连接线标签BBox获取失败，使用估算值: ${d.id}`);
            return;
          }

          setTimeout(() => {
            try {
              // 检查元素是否在DOM中且可见
              if (this.isConnected && this.getBoundingClientRect().width > 0) {
                const bbox = this.getBBox();
                if (bbox.width > 0) {
                  d.labelComputedWidth = bbox.width;
                } else {
                  // BBox宽度为0，重试
                  tryGetBBox(retryCount + 1);
                }
              } else {
                // 元素不在DOM中或不可见，重试
                tryGetBBox(retryCount + 1);
              }
            } catch (e) {
              console.warn(`连接线标签BBox获取错误 (重试 ${retryCount + 1}):`, d.id, e);
              tryGetBBox(retryCount + 1);
            }
          }, (retryCount + 1) * 50); // 递增延迟时间
        };

        // 开始尝试获取BBox
        tryGetBBox();
      });

    this.renderManager.renderedLinkLabels = renderedLinkLabels;
    return renderedLinkLabels;
  }

  /**
   * 更新节点标签位置
   * @param {Array} nodes 节点数组
   */
  updateNodeLabelsPosition(nodes) {
    if (!this.renderManager.renderedLabels) return;

    // 只有在容器尺寸有效时才更新位置
    if (!this.isRenderReady()) return;

    this.renderManager.renderedLabels
      .attr("x", (d) => {
        const x = this.calculateNodeLabelX(d);
        // 最终验证坐标有效性
        return Number.isFinite(x) ? x : (this.renderManager.width || 800) / 2;
      })
      .attr("y", (d) => {
        const y = this.calculateNodeLabelY(d);
        // 最终验证坐标有效性
        return Number.isFinite(y) ? y : (this.renderManager.height || 600) / 2;
      })
      .attr("text-anchor", (d) => this.getNodeLabelAnchor(d));
  }

  /**
   * 计算节点标签X坐标
   */
  calculateNodeLabelX(node) {
    // 确保有安全的后备值
    const safeWidth = this.renderManager.width || 800;
    const fallbackX = safeWidth / 2;

    if (!node) return fallbackX;

    let x = node.x;
    if (!Number.isFinite(x) || isNaN(x) || x === 0) {
      x = fallbackX;
    }

    // 为 series_power_supply 节点根据 nodetype 调整标签位置
    if (node.attribute && node.attribute.stationstyle === "series_power_supply" && node.attribute.nodetype) {
      const nodeRadius = this.calculateNodeRadius(node);
      const labelOffset = nodeRadius + 5; // 减小偏移量，使文字更靠近节点
      const separationGap = 1; // 原来是 3

      if (node.attribute.nodetype === "left-half") {
        return x - separationGap - labelOffset; // 文字在左侧
      } else if (node.attribute.nodetype === "right-half") {
        return x + separationGap + labelOffset; // 文字在右侧
      }
    }

    return x; // 其他类型节点，x 坐标为节点中心
  }

  /**
   * 计算节点标签Y坐标
   */
  calculateNodeLabelY(node) {
    // 确保有安全的后备值
    const safeHeight = this.renderManager.height || 600;
    const fallbackY = safeHeight / 2;

    if (!node) return fallbackY;

    let y = node.y;
    if (!Number.isFinite(y) || isNaN(y) || y === 0) {
      y = fallbackY;
    }

    return y;
  }

  /**
   * 获取节点标签锚点
   */
  getNodeLabelAnchor(node) {
    if (node.attribute && node.attribute.stationstyle === "series_power_supply" && node.attribute.nodetype) {
      if (node.attribute.nodetype === "left-half") {
        return "end"; // 文字在左侧，锚点在末尾
      } else if (node.attribute.nodetype === "right-half") {
        return "start"; // 文字在右侧，锚点在开头
      }
    }
    return "middle"; // 默认居中锚点
  }

  /**
   * 更新连接线标签位置
   * @param {Array} links 连接线数组
   */
  updateLinkLabelsPosition(links) {
    if (!this.renderManager.renderedLinkLabels) return;

    // 只有在容器尺寸有效时才更新位置
    if (!this.isRenderReady()) return;

    const self = this;
    this.renderManager.renderedLinkLabels.attr("transform", function (d) {
      const transform = self.calculateLinkLabelTransform(d);

      // 验证transform字符串的有效性
      if (!transform || transform.includes("NaN") || transform.includes("Infinity")) {
        const safeWidth = self.renderManager.width || 800;
        const safeHeight = self.renderManager.height || 600;
        return `translate(${safeWidth / 2}, ${safeHeight / 2})`;
      }

      return transform;
    });
  }

  /**
   * 计算连接线标签变换
   */
  calculateLinkLabelTransform(link) {
    if (!link || !link.source || !link.target) {
      return "translate(0,0)";
    }

    // 获取节点位置 - 增加验证
    let sourceX = link.source.x || 0;
    let sourceY = link.source.y || 0;
    let targetX = link.target.x || 0;
    let targetY = link.target.y || 0;

    // 确保有安全的后备值
    const safeWidth = this.renderManager.width || 800;
    const safeHeight = this.renderManager.height || 600;

    // 验证并修复坐标
    if (!Number.isFinite(sourceX) || sourceX === 0) sourceX = safeWidth * 0.3;
    if (!Number.isFinite(sourceY) || sourceY === 0) sourceY = safeHeight * 0.3;
    if (!Number.isFinite(targetX) || targetX === 0) targetX = safeWidth * 0.7;
    if (!Number.isFinite(targetY) || targetY === 0) targetY = safeHeight * 0.7;

    const controlX = link._controlPointX || (sourceX + targetX) / 2;
    const controlY = link._controlPointY || (sourceY + targetY) / 2;

    // 计算贝塞尔曲线上的点
    const lineType = link.type || "default";
    const tValues = {
      default: 0.5
    };
    const t = tValues[lineType] || 0.5;

    // 计算贝塞尔曲线上的点坐标
    const labelX = Math.pow(1 - t, 2) * sourceX + 2 * (1 - t) * t * controlX + Math.pow(t, 2) * targetX;
    const labelY = Math.pow(1 - t, 2) * sourceY + 2 * (1 - t) * t * controlY + Math.pow(t, 2) * targetY;

    // 验证计算结果
    if (!Number.isFinite(labelX) || !Number.isFinite(labelY)) {
      return `translate(${(sourceX + targetX) / 2}, ${(sourceY + targetY) / 2})`;
    }

    // 计算曲线切线角度
    let degrees = 0;
    try {
      degrees = this.geometryUtils.calculateBezierTangentAngle(t, { x: sourceX, y: sourceY }, { x: controlX, y: controlY }, { x: targetX, y: targetY });
      // 验证角度值
      if (!Number.isFinite(degrees)) {
        degrees = 0;
      }
    } catch (e) {
      console.warn("Error calculating bezier tangent angle:", e);
      degrees = 0;
    }

    return `translate(${labelX}, ${labelY}) rotate(${degrees})`;
  }

  /**
   * 更新节点标签数据绑定
   * @param {Array} nodes 节点数组
   */
  updateNodeLabels(nodes) {
    if (!this.renderManager.renderedLabels) return;

    const nodeLabelFillColor = this.themeManager.getLabelColor("node", "fill");
    const nodeLabelStrokeColor = this.themeManager.getLabelColor("node", "stroke");

    // 进行数据绑定
    const nodeLabels = this.renderManager.container
      .select("g.labels")
      .selectAll("text")
      .data(nodes, (d) => d.id);

    // 移除不再需要的标签
    nodeLabels.exit().remove();

    // 添加新标签
    const enterSelection = nodeLabels
      .enter()
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .style("font-size", "10px")
      .style("pointer-events", "none")
      .style("fill", nodeLabelFillColor)
      .style("text-shadow", `-1px -1px 0 ${nodeLabelStrokeColor}, 1px -1px 0 ${nodeLabelStrokeColor}, -1px 1px 0 ${nodeLabelStrokeColor}, 1px 1px 0 ${nodeLabelStrokeColor}`);

    // 合并并应用属性
    const mergedLabels = nodeLabels.merge(enterSelection).text((d) => d.des || d.name);

    this.renderManager.renderedLabels = mergedLabels;
  }

  /**
   * 更新连接线标签数据绑定
   * @param {Array} links 连接线数组
   */
  updateLinkLabels(links) {
    if (!this.renderManager.renderedLinkLabels) {
      console.warn("renderedLinkLabels不存在，重新渲染连接线标签");
      this.renderLinkLabels(links, true);
      return;
    }

    const linkLabelFillColor = this.themeManager.getLabelColor("link", "fill");
    const linkLabelStrokeColor = this.themeManager.getLabelColor("link", "stroke");

    // 进行数据绑定
    const linkLabelsContainer = this.renderManager.container.select("g.link-labels");
    if (linkLabelsContainer.empty()) {
      console.warn("连接线标签容器不存在，重新渲染");
      this.renderLinkLabels(links, true);
      return;
    }

    const linkLabels = linkLabelsContainer.selectAll("text").data(links, this.getLinkId);

    // 移除不再需要的标签
    linkLabels.exit().remove();

    // 添加新标签
    const enterSelection = linkLabels
      .enter()
      .append("text")
      .attr("dy", -5)
      .style("font-size", "9px")
      .style("text-anchor", "middle")
      .style("dominant-baseline", "central")
      .style("pointer-events", "none")
      .style("fill", linkLabelFillColor)
      .style("font-weight", "bold")
      .style("text-shadow", `-1px -1px 1px ${linkLabelStrokeColor}, 1px -1px 1px ${linkLabelStrokeColor}, -1px 1px 1px ${linkLabelStrokeColor}, 1px 1px 1px ${linkLabelStrokeColor}`)
      .each(function (d) {
        const name = d.name || "";
        const measureValue = d.attribute && d.attribute.measurevalue ? d.attribute.measurevalue : "";

        // 创建完整的文本内容，优先显示名称
        const fullText = name || measureValue || "连接线";

        // 立即创建文本内容，确保有可见的标签
        const textElement = d3.select(this);
        textElement.selectAll("tspan").remove(); // 清除可能存在的旧tspan

        if (fullText && fullText.trim() !== "") {
          textElement.append("tspan").attr("x", 0).attr("dy", 0).text(fullText);
        } else {
          // 如果没有文本内容，显示占位符
          textElement.append("tspan").attr("x", 0).attr("dy", 0).text("—");
          console.warn(`更新时连接线缺少标签文本: ${d.id || "unknown"}`, d);
        }

        // 设置初始宽度估算值
        d.labelComputedWidth = Math.max(fullText.length * 6, 20);

        // 使用稳定的延迟策略获取BBox
        const tryGetBBox = (retryCount = 0) => {
          if (retryCount > 2) {
            console.warn(`更新时连接线标签BBox获取失败，使用估算值: ${d.id}`);
            return;
          }

          setTimeout(() => {
            try {
              if (this.isConnected && this.getBoundingClientRect().width > 0) {
                const bbox = this.getBBox();
                if (bbox.width > 0) {
                  d.labelComputedWidth = bbox.width;
                } else {
                  tryGetBBox(retryCount + 1);
                }
              } else {
                tryGetBBox(retryCount + 1);
              }
            } catch (e) {
              console.warn(`更新时连接线标签BBox获取错误 (重试 ${retryCount + 1}):`, d.id, e);
              tryGetBBox(retryCount + 1);
            }
          }, 100 + retryCount * 50);
        };

        tryGetBBox();
      });

    // 合并选择集，确保所有标签都有正确的文本内容
    const mergedLinkLabels = linkLabels.merge(enterSelection);

    // 对所有现有标签进行文本内容验证和修复
    mergedLinkLabels.each(function (d) {
      const textElement = d3.select(this);
      const existingText = textElement.select("tspan").text();

      if (!existingText || existingText.trim() === "") {
        const name = d.name || "";
        const measureValue = d.attribute && d.attribute.measurevalue ? d.attribute.measurevalue : "";
        const fullText = name || measureValue || "连接线";

        textElement.selectAll("tspan").remove();
        textElement.append("tspan").attr("x", 0).attr("dy", 0).text(fullText);
      }
    });

    this.renderManager.renderedLinkLabels = mergedLinkLabels;
  }

  /**
   * 为增量更新优化的连接线标签更新方法
   * @param {Array} links 连接线数组
   */
  updateLinkLabelsForIncremental(links) {
    if (!this.renderManager.renderedLinkLabels) {
      console.warn("renderedLinkLabels不存在，重新渲染连接线标签");
      this.renderLinkLabels(links, true);
      return;
    }

    const linkLabelFillColor = this.themeManager.getLabelColor("link", "fill");
    const linkLabelStrokeColor = this.themeManager.getLabelColor("link", "stroke");

    // 进行数据绑定
    const linkLabelsContainer = this.renderManager.container.select("g.link-labels");
    if (linkLabelsContainer.empty()) {
      console.warn("连接线标签容器不存在，重新渲染");
      this.renderLinkLabels(links, true);
      return;
    }

    const linkLabels = linkLabelsContainer.selectAll("text").data(links, this.getLinkId);

    // 温和地移除不再需要的标签，但不立即移除
    const exitSelection = linkLabels.exit();
    exitSelection.transition().duration(150).style("opacity", 0).remove();

    // 添加新标签，使用渐入动画
    const enterSelection = linkLabels
      .enter()
      .append("text")
      .attr("dy", -5)
      .style("font-size", "9px")
      .style("text-anchor", "middle")
      .style("dominant-baseline", "central")
      .style("pointer-events", "none")
      .style("fill", linkLabelFillColor)
      .style("font-weight", "bold")
      .style("text-shadow", `-1px -1px 1px ${linkLabelStrokeColor}, 1px -1px 1px ${linkLabelStrokeColor}, -1px 1px 1px ${linkLabelStrokeColor}, 1px 1px 1px ${linkLabelStrokeColor}`)
      .style("opacity", 0) // 初始透明度为0
      .each(function (d) {
        const name = d.name || "";
        const measureValue = d.attribute && d.attribute.measurevalue ? d.attribute.measurevalue : "";
        const fullText = name || measureValue || "连接线";

        // 立即创建文本内容
        const textElement = d3.select(this);
        if (fullText && fullText.trim() !== "") {
          textElement.append("tspan").attr("x", 0).attr("dy", 0).text(fullText);
        } else {
          textElement.append("tspan").attr("x", 0).attr("dy", 0).text("—");
        }

        // 设置初始宽度估算值
        d.labelComputedWidth = Math.max(fullText.length * 6, 20);
      });

    // 渐入动画
    enterSelection.transition().duration(200).style("opacity", 1);

    // 合并选择集，但不做额外的文本验证，避免闪烁
    const mergedLinkLabels = linkLabels.merge(enterSelection);

    // 只对新添加的标签进行位置更新
    if (enterSelection.size() > 0) {
      // 等待渐入动画完成后更新位置
      setTimeout(() => {
        this.updateLinkLabelsPosition(links);
      }, 250);
    }

    this.renderManager.renderedLinkLabels = mergedLinkLabels;
  }

  /**
   * 设置节点标签显示状态
   * @param {boolean} show 是否显示
   */
  setNodeLabelsVisibility(show) {
    if (this.renderManager.renderedLabels) {
      this.renderManager.renderedLabels.style("display", show ? "block" : "none");
    }
  }

  /**
   * 设置连接线标签显示状态
   * @param {boolean} show 是否显示
   */
  setLinkLabelsVisibility(show) {
    if (this.renderManager.renderedLinkLabels) {
      this.renderManager.renderedLinkLabels.style("display", show ? "block" : "none");

      // 在显示时验证标签内容
      if (show) {
        this.validateAndFixLinkLabels();
      }
    }
  }

  /**
   * 验证并修复连接线标签内容（增量更新优化版本）
   */
  validateAndFixLinkLabels() {
    if (!this.renderManager.renderedLinkLabels) return;

    // 增量更新时使用更温和的验证策略
    let fixedCount = 0;
    this.renderManager.renderedLinkLabels.each(function (d) {
      const textElement = d3.select(this);
      const tspans = textElement.selectAll("tspan");

      // 只在确实缺失文本内容时才修复，避免不必要的DOM操作
      if (tspans.empty() || !tspans.text().trim()) {
        const name = d.name || "";
        const measureValue = d.attribute && d.attribute.measurevalue ? d.attribute.measurevalue : "";
        const fullText = name || measureValue || "连接线";

        // 使用温和的更新方式，避免闪烁
        if (tspans.empty()) {
          textElement.append("tspan").attr("x", 0).attr("dy", 0).text(fullText);
        } else {
          tspans.text(fullText);
        }
        fixedCount++;
      }
    });

    if (fixedCount > 0) {
      console.log(`本次验证修复了 ${fixedCount} 个连接线标签`);
    }
  }

  /**
   * 应用高亮样式到标签
   * @param {Set} highlightedNodeIds 高亮节点ID集合
   * @param {Object} selectedNode 选中的节点
   */
  applyHighlightStyles(highlightedNodeIds, selectedNode) {
    if (this.renderManager.renderedLabels) {
      this.renderManager.renderedLabels
        .style("display", (d) => (highlightedNodeIds.has(d.id) ? "block" : "none"))
        .style("font-weight", (d) => (d.id === selectedNode.id ? "bold" : "normal"))
        .style("font-size", (d) => (d.id === selectedNode.id ? "12px" : "10px"));
    }
  }

  /**
   * 应用连接线高亮样式
   * @param {Object} selectedNode 选中的节点
   */
  applyLinkHighlightStyles(selectedNode) {
    if (this.renderManager.renderedLinkLabels) {
      const linkLabelHighlightColor = this.themeManager.getLabelColor("link", "highlight");
      const linkLabelFillColor = this.themeManager.getLabelColor("link", "fill");
      const linkLabelStrokeColor = this.themeManager.getLabelColor("link", "stroke");

      this.renderManager.renderedLinkLabels
        .style("opacity", (link) => {
          // 相关的连接线标签保持完全不透明，其他的稍微变透明但仍然可见
          if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {
            return 1;
          }
          return 0.5; // 非相关的连接线标签变为半透明，但仍然可见
        })
        .style("font-weight", (link) => {
          if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {
            return "bold";
          }
          return "normal";
        })
        .style("font-size", (link) => {
          if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {
            return "11px";
          }
          return "9px";
        })
        .style("fill", (link) => {
          if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {
            return linkLabelHighlightColor; // 高亮连接线标签
          }
          return linkLabelFillColor;
        })
        .style("text-shadow", (link) => {
          if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {
            return `-1px -1px 2px ${linkLabelStrokeColor}, 1px -1px 2px ${linkLabelStrokeColor}, -1px 1px 2px ${linkLabelStrokeColor}, 1px 1px 2px ${linkLabelStrokeColor}`;
          }
          return `-1px -1px 1px ${linkLabelStrokeColor}, 1px -1px 1px ${linkLabelStrokeColor}, -1px 1px 1px ${linkLabelStrokeColor}, 1px 1px 1px ${linkLabelStrokeColor}`;
        });
    }
  }

  /**
   * 应用连接线选择高亮样式
   * @param {Object} selectedLink 选中的连接线
   * @param {Object} sourceNode 源节点
   * @param {Object} targetNode 目标节点
   */
  applyLinkSelectionHighlightStyles(selectedLink, sourceNode, targetNode) {
    if (!this.renderManager.renderedLinkLabels) return;

    const linkLabelHighlightColor = this.themeManager.getLabelColor("link", "highlight");
    const linkLabelFillColor = this.themeManager.getLabelColor("link", "fill");

    this.renderManager.renderedLinkLabels.style("fill", (d) => {
      if (d === selectedLink) return linkLabelHighlightColor;
      return linkLabelFillColor;
    });
  }

  /**
   * 清除高亮样式
   * @param {boolean} showNodeLabels 是否显示节点标签
   * @param {boolean} showLinkLabels 是否显示连接线标签
   */
  clearHighlightStyles(showNodeLabels, showLinkLabels) {
    const nodeLabelFillColor = this.themeManager.getLabelColor("node", "fill");
    const nodeLabelStrokeColor = this.themeManager.getLabelColor("node", "stroke");
    const linkLabelFillColor = this.themeManager.getLabelColor("link", "fill");
    const linkLabelStrokeColor = this.themeManager.getLabelColor("link", "stroke");

    if (this.renderManager.renderedLabels) {
      this.renderManager.renderedLabels
        .style("display", showNodeLabels ? "block" : "none")
        .style("fill", nodeLabelFillColor)
        .style("text-shadow", `-1px -1px 1px ${nodeLabelStrokeColor}, 1px -1px 1px ${nodeLabelStrokeColor}, -1px 1px 1px ${nodeLabelStrokeColor}, 1px 1px 1px ${nodeLabelStrokeColor}`);
    }

    if (this.renderManager.renderedLinkLabels) {
      this.renderManager.renderedLinkLabels
        .style("display", showLinkLabels ? "block" : "none")
        .style("opacity", 1) // 恢复透明度为完全不透明
        .style("font-weight", "normal") // 恢复字体粗细
        .style("font-size", "9px") // 恢复字体大小
        .style("fill", linkLabelFillColor)
        .style("text-shadow", `-1px -1px 1px ${linkLabelStrokeColor}, 1px -1px 1px ${linkLabelStrokeColor}, -1px 1px 1px ${linkLabelStrokeColor}, 1px 1px 1px ${linkLabelStrokeColor}`);

      // 清除高亮样式时也验证标签内容
      if (showLinkLabels) {
        this.validateAndFixLinkLabels();
      }
    }
  }

  /**
   * 获取连接线ID（复用函数）
   */
  getLinkId(link) {
    const sourceId = link.source && typeof link.source === "object" && link.source.id !== undefined ? link.source.id : link.source;
    const targetId = link.target && typeof link.target === "object" && link.target.id !== undefined ? link.target.id : link.target;
    return link.id || `${sourceId}-${targetId}`;
  }

  /**
   * 设置节点标签的悬停高亮状态
   * @param {String|Number} nodeId 节点ID
   * @param {boolean} isHighlighted 是否高亮
   */
  setNodeLabelHoverHighlight(nodeId, isHighlighted) {
    if (!this.renderManager.renderedLabels) {
      return;
    }

    const nodeLabelHighlightColor = this.themeManager.getLabelColor("node", "highlight");
    const nodeLabelFillColor = this.themeManager.getLabelColor("node", "fill");

    this.renderManager.renderedLabels.filter((d) => d.id === nodeId).style("fill", isHighlighted ? nodeLabelHighlightColor : nodeLabelFillColor); // Cyan for highlight, white for normal
  }

  /**
   * 获取连接线标签高亮颜色
   */
  getLinkLabelHighlightColor(d, selectedNode) {
    if (d.source && d.target && selectedNode && (d.source.id === selectedNode.id || d.target.id === selectedNode.id)) {
      return this.themeManager.getLabelColor("link", "highlight"); // 高亮连接线标签
    }
    return this.themeManager.getLabelColor("link", "fill");
  }

  /**
   * 获取连接线标签阴影
   */
  getLinkLabelTextShadow(d, selectedNode) {
    const strokeColor = this.themeManager.getLabelColor("link", "stroke");
    if (d.source && d.target && selectedNode && (d.source.id === selectedNode.id || d.target.id === selectedNode.id)) {
      return `-1px -1px 2px ${strokeColor}, 1px -1px 2px ${strokeColor}, -1px 1px 2px ${strokeColor}, 1px 1px 2px ${strokeColor}`;
    }
    return `-1px -1px 1px ${strokeColor}, 1px -1px 1px ${strokeColor}, -1px 1px 1px ${strokeColor}, 1px 1px 1px ${strokeColor}`;
  }
}
