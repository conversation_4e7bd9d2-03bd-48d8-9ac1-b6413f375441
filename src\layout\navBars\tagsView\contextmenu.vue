<template>
  <div>
    <transition name="el-zoom-in-top">
      <ul
        class="el-dropdown-menu el-popper el-dropdown-menu--medium custom-contextmenu"
        :style="`top: ${dropdowns.y}px;left: ${dropdowns.x}px;`"
        x-placement="bottom-end"
        id="contextmenu"
        v-show="isShow"
      >
        <li
          class="el-dropdown-menu__item"
          :class="{
            'is-disabled': item.disabled,
            'el-dropdown-menu__item--divided': item.divided
          }"
          v-for="item in dropdownList"
          :key="item.id"
          @click="onCurrentContextmenuClick(item.id)"
        >
          <template v-if="item.show">
            <i :class="item.icon"></i>
            <span>{{ item.text }}</span>
          </template>
        </li>
        <div
          x-arrow
          class="popper__arrow"
          :style="{ left: `${arrowLeft}px` }"
        ></div>
      </ul>
    </transition>
  </div>
</template>

<script>
import { Session } from "@/utils/storage";
import { isEmpty, isEqual } from "lodash-es";
export default {
  name: "layoutTagsViewContextmenu",
  props: {
    dropdown: {
      type: Object
    },
    tagsViewList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isShow: false,
      path: {},
      arrowLeft: 5
    };
  },
  // 监听下拉菜单位置
  watch: {
    dropdown: {
      handler({ x }) {
        if (x + 99 > document.documentElement.clientWidth) this.arrowLeft = 99 - (document.documentElement.clientWidth - x);
        else this.arrowLeft = 10;
      },
      deep: true
    }
  },
  computed: {
    dropdownList() {
      return [
        {
          id: "refresh",
          icon: "el-icon-refresh",
          text: "刷新",
          disabled: false,
          divided: false,
          show: true
        },
        {
          id: "closeCurrent",
          icon: "el-icon-remove-outline",
          text: "关闭",
          disabled: false,
          divided: false,
          show: true
        },
        {
          id: "closeLeft",
          icon: "el-icon-d-arrow-left",
          text: "关闭左侧",
          divided: false,
          disabled: false,
          show: true
        },
        {
          id: "closeRight",
          icon: "el-icon-d-arrow-right",
          text: "关闭右侧",
          divided: false,
          disabled: false,
          show: true
        },
        {
          id: "closeOther",
          icon: "el-icon-circle-close",
          text: "关闭其它",
          disabled: false,
          divided: false,
          show: true
        },
        {
          id: "closeAll",
          icon: "el-icon-folder-delete",
          text: "关闭全部",
          disabled: false,
          divided: false,
          show: true
        }
      ];
    },
    dropdowns() {
      // 99 为 `Dropdown 下拉菜单` 的宽度
      if (this.dropdown.x + 99 > document.documentElement.clientWidth) {
        return {
          x: document.documentElement.clientWidth - 99 - 5,
          y: this.dropdown.y
        };
      } else {
        return this.dropdown;
      }
    }
  },
  mounted() {
    // 监听页面监听进行右键菜单的关闭
    document.body.addEventListener("click", this.closeContextmenu);
  },
  destroyed() {
    // 页面卸载时，移除右键菜单监听事件
    document.body.removeEventListener("click", this.closeContextmenu);
  },
  methods: {
    // 当前项菜单点击
    onCurrentContextmenuClick(id) {
      this.$emit("currentContextmenuClick", { id, path: this.path });
    },
    // 打开右键菜单：判断是否固定，固定则不显示关闭按钮
    openContextmenu(tag) {
      this.closeContextmenu();
      const topPath = "/home";
      const tagsViewList = Session.get("tagsViewList");
      if (tag.path === topPath || tag?.meta?.isAffix) {
        // 右键菜单为顶级菜单或拥有 fixedTag 属性，只显示刷新
        this.showMenus(false);
        this.dropdownList[0].show = true;
      } else if (this.$route.path !== tag.path && this.$route.name !== tag.name) {
        // 右键菜单不匹配当前路由，隐藏刷新
        this.dropdownList[0].show = false;
        this.showMenuModel(tag.path, tag.query);
      } else if (tagsViewList.length === 2 && this.$route.path !== tag.path) {
        this.showMenus(true);
        // 只有两个标签时不显示关闭其他标签页
        this.dropdownList[4].show = false;
      } else if (this.$route.path === tag.path) {
        // 右键当前激活的菜单
        this.showMenuModel(tag.path, tag.query, true);
      }
      this.path = tag.path;
      setTimeout(() => {
        this.isShow = true;
      }, 80);
    },
    // 关闭右键菜单
    closeContextmenu() {
      this.isShow = false;
    },
    showMenuModel(currentPath, query = {}, refresh = false) {
      const allRoute = Session.get("tagsViewList");
      const routeLength = allRoute.length;
      let currentIndex = -1;
      if (isEmpty(query)) {
        currentIndex = allRoute.findIndex((v) => v.path === currentPath);
      } else {
        currentIndex = allRoute.findIndex((v) => isEqual(v.query, query));
      }
      function fixedTagDisabled() {
        if (allRoute[currentIndex]?.meta?.isAffix) {
          Array.of(1, 2, 3, 4, 5).forEach((v) => {
            this.dropdownList[v].disabled = true;
          });
        }
      }

      this.showMenus(true);

      if (refresh) {
        this.dropdownList[0].show = true;
      }

      /**
       * currentIndex为1时，左侧的菜单顶级菜单，则不显示关闭左侧标签页
       * 如果currentIndex等于routeLength-1，右侧没有菜单，则不显示关闭右侧标签页
       */
      if (currentIndex === 1 && routeLength !== 2) {
        // 左侧的菜单是顶级菜单，右侧存在别的菜单
        this.dropdownList[2].show = false;
        Array.of(1, 3, 4, 5).forEach((v) => {
          this.dropdownList[v].disabled = false;
        });
        this.dropdownList[2].disabled = true;
        fixedTagDisabled();
      } else if (currentIndex === 1 && routeLength === 2) {
        this.disabledMenus(false);
        // 左侧的菜单是顶级菜单，右侧不存在别的菜单
        Array.of(2, 3, 4).forEach((v) => {
          this.dropdownList[v].show = false;
          this.dropdownList[v].disabled = true;
        });
        fixedTagDisabled();
      } else if (routeLength - 1 === currentIndex && currentIndex !== 0) {
        // 当前路由是所有路由中的最后一个
        this.dropdownList[3].show = false;
        Array.of(1, 2, 4, 5).forEach((v) => {
          this.dropdownList[v].disabled = false;
        });
        this.dropdownList[3].disabled = true;
        if (allRoute[currentIndex - 1]?.meta?.isAffix) {
          this.dropdownList[2].show = false;
          this.dropdownList[2].disabled = true;
        }
        fixedTagDisabled();
      } else if (currentIndex === 0) {
        // 当前路由为顶级菜单
        this.disabledMenus(true);
      } else {
        this.disabledMenus(false, allRoute[currentIndex - 1]?.meta?.isAffix);
        fixedTagDisabled();
      }
    },
    showMenus(value) {
      Array.of(1, 2, 3, 4, 5).forEach((index) => {
        this.dropdownList[index].show = value;
      });
    },
    disabledMenus(value, fixedTag = false) {
      Array.of(1, 2, 3, 4, 5).forEach((index) => {
        this.dropdownList[index].disabled = value;
      });
      if (fixedTag) {
        this.dropdownList[2].show = false;
        this.dropdownList[2].disabled = true;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.custom-contextmenu {
  transform-origin: center top;
  z-index: 2190;
  position: fixed;
  .el-dropdown-menu__item {
    font-size: 12px !important;
    white-space: nowrap;
    i {
      font-size: 12px !important;
    }
  }
}
</style>
