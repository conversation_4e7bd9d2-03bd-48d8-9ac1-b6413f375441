const prompts = require("prompts");
const shell = require("shelljs");
const path = require("path");
const { NodeSSH } = require("node-ssh");
const scpClient = require("scp2");
const fs = require("fs");
const ssh = new NodeSSH();

function getFormattedDate() {
  const date = new Date();
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const hour = date.getHours().toString().padStart(2, "0");
  const minute = date.getMinutes().toString().padStart(2, "0");
  return `${year}${month}${day}${hour}${minute}`;
}

async function main() {
  const defaultConfPath = "./deploy-config.json";
  const packageJsonFile = "./package.json";

  try {
    import("chalk").then(async (chalkModule) => {
      const chalk = chalkModule.default;

      // 同步读取文件
      const rawData = fs.readFileSync(defaultConfPath, "utf8");
      const packageData = fs.readFileSync(packageJsonFile, "utf8");
      // 将读取的内容转换为JSON对象
      const defaultConf = JSON.parse(rawData);
      const conf = JSON.parse(packageData);

      console.log(chalk.green("欢迎使用打包脚本"));
      console.log(chalk.red.bold("1、目前此脚本只适用于服务器部署,不适用于Docker环境部署"));
      console.log(chalk.red.bold(`2、若使用脚本添加nginx代理配置请手动先在服务器当前nginx主配置相应server节点中加入include ${defaultConf.defaultNginxConfPath}; 引入配置文件，否则不生效！！！`));

      const response = await prompts([
        {
          type: "text",
          name: "folder",
          message: chalk.cyan("请输入本地打包后的文件夹路径【默认dist】: ")
        },
        {
          type: "select",
          name: "version",
          message: chalk.cyan("发布版本【开发中的项目建议发布测试版，线上版本使用稳定版】: "),
          choices: [
            { title: "测试版", value: "test" },
            { title: "稳定版", value: "stable" }
          ],
          initial: 0
        },
        {
          type: "text",
          name: "server",
          message: chalk.cyan("请输入服务器地址【默认19服务器】:")
        },
        {
          type: "text",
          name: "username",
          message: chalk.cyan("请输入服务器用户名【默认root】:")
        },
        {
          type: "password",
          name: "password",
          message: chalk.cyan("请输入服务器密码:"),
          validate: (value) => (value ? true : "请输入密码."), // 确保输入非空
          mask: "*"
        },
        {
          type: "text",
          name: "target",
          message: chalk.cyan(`请输入上传的目标文件夹（项目名）【统一资源地址:${defaultConf.defaultWebResourcesPath}】:`),
          default: conf.name,
          validate: (value) => (value ? true : "目标文件夹（项目名）不能为空") // 确保输入非空
        },
        {
          type: "confirm",
          name: "nginx",
          message: chalk.cyan(`是否需要添加Nginx配置【配置文件路径:${defaultConf.defaultNginxConfPath}】?`)
        }
      ]);

      deploy(response, defaultConf, chalk);
    });
  } catch (error) {
    console.error("读取默认配置文件失败:", error);
  }
}

async function deploy(config, defaultConf, chalk) {
  if (!config.folder) {
    config.folder = "dist";
  }
  if (!config.server) {
    config.server = "10.100.1.19";
  }
  if (!config.username) {
    config.username = "root";
  }
  // 执行打包命令
  console.log("正在本地执行打包...");
  const child = shell.exec("npm run build", { async: true });

  child.stdout.on("data", function (data) {
    // 这里假设打包脚本会输出进度信息，例如 "10% building modules"
    const match = data.match(/(\d+)%/);
    if (match) {
      console.log(`打包进度: ${match[1]}%`);
    }
  });

  child.on("close", function (code) {
    if (code === 0) {
      console.log(chalk.green("✓ 前端打包完成"));
      uploadFiles(config, defaultConf, chalk);
    } else {
      console.error("打包失败。");
    }
  });
}

async function uploadFiles(config, defaultConf, chalk) {
  // 上传文件到服务器
  await ssh.connect({
    host: config.server,
    username: config.username,
    password: config.password
  });
  console.log(chalk.green("✓ 连接到服务器成功"));

  let remoteTargetPath = `${defaultConf.defaultWebResourcesPath}/${config.target}`;
  if (config.version === "test") {
    remoteTargetPath = `${defaultConf.defaultWebResourcesPath}/${config.target}-${config.version}`;
  }

  // 检查远程路径是否存在
  const checkPath = await ssh.execCommand(`if [ -d "${remoteTargetPath}" ]; then echo "exists"; fi`);
  if (checkPath.stdout.trim() === "exists") {
    // 发布正式版会备份前一个版本的文件夹
    if (config.version === "stable") {
      // 确保备份目录存在
      const mkdirCommand = `mkdir -p ${defaultConf.defaultBackUpPath}`;
      const mkdirResult = await ssh.execCommand(mkdirCommand);
      if (mkdirResult.stderr) {
        console.log(chalk.red.bold("× 创建备份目录失败:", tarResult.stderr));
        ssh.dispose();
        return;
      }
      // 获取当前日期时间字符串
      const dateTime = getFormattedDate();
      // 获取目标文件夹的父目录及本目录名
      const parentDir = remoteTargetPath.substring(0, remoteTargetPath.lastIndexOf("/"));
      const dirName = remoteTargetPath.substring(remoteTargetPath.lastIndexOf("/") + 1);
      // 构建tar命令来备份指定文件夹
      const backupFilePath = `${defaultConf.defaultBackUpPath}/${config.target}-${dateTime}.tar.gz`;
      const tarCommand = `cd ${parentDir} && tar -czf ${backupFilePath} ${dirName}`;
      const tarResult = await ssh.execCommand(tarCommand);
      if (tarResult.stderr) {
        console.log(chalk.red.bold("× 备份失败:", tarResult.stderr));
        ssh.dispose();
        return;
      }
      console.log(chalk.green(`✓ ${parentDir}/${dirName}备份成功`));

      // 删除多余的备份文件
      const listBackupsCommand = `ls -t ${defaultConf.defaultBackUpPath}/${config.target}-*.tar.gz`;
      const listBackupsResult = await ssh.execCommand(listBackupsCommand);
      const maxBackups = defaultConf.maxBackups;
      if (listBackupsResult.stdout) {
        const backups = listBackupsResult.stdout.split("\n");
        if (backups.length > maxBackups) {
          const backupsToDelete = backups.slice(maxBackups);
          for (const backup of backupsToDelete) {
            const deleteCommand = `rm -f ${backup}`;
            await ssh.execCommand(deleteCommand);
            console.log(chalk.green(`✓ 已删除旧备份文件: ${backup}`));
          }
        }
      }
    }

    await ssh.execCommand(`rm -rf ${remoteTargetPath}`);
    console.log(chalk.green("✓ 原文件夹删除成功"));
  }

  // 读取构建后的文件
  const buildPath = path.resolve(__dirname, config.folder);
  scpClient.scp(
    buildPath,
    {
      host: config.server,
      username: config.username,
      password: config.password,
      path: remoteTargetPath
    },
    function (err) {
      if (err) {
        console.log(chalk.red.bold("× 文件上传失败:", err));
      } else {
        console.log(chalk.green("✓ 文件上传成功"));
        ssh.dispose();
        if (config.nginx) {
          modifyNginx(config, defaultConf, chalk);
        }
      }
    }
  );
}

async function modifyNginx(config, defaultConf, chalk) {
  await ssh.connect({
    host: config.server,
    username: config.username,
    password: config.password
  });

  // console.log("正在更新Nginx配置...");
  const nginxConfPath = defaultConf.defaultNginxConfPath; // Nginx 配置文件路径
  const proxyPort = defaultConf.nginxProxyPort; // Nginx默认端口
  const entry = defaultConf.defaultEntry; // 默认页面
  const locationPath = config.version === "stable" ? `/${config.target}/` : `/${config.target}-${config.version}/`;

  // 读取现有的 Nginx 配置
  const { stdout: originalConf } = await ssh.execCommand(`cat ${nginxConfPath}`);

  // 检查是否已经包含指定的 location
  const locationRegex = new RegExp(`location\\s+${locationPath.replace("/", "\\/")}\\s+{`, "g");
  if (locationRegex.test(originalConf)) {
    console.log(`Nginx配置中已存在location ${locationPath}，无需重复添加。`);
    console.log(chalk.green(`✓ 前端部署成功，访问地址: https://${config.server}:${proxyPort}${locationPath}${entry}`));
    ssh.dispose();
    return;
  }

  // 如果不存在，则添加新的 location 配置
  const locationBlock = `
    location ${locationPath} {
		root ${defaultConf.defaultWebResourcesPath}/;
		index ${entry} index.htm;
		add_header 'Access-Control-Allow-Origin' '*' always;
	}
  `;

  const modifiedConf = originalConf + locationBlock;

  // 写回修改后的配置
  await ssh.execCommand(`echo "${modifiedConf.replace(/"/g, '\\"')}" > ${nginxConfPath}`);
  await ssh.execCommand("nginx -s reload"); // 重新加载 Nginx 配置

  console.log(chalk.green("✓ Nginx配置已更新"));
  console.log(chalk.green(`✓ 前端部署成功，访问地址: https://${config.server}:${proxyPort}${locationPath}${entry}`));
  ssh.dispose();
}

main();
