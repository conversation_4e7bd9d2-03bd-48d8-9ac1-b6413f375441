import config from "../../package.json";

// 名称前自动添加前缀
const autoAddPrefix = (key) => {
  return `${config.name}_${key}`;
};

// 判断是否支持 Storage
export const isSupportStorage = () => {
  return typeof Storage !== "undefined" ? true : false;
};

class Cache {
  constructor(type) {
    this.storage = window[type];
  }
  // 设置永久缓存
  set(key, val) {
    if (val === "" || val === null || val === undefined) {
      val = null;
    }
    this.storage.setItem(autoAddPrefix(key), JSON.stringify(val));
  }
  // 获取永久缓存
  get(key) {
    key = autoAddPrefix(key);
    // key 不存在判断
    if (!this.storage.getItem(key) || this.storage.getItem(key) === "null") {
      return null;
    }
    const json = this.storage.getItem(key);
    return JSON.parse(json);
  }
  // 移除永久缓存
  remove(key) {
    this.storage.removeItem(autoAddPrefix(key));
  }
  // 移除全部永久缓存
  clear() {
    this.storage.clear();
  }
}

const Local = new Cache("localStorage");
const Session = new Cache("sessionStorage");
export { Local, Session };
