<template>
  <div class="login">
    <div class="logo">
      <svg-icon
        style="width: 230px; height: 70px"
        icon-name="logo-colour"
      ></svg-icon>
    </div>
    <div class="title">{{ getThemeConfig.globalTitle }}</div>
    <div class="form__wrapper">
      <el-form
        :rules="rules"
        ref="loginForm"
        :model="loginForm"
        @keyup.enter.native="submitLogin()"
        class="login-container"
      >
        <div class="login-title">登 录</div>
        <el-form-item prop="username">
          <el-input
            type="text"
            clearable
            prefix-icon="el-icon-user"
            v-model="loginForm.username"
            placeholder="请输入用户名"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            type="password"
            clearable
            prefix-icon="el-icon-lock"
            show-password
            v-model="loginForm.password"
            placeholder="请输入密码"
          />
        </el-form-item>
        <el-form-item style="margin: 20px 0px 0">
          <el-button
            type="primary"
            style="width: 100%"
            native-type="submit"
            @click.prevent="submitLogin"
            >登 录</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { Local } from "@/utils/storage";
export default {
  name: "Login",
  data() {
    return {
      loginForm: {
        username: "admin",
        password: "123456"
      },
      rules: {
        username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }]
      }
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    }
  },
  mounted() {},
  methods: {
    submitLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          // eslint-disable-next-line no-unused-vars
          const { password, ...userInfo } = this.loginForm;
          const data = {
            ...userInfo
          };
          const token = Math.random().toString(36).substring(0);
          Local.set("token", token);
          this.$store.commit("userInfos/setToken", token);
          this.$store.commit("keepAliveNames/setKeepAliveName", []);
          // 存储用户信息到vuex
          this.$store.dispatch("userInfos/setUserInfos", data);
          this.$router.push("/");
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
$primary: #409eff;
$gray: #2d343b;
.login {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: url("@/assets/images/layout/login-bg.jpg") no-repeat;
  background-size: cover;
  position: relative;
  .logo {
    position: absolute;
    top: 10px;
    left: 10px;
  }
  .title {
    font-size: 26px;
    font-weight: 700;
    letter-spacing: 15px;
    padding-bottom: 20px;
    background: linear-gradient(to right, #00d9f1, #007bf5);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
}
.form__wrapper {
  position: relative;
  width: 23vw;
  min-width: 450px;
  padding: 0.2rem;
  border-radius: 5px;
  overflow: hidden;
  .login-container {
    width: 100%;
    padding: 60px 50px;
    border-radius: 3px;
    background-color: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px); //毛玻璃属性
    position: relative;
    .login-title {
      font-size: 20px;
      color: var(--nari-c-white);
      margin-bottom: 20px;
      text-align: center;
      &::before,
      &::after {
        content: "- - - - - -";
        font-size: 14px;
        padding: 0 8px;
      }
    }
    .login-remember {
      text-align: left;
      margin: 0px 0px 15px 0px;
    }
    ::v-deep .el-form-item {
      .el-input {
        .el-input__inner {
          color: var(--nari-c-white);
          border-color: $gray;
          background: $gray;
          &::placeholder {
            color: var(--nari-c-gray);
          }
        }
        .el-input__prefix {
          .el-input__icon {
            color: $primary;
          }
        }
        .el-input__suffix {
          .el-input__suffix-inner {
            .el-input__icon {
              color: var(--nari-c-gray-light-2);
            }
          }
        }
        .el-input-group__append {
          padding: 0 5px;
        }
      }
      .el-checkbox__input {
        .el-checkbox__inner {
          background: transparent;
        }
        &.is-checked,
        &.is-indeterminate {
          .el-checkbox__inner {
            background: $primary;
            border-color: $primary;
          }
        }
        &.is-checked + .el-checkbox__label {
          color: $primary;
        }
      }
      .el-button--primary {
        background: $primary;
        border-color: $primary;
        &:focus,
        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
}
</style>
