import * as d3 from "d3";
import { GeometryUtils } from "../utils/geometryUtils.js";
import { ThemeManager } from "../core/ThemeManager.js";

/**
 * 连接线渲染器类
 * 负责连接线的绘制和更新
 */
export class LinkRenderer {
  constructor(renderManager, dataProcessor) {
    this.renderManager = renderManager;
    this.dataProcessor = dataProcessor;
    this.themeManager = new ThemeManager();
    this.links = null;
    this.geometryUtils = new GeometryUtils();
  }

  /**
   * 设置主题
   * @param {string} theme 主题名称 ('light' | 'dark')
   */
  setTheme(theme) {
    this.themeManager.setTheme(theme);
  }

  /**
   * 渲染连接线
   * @param {Array} links 连接线数组
   * @param {Map} nodeMap 节点映射
   * @returns {d3.Selection} 连接线选择集
   */
  render(links, nodeMap) {
    this.links = links;
    const linksContainer = this.renderManager.createGroup("links");

    const renderedLinks = linksContainer
      .selectAll("path")
      .data(links)
      .enter()
      .append("path")
      .attr("stroke", (d) => this.getLinkStroke(d))
      .attr("stroke-opacity", 0.8)
      .attr("stroke-width", 1.5)
      .attr("fill", "none")
      .attr("stroke-dasharray", (d) => this.getLinkDashArray(d))
      .classed("link-flow-forward", (d) => d.label && d.label.linestyle === "dash-flow-forward")
      .classed("link-blink", (d) => d.label && d.label.linestyle === "dash-blink")
      .attr("d", (d, i) => this.calculateLinkPath(d, i, nodeMap));

    this.renderManager.renderedLinks = renderedLinks;
    return renderedLinks;
  }

  /**
   * 更新连接线
   * @param {Array} links 连接线数组
   * @param {Map} nodeMap 节点映射
   * @returns {d3.Selection} 连接线选择集
   */
  update(links, nodeMap) {
    this.links = links;
    const linksContainer = this.renderManager.container.select("g.links");

    // 数据绑定
    const renderedLinks = linksContainer.selectAll("path").data(links, this.getLinkId);

    // 移除不再需要的连接线
    renderedLinks.exit().remove();

    // 添加新连接线
    const enterSelection = renderedLinks.enter().append("path").attr("fill", "none");

    // 合并并应用属性
    const mergedLinks = renderedLinks
      .merge(enterSelection)
      .attr("stroke", (d) => this.getLinkStroke(d))
      .attr("stroke-opacity", 0.8)
      .attr("stroke-width", 1.5)
      .attr("stroke-dasharray", (d) => this.getLinkDashArray(d))
      .classed("link-flow-forward", (d) => d.label && d.label.linestyle === "dash-flow-forward")
      .classed("link-blink", (d) => d.label && d.label.linestyle === "dash-blink")
      .attr("d", (d, i) => this.calculateLinkPath(d, i, nodeMap));

    this.renderManager.renderedLinks = mergedLinks;
    return mergedLinks;
  }

  /**
   * 计算连接线路径
   * @param {Object} link 连接线数据
   * @param {number} index 索引
   * @param {Map} nodeMap 节点映射
   * @returns {string} SVG路径字符串
   */
  calculateLinkPath(link, index, nodeMap) {
    const sourceNode = nodeMap.get(String(typeof link.source === "object" ? link.source.id : link.source));
    const targetNode = nodeMap.get(String(typeof link.target === "object" ? link.target.id : link.target));

    if (!sourceNode || !targetNode) {
      console.warn(`无法找到连接线的源节点或目标节点:`, link);
      return "M0,0 L0,0";
    }

    // 验证节点坐标
    if (!this.validateNodeCoordinates(sourceNode) || !this.validateNodeCoordinates(targetNode)) {
      console.warn(`节点坐标无效:`, { sourceNode, targetNode });
      return "M0,0 L0,0";
    }

    const sourceEdge = this.geometryUtils.getEdgeCoordinates(sourceNode, targetNode);
    const targetEdge = this.geometryUtils.getEdgeCoordinates(targetNode, sourceNode);

    // 检查两个节点间的连接线数量
    const sourceId = typeof link.source === "object" ? link.source.id : link.source;
    const targetId = typeof link.target === "object" ? link.target.id : link.target;

    const parallelLinks = this.links.filter((l) => {
      const linkSourceId = typeof l.source === "object" ? l.source.id : l.source;
      const linkTargetId = typeof l.target === "object" ? l.target.id : l.target;
      return (linkSourceId === sourceId && linkTargetId === targetId) || (linkSourceId === targetId && linkTargetId === sourceId);
    });

    const connectionCount = parallelLinks.length;

    if (connectionCount === 1) {
      // 单条连接线，绘制直线
      link._controlPointX = (sourceNode.x + targetNode.x) / 2;
      link._controlPointY = (sourceNode.y + targetNode.y) / 2;
      return `M${sourceEdge.x},${sourceEdge.y} L${targetEdge.x},${targetEdge.y}`;
    }

    // 多条连接线，绘制曲线
    return this.calculateCurvePath(link, sourceNode, targetNode, sourceEdge, targetEdge, parallelLinks, index);
  }

  /**
   * 计算曲线路径
   */
  calculateCurvePath(link, sourceNode, targetNode, sourceEdge, targetEdge, parallelLinks, index) {
    const dx = targetNode.x - sourceNode.x;
    const dy = targetNode.y - sourceNode.y;
    const dr = Math.sqrt(dx * dx + dy * dy);

    let linkIndexInParallelSet = -1;
    if (link.name) {
      linkIndexInParallelSet = parallelLinks.findIndex((pl) => pl.name === link.name);
    }
    if (linkIndexInParallelSet === -1) {
      linkIndexInParallelSet = parallelLinks.findIndex((pl) => this.getLinkId(pl) === this.getLinkId(link));
      if (linkIndexInParallelSet === -1) {
        linkIndexInParallelSet = index % parallelLinks.length;
      }
    }

    const curveMagnitude = 15 + dr * 0.05;
    const direction = linkIndexInParallelSet % 2 === 0 ? 1 : -1;
    const offset = curveMagnitude * direction;

    let controlPointX = (sourceNode.x + targetNode.x) / 2;
    let controlPointY = (sourceNode.y + targetNode.y) / 2;

    if (dr !== 0) {
      const normX = -dy / dr;
      const normY = dx / dr;
      controlPointX += offset * normX;
      controlPointY += offset * normY;
    } else {
      controlPointX += offset;
      controlPointY += offset;
    }

    link._controlPointX = controlPointX;
    link._controlPointY = controlPointY;

    return `M${sourceEdge.x},${sourceEdge.y} Q${controlPointX},${controlPointY} ${targetEdge.x},${targetEdge.y}`;
  }

  /**
   * 验证节点坐标
   */
  validateNodeCoordinates(node) {
    return node && Number.isFinite(node.x) && Number.isFinite(node.y) && !isNaN(node.x) && !isNaN(node.y) && node.x !== 0 && node.y !== 0;
  }

  /**
   * 获取连接线颜色
   * @param {Object} link 连接线数据
   * @returns {string} 颜色值
   */
  getLinkStroke(link) {
    return this.themeManager.getLinkStrokeColor(link);
  }

  /**
   * 获取连接线虚线样式
   * @param {Object} link 连接线数据
   * @returns {string|null} 虚线样式
   */
  getLinkDashArray(link) {
    if (link.label && link.label.linestyle === "dashed_line") {
      return "5,5";
    }
    return null;
  }

  /**
   * 获取连接线唯一ID
   * @param {Object} link 连接线数据
   * @returns {string} 唯一ID
   */
  getLinkId(link) {
    const sourceId = link.source && typeof link.source === "object" && link.source.id !== undefined ? link.source.id : link.source;
    const targetId = link.target && typeof link.target === "object" && link.target.id !== undefined ? link.target.id : link.target;
    return link.id || `${sourceId}-${targetId}`;
  }

  /**
   * 应用连接线高亮样式
   * @param {Object} selectedNode 选中的节点
   */
  applyHighlightStyles(selectedNode) {
    if (!this.renderManager.renderedLinks) return;

    // 使用ThemeManager方法获取连接线高亮颜色
    const relatedLinkColor = this.themeManager.getLinkHighlightColor("related");
    const normalOpacity = this.themeManager.getOpacity("normal");
    const dimmedOpacity = this.themeManager.getOpacity("dimmed");

    this.renderManager.renderedLinks
      .attr("stroke", (link) => {
        if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {
          return relatedLinkColor;
        }
        return this.getLinkStroke(link);
      })
      .attr("stroke-opacity", (link) => {
        if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {
          return normalOpacity;
        }
        return dimmedOpacity;
      })
      .attr("stroke-width", (link) => {
        if (link.source.id === selectedNode.id || link.target.id === selectedNode.id) {
          return 3;
        }
        return 1.5;
      });
  }

  /**
   * 应用连接线选择高亮样式
   * @param {Object} selectedLink 选中的连接线
   * @param {Object} sourceNode 源节点
   * @param {Object} targetNode 目标节点
   */
  applyLinkHighlightStyles(selectedLink, sourceNode, targetNode) {
    if (!this.renderManager.renderedLinks) return;

    // 使用ThemeManager方法获取连接线高亮颜色
    const selectedLinkColor = this.themeManager.getLinkHighlightColor("selected");
    const relatedLinkColor = this.themeManager.getLinkHighlightColor("related");
    const defaultColor = this.themeManager.getLinkDefaultColor();
    const hiddenOpacity = this.themeManager.getOpacity("hidden");

    this.renderManager.renderedLinks
      .attr("stroke", (d) => {
        if (d === selectedLink) return selectedLinkColor; // 选中的连接线
        if (d.source.id === sourceNode.id && d.target.id === targetNode.id) return relatedLinkColor; // 同一对节点之间的其他连接线
        return defaultColor; // 其他连接线
      })
      .attr("stroke-opacity", (d) => {
        if (d === selectedLink) return 1;
        if (d.source.id === sourceNode.id && d.target.id === targetNode.id) return 0.8;
        return hiddenOpacity; // 其他连接线几乎透明
      })
      .attr("stroke-width", (d) => {
        if (d === selectedLink) return 4; // 选中的连接线更粗
        if (d.source.id === sourceNode.id && d.target.id === targetNode.id) return 2.5;
        return 1;
      });
  }

  /**
   * 清除高亮样式
   */
  clearHighlightStyles() {
    if (!this.renderManager.renderedLinks) return;

    const normalOpacity = this.themeManager.getOpacity("normal");

    this.renderManager.renderedLinks
      .attr("stroke", (d) => this.getLinkStroke(d))
      .attr("stroke-opacity", normalOpacity)
      .attr("stroke-width", 1.5)
      .attr("stroke-dasharray", (d) => this.getLinkDashArray(d));
  }

  /**
   * 应用类别筛选样式
   * @param {string} selectedCategory 选中的类别
   * @param {Array} nodes 节点数组
   */
  applyCategoryFilter(selectedCategory, nodes) {
    if (!this.renderManager.renderedLinks) return;

    this.renderManager.renderedLinks.each(function (d) {
      const link = d3.select(this);
      const sourceMatch = d.source.category && String(d.source.category) === String(selectedCategory);
      const targetMatch = d.target.category && String(d.target.category) === String(selectedCategory);

      // 处理特殊状态节点
      let sourceSpecialMatch = false;
      let targetSpecialMatch = false;

      if (selectedCategory === "loss_pressure") {
        sourceSpecialMatch = d.source.attribute && d.source.attribute.stationstyle === "loss_pressure";
        targetSpecialMatch = d.target.attribute && d.target.attribute.stationstyle === "loss_pressure";
      } else if (selectedCategory === "loss_pressure_half") {
        sourceSpecialMatch = d.source.attribute && d.source.attribute.stationstyle === "loss_pressure_half";
        targetSpecialMatch = d.target.attribute && d.target.attribute.stationstyle === "loss_pressure_half";
      } else if (selectedCategory === "disconnection") {
        sourceSpecialMatch = d.source.attribute && d.source.attribute.stationstyle === "disconnection";
        targetSpecialMatch = d.target.attribute && d.target.attribute.stationstyle === "disconnection";
      } else if (selectedCategory === "series_power_supply") {
        sourceSpecialMatch = d.source.attribute && d.source.attribute.stationstyle === "series_power_supply";
        targetSpecialMatch = d.target.attribute && d.target.attribute.stationstyle === "series_power_supply";
      }

      // 如果起点或终点匹配选中的类别，显示连接线
      if (sourceMatch || targetMatch || sourceSpecialMatch || targetSpecialMatch) {
        link.style("opacity", 1);
      } else {
        link.style("opacity", 0.1);
      }
    });
  }

  /**
   * 清除类别筛选
   */
  clearCategoryFilter() {
    if (!this.renderManager.renderedLinks) return;

    this.renderManager.renderedLinks.style("opacity", 1);
  }
}
