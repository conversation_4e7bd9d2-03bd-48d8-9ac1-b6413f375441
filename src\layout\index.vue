<template>
  <Defaults v-if="getThemeConfig.layout === 'defaults'" />
  <Vertical v-else-if="getThemeConfig.layout === 'vertical'" />
  <Horizontal v-else-if="getThemeConfig.layout === 'horizontal'" />
  <Mix v-else-if="getThemeConfig.layout === 'mix'" />
</template>

<script>
export default {
  name: "layout",
  components: {
    Defaults: () => import("@/layout/main/defaults.vue"),
    Vertical: () => import("@/layout/main/vertical.vue"),
    Horizontal: () => import("@/layout/main/horizontal.vue"),
    Mix: () => import("@/layout/main/mix.vue")
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    }
  },
  created() {},
  methods: {}
};
</script>
