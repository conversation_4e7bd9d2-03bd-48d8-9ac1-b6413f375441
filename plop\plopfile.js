import { fetchTemplateRepo, fetchTemplate } from "./utils/fetch-template-git.js";
import fs from "fs";
import path from "path";

export default async function (plop) {
  const tempRepoDir = "./.temp-template-repo"; // 临时Git存储目录
  const repoSrcDir = path.join(tempRepoDir, "src/template-repo"); // 仓库内模板的路径

  // 拉取远程仓库
  try {
    console.log("初始化：开始拉取远程模板仓库...");
    await fetchTemplateRepo(tempRepoDir); // 拉取远程模板
    console.log("模板仓库拉取成功！");
  } catch (err) {
    console.error(`模板仓库拉取失败：${err.message}`);
    throw new Error("初始化失败：无法拉取远程模板仓库！");
  }

  // 动态生成模板选项：遍历 src/template-repo 下的目录
  let templateChoices;
  try {
    templateChoices = fs.readdirSync(repoSrcDir).filter((file) => {
      const fullPath = path.join(repoSrcDir, file);
      return fs.statSync(fullPath).isDirectory(); // 保留子目录
    });
  } catch (err) {
    console.error(`加载模板类型失败：${err.message}`);
    throw new Error("初始化失败：无法加载模板类型！");
  }

  // 动态生成目标路径选项：遍历 src/views 下的子目录
  let viewDirectories;
  try {
    const viewsPath = "./src/views";
    if (!fs.existsSync(viewsPath)) {
      fs.mkdirSync(viewsPath, { recursive: true }); // 如果 views 目录不存在，则创建
    }
    viewDirectories = fs.readdirSync(viewsPath).filter((file) => {
      const fullPath = path.join(viewsPath, file);
      return fs.statSync(fullPath).isDirectory(); // 保留子目录
    });
  } catch (err) {
    console.error(`加载视图目录失败：${err.message}`);
    throw new Error("初始化失败：无法加载视图目录！");
  }

  // 注册动作类型 fetchTemplate
  plop.setActionType("fetchTemplate", async (_, config) => {
    const { templateType, targetPath } = config;

    if (!templateType || !targetPath) {
      throw new Error(`fetchTemplate 参数错误！templateType=${templateType}, targetPath=${targetPath}`);
    }

    try {
      console.log(`开始拉取模板：类型=${templateType}, 目标路径=${targetPath}`);
      await fetchTemplate(templateType, targetPath, tempRepoDir); // 调用模板生成逻辑
      console.log(`模板生成成功：${targetPath}`);
    } catch (err) {
      console.error(`模板拉取失败：${err.message}`);
      throw new Error("模板拉取失败！请检查模板类型和远程仓库连接！");
    }

    return `模板生成成功：${targetPath}`;
  });

  // 注册动作类型 cleanup
  plop.setActionType("cleanup", (_, config) => {
    const { tempDir } = config;
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true });
      console.log(`临时目录 ${tempDir} 已删除`);
    }
    return `清理完成：${tempDir}`;
  });

  // 配置 plop 的生成器
  plop.setGenerator("创建页面", {
    description: "创建基于远程模板的页面",
    prompts: [
      {
        type: "input",
        name: "name",
        message: "请输入页面名称:",
        validate(name) {
          if (!name) return "页面名称不能为空！";
          if (!/^[a-z][a-z-]+[a-z]$/.test(name)) {
            return "页面名称格式错误，请使用小写字母和连字符（如：my-page）";
          }
          return true;
        }
      },
      {
        type: "list",
        name: "type",
        message: "请选择页面模板类型:",
        choices: templateChoices // 动态生成模板类型选项
      },
      {
        type: "list",
        name: "targetDirectory",
        message: "请选择页面生成的目标路径（基于 src/views/）:",
        choices: ["默认", ...viewDirectories, "新建子目录"], // 添加“默认”选项
        default: "默认"
      },
      {
        type: "input",
        name: "customDirectory",
        message: "请输入新建的子目录名称（相对 src/views）：",
        when(answers) {
          return answers.targetDirectory === "新建子目录"; // 仅当选择“新建子目录”时显示此询问
        },
        validate(customDirectory) {
          if (!customDirectory) return "目录名称不能为空！";
          if (!/^[a-z][a-z0-9-/]+[a-z0-9]$/.test(customDirectory)) {
            return "目录名称格式错误，请使用符合路径格式的小写字母、数字和斜杠（如：my-folder/sub-folder）";
          }
          return true;
        }
      }
    ],
    actions: (answers) => {
      // 根据选择确定目标路径
      let targetSubPath;

      if (answers.targetDirectory === "默认") {
        // 默认行为：直接以输入的页面名称作为子目录
        targetSubPath = answers.name;
      } else if (answers.targetDirectory === "新建子目录") {
        // 当选择“新建子目录”时，使用用户填写的 customDirectory
        targetSubPath = answers.customDirectory;
      } else {
        // 从已有目录中选择
        targetSubPath = answers.targetDirectory;
      }

      // 最终确定的目标路径
      const targetPath = `src/views/${targetSubPath}/index.vue`;

      return [
        {
          type: "fetchTemplate", // 自定义动作进行模板生成
          templateType: answers.type, // 模板类型
          targetPath: targetPath // 目标路径
        },
        {
          type: "cleanup", // 清理临时目录
          tempDir: tempRepoDir
        }
      ];
    }
  });
}
