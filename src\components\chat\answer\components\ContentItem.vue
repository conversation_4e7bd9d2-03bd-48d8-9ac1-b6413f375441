<template>
  <component
    class="text-white"
    :is="componentType"
    :data="data"
  />
</template>

<script>
import TableCard from "./card/table";
import ListCard from "./card/list";
import TextCard from "./card/text";
import UrlCard from "./card/url";
import TextareaCard from "./card/textarea";
import FaultPointCard from "./card/fault-point";
import DialogCard from "./card/dialog";
import TabsCard from "./card/tabs";
import SectionCard from "./card/section";
import SuggestionsCard from "./card/suggestions";

export default {
  name: "ContentItem",
  components: {
    TableCard,
    ListCard,
    TextCard,
    UrlCard,
    TextareaCard,
    FaultPointCard,
    DialogCard,
    TabsCard,
    SectionCard,
    SuggestionsCard
  },
  props: {
    type: {
      type: String,
      required: true
    },
    data: {
      type: [Object, Array],
      required: true
    }
  },
  computed: {
    componentType() {
      const componentMap = {
        table: "TableCard",
        list: "ListCard",
        text: "TextCard",
        url: "UrlCard",
        textarea: "TextareaCard",
        faultPoint: "FaultPointCard",
        dialog: "DialogCard",
        tabs: "TabsCard",
        section: "SectionCard",
        suggestions: "SuggestionsCard"
      };
      return componentMap[this.type];
    }
  }
};
</script>
