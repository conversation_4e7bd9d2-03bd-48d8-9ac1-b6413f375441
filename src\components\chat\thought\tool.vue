<template>
  <div>
    <div
      class="max-w-full overflow-x-auto bg-white rounded-md"
      :class="{
        'inline-block shadow-sm': !isShowDetail
      }"
    >
      <div
        class="flex items-center h-7 px-2 cursor-pointer"
        @click="setIsShowDetail"
      >
        <svg-icon
          v-if="!payload.isFinished"
          class="w-3 h-3 text-gray-500 animate-spin shrink-0"
          icon-name="running"
        ></svg-icon>
        <svg-icon
          v-if="payload.isFinished && !isShowDetail"
          class="w-3 h-3 text-[#12B76A] shrink-0"
          icon-name="succeeded"
        ></svg-icon>
        <template v-if="payload.isFinished && isShowDetail">
          <svg-icon
            v-if="toolName.startsWith('dataset_')"
            class="shrink-0"
            icon-name="data-set"
          ></svg-icon>
          <template v-if="allToolIcons[toolName]">
            <div
              v-if="typeof allToolIcons[toolName] === 'string'"
              class="w-3 h-3 bg-cover bg-center rounded-[3px] shrink-0"
              :style="{
                backgroundImage: `url(${allToolIcons[toolName]})`
              }"
            ></div>
            <div
              class="rounded-[3px] shrink-0"
              v-text="icon?.content"
            ></div>
            <!-- <AppIcon
              v-else
              class="rounded-[3px] shrink-0"
              size="xs"
              :icon="icon?.content"
              :background="icon?.background"
            /> -->
          </template>
        </template>
        <span class="mx-1 text-xs font-medium text-gray-500 shrink-0">
          {{ payload.isFinished ? "已使用" : "正在使用" }}
        </span>
        <span
          class="text-xs font-medium text-gray-700 truncate"
          :title="toolLabel"
        >
          {{ toolLabel }}
        </span>
        <i
          class="el-icon-arrow-down ml-1 w-3 h-3 text-gray-500 select-none cursor-pointer shrink-0"
          :class="isShowDetail ? 'rotate-180' : ''"
        ></i>
      </div>
      <div
        class="border-t border-black/5 p-2 space-y-2"
        v-if="isShowDetail"
      >
        <Panel
          :isRequest="true"
          :toolName="toolName"
          :content="input"
        />
        <Panel
          v-if="output"
          :isRequest="false"
          :toolName="toolName"
          :content="output"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Tool",
  props: {
    payload: {
      type: Object,
      default: () => ({})
    },
    allToolIcons: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isShowDetail: false
    };
  },
  computed: {
    toolName() {
      const { name } = this.payload;
      return name.startsWith("dataset_") ? "知识库" : name;
    },
    toolLabel() {
      const { label } = this.payload;
      return label.startsWith("dataset_") ? "知识库" : label;
    }
  },
  methods: {
    setIsShowDetail() {
      this.isShowDetail = !this.isShowDetail;
    }
  }
};
</script>
