<template>
  <div class="layout-container layout-defaults">
    <Asides v-show="isSideBar" />
    <div class="layout-main">
      <Headers v-if="isSideBar" />
      <Mains />
    </div>
    <el-backtop target=".layout-scrollbar"></el-backtop>
  </div>
</template>

<script>
import Asides from "@/layout/component/aside.vue";
import Headers from "@/layout/component/header.vue";
import Mains from "@/layout/component/main.vue";
export default {
  name: "layoutDefaults",
  components: { Asides, Headers, Mains },
  data() {
    return {};
  },
  computed: {
    isSideBar() {
      return this.$store.state.themeConfig.themeConfig.isSideBar;
    }
  }
};
</script>
