<template>
  <div class="layout-breadcrumb-setting">
    <el-drawer
      title="布局配置"
      :visible.sync="getThemeConfig.isDrawer"
      direction="rtl"
      destroy-on-close
      size="280px"
      @close="onDrawerClose"
    >
      <div class="layout-breadcrumb-setting-bar">
        <el-divider>主题</el-divider>
        <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">深色模式</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-switch
              v-model="getThemeConfig.isIsDark"
              :width="35"
              @change="onAddDarkChange"
            ></el-switch>
          </div>
        </div>
        <div
          class="layout-breadcrumb-setting-bar-flex"
          v-show="getThemeConfig.isIsDark"
        >
          <div class="layout-breadcrumb-setting-bar-flex-label">主题风格</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-radio-group
              v-model="getThemeConfig.layoutStyle"
              @input="onLayoutStyleChange"
            >
              <el-radio label="normal">默认</el-radio>
              <el-radio label="custom">定制</el-radio>
            </el-radio-group>
          </div>
        </div>
        <!-- <div class="layout-breadcrumb-setting-bar-flex">
					<div class="layout-breadcrumb-setting-bar-flex-label">主题色</div>
					<div class="layout-breadcrumb-setting-bar-flex-value">
						<el-color-picker v-model="getThemeConfig.primary" size="small" @change="onColorPickerChange"></el-color-picker>
					</div>
				</div> -->
        <div
          class="layout-breadcrumb-setting-bar-flex"
          v-show="!getThemeConfig.isIsDark || getThemeConfig.layoutStyle !== 'custom'"
        >
          <div class="theme-color">
            <div
              class="theme-color-item"
              v-for="(item, index) in themeColors"
              :key="index"
              v-show="showThemeColors(item.themeColor)"
              :style="getThemeColorStyle(item.color)"
              @click="setLayoutThemeColor(item)"
            >
              <el-tooltip
                effect="dark"
                :content="item.title"
                placement="top"
              >
                <i
                  class="el-icon-check"
                  :style="getThemeColor(item.themeColor)"
                ></i>
              </el-tooltip>
            </div>
          </div>
        </div>

        <!-- 布局切换 -->
        <el-divider>布局切换</el-divider>
        <div class="layout-drawer-content-flex">
          <!-- defaults 布局 -->
          <div
            class="layout-drawer-content-item"
            @click="onSetLayout('defaults')"
          >
            <section
              class="el-container el-circular"
              :class="{
                'drawer-layout-active': getThemeConfig.layout === 'defaults'
              }"
            >
              <aside
                class="el-aside"
                style="width: 20px"
              ></aside>
              <section class="el-container is-vertical">
                <header
                  class="el-header"
                  style="height: 10px"
                ></header>
                <main class="el-main">
                  <div class="layout-tips-txt">默认</div>
                </main>
              </section>
            </section>
          </div>
          <!-- vertical 布局 -->
          <div
            class="layout-drawer-content-item"
            @click="onSetLayout('vertical')"
          >
            <section
              class="el-container is-vertical el-circular"
              :class="{
                'drawer-layout-active': getThemeConfig.layout === 'vertical'
              }"
            >
              <header
                class="el-header"
                style="height: 10px"
              ></header>
              <section class="el-container">
                <aside
                  class="el-aside"
                  style="width: 20px"
                ></aside>
                <section class="el-container is-vertical">
                  <main class="el-main">
                    <div class="layout-tips-txt">顶部</div>
                  </main>
                </section>
              </section>
            </section>
          </div>
          <!-- horizontal 布局 -->
          <div
            class="layout-drawer-content-item"
            @click="onSetLayout('horizontal')"
          >
            <section
              class="el-container is-vertical el-circular"
              :class="{
                'drawer-layout-active': getThemeConfig.layout === 'horizontal'
              }"
            >
              <header
                class="el-header"
                style="height: 10px"
              ></header>
              <section class="el-container">
                <section class="el-container is-vertical">
                  <main class="el-main">
                    <div class="layout-tips-txt">横向</div>
                  </main>
                </section>
              </section>
            </section>
          </div>
          <!-- mix 布局 -->
          <div
            class="layout-drawer-content-item"
            @click="onSetLayout('mix')"
          >
            <section
              class="el-container is-vertical el-circular"
              :class="{
                'drawer-layout-active': getThemeConfig.layout === 'mix'
              }"
            >
              <header
                class="el-header"
                style="height: 10px"
              ></header>
              <section class="el-container">
                <aside
                  class="el-aside"
                  style="width: 20px"
                ></aside>
                <section class="el-container is-vertical">
                  <main class="el-main">
                    <div class="layout-tips-txt">混合</div>
                  </main>
                </section>
              </section>
            </section>
          </div>
        </div>

        <!-- 界面设置 -->
        <el-divider>界面设置</el-divider>
        <div
          class="layout-breadcrumb-setting-bar-flex"
          :style="{
            opacity: getThemeConfig.layout === 'vertical' || getThemeConfig.layout === 'horizontal' ? 0.5 : 1
          }"
        >
          <div class="layout-breadcrumb-setting-bar-flex-label">开启面包屑</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-switch
              v-model="getThemeConfig.isBreadcrumb"
              :disabled="getThemeConfig.layout === 'vertical' || getThemeConfig.layout === 'horizontal'"
              :width="35"
              @change="setLocalThemeConfig"
            ></el-switch>
          </div>
        </div>
        <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">开启面包屑图标</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-switch
              v-model="getThemeConfig.isBreadcrumbIcon"
              :width="35"
              @change="setLocalThemeConfig"
            ></el-switch>
          </div>
        </div>
        <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">开启标签页</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-switch
              v-model="getThemeConfig.isTagsview"
              :width="35"
              @change="setLocalThemeConfig"
            ></el-switch>
          </div>
        </div>
        <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">开启消息提示</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-switch
              v-model="getThemeConfig.isNotice"
              :width="35"
              @change="setLocalThemeConfig"
            ></el-switch>
          </div>
        </div>
        <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">开启登录信息</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-switch
              v-model="getThemeConfig.isUserInfo"
              :width="35"
              @change="setLocalThemeConfig"
            ></el-switch>
          </div>
        </div>
        <!-- <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">
            开启标签页缓存
          </div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-switch
              v-model="getThemeConfig.isCacheTagsView"
              :width="35"
              @change="setLocalThemeConfig"
            ></el-switch>
          </div>
        </div> -->
        <!-- <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">开启底部</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-switch
              v-model="getThemeConfig.isFooter"
              :width="35"
              @change="setLocalThemeConfig"
            ></el-switch>
          </div>
        </div> -->
        <!-- 其它设置 -->
        <el-divider content-position="left">其它设置</el-divider>
        <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">标签风格</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-radio-group
              v-model="getThemeConfig.tagsStyle"
              @input="setLocalThemeConfig"
            >
              <el-radio label="schedule">灵动</el-radio>
              <el-radio label="card">卡片</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="layout-breadcrumb-setting-bar-flex">
          <div class="layout-breadcrumb-setting-bar-flex-label">主页面切换动画</div>
          <div class="layout-breadcrumb-setting-bar-flex-value">
            <el-select
              v-model="getThemeConfig.animation"
              placeholder="请选择"
              size="mini"
              style="width: 100px"
              @change="setLocalThemeConfig"
            >
              <el-option
                label="slide-right"
                value="slide-right"
              ></el-option>
              <el-option
                label="slide-left"
                value="slide-left"
              ></el-option>
              <el-option
                label="opacitys"
                value="opacitys"
              ></el-option>
              <el-option
                label="关闭"
                value=""
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="copy-config">
          <el-button
            size="small"
            class="copy-config-btn-reset"
            type="danger"
            icon="el-icon-refresh-right"
            @click="onResetConfigClick"
            >清空缓存并返回登录页</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { Local } from "@/utils/storage.js";
import { themeColors, useChangeColor } from "@/utils/theme.js";
export default {
  name: "layoutBreadcrumbSetting",
  data() {
    return {
      themeColors
    };
  },
  computed: {
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    /** 当网页为暗黑模式时不显示亮白色切换选项 */
    showThemeColors() {
      return (themeColor) => {
        // return !(themeColor === "light");
        return !(themeColor === "light" && this.getThemeConfig.isIsDark);
      };
    },
    getThemeColorStyle() {
      return (color) => {
        return { background: color };
      };
    },
    /** 主题色 激活选择项 */
    getThemeColor() {
      return (current) => {
        let color = "transparent";
        if (current === this.getThemeConfig.theme && this.getThemeConfig.theme !== "light") {
          color = this.themeColors[1].color;
        } else if (current === this.getThemeConfig.theme && this.getThemeConfig.theme === "light") {
          color = this.themeColors[0].color;
        }
        return { color };
      };
    }
  },
  created() {},
  mounted() {
    this.initLayoutConfig();
  },
  methods: {
    /** 设置导航主题色 */
    setLayoutThemeColor(item) {
      const { color, themeColor } = item;
      let darkColor = "";
      if (themeColor === "default" || themeColor === "light") {
        this.getThemeConfig.primary = this.getThemeConfig.themeColor;
        darkColor = this.themeColors[0].color;
      } else {
        this.getThemeConfig.primary = color;
        darkColor = useChangeColor().getDarkColor(color, 0.8);
      }
      this.getThemeConfig.theme = themeColor;
      // 颜色加深
      if (this.getThemeConfig.isIsDark) {
        darkColor = this.themeColors[0].color;
      }
      const bgColor = themeColor === "light" ? this.themeColors[1].color : darkColor;
      const textColor = themeColor === "light" ? this.themeColors[0].color : this.themeColors[1].color;
      const attributes = {
        "bg-aside": bgColor,
        "c-aside": textColor,
        "bg-header": bgColor,
        "c-header": textColor
      };
      document.documentElement.setAttribute("layout-theme-color", themeColor);
      document.documentElement.style.setProperty("--nari-c-primary", this.getThemeConfig.primary);
      // 颜色变浅
      for (let i = 1; i <= 9; i++) {
        document.documentElement.style.setProperty(`--nari-c-primary-light-${i}`, `${useChangeColor().getLightColor(this.getThemeConfig.primary, i / 10)}`);
      }
      this.onBgColorPickerChange(attributes);
      this.setLocalThemeConfig();
    },
    // 深色模式
    onAddDarkChange() {
      const body = document.documentElement;
      let themeMode = "light";
      if (this.getThemeConfig.isIsDark) {
        themeMode = "dark";
        this.setLayoutThemeColor(this.themeColors[0]);
      }
      body.setAttribute("data-theme", themeMode);
    },
    // 深色模式，设置布局风格
    onLayoutStyleChange() {
      window.document.body.setAttribute("layout-style", this.getThemeConfig.layoutStyle);
      if (this.getThemeConfig.layoutStyle === "custom") this.setLayoutThemeColor(this.themeColors[0]);
    },
    // 全局主题
    onColorPickerChange() {
      if (!this.getThemeConfig.primary) return;
      this.getThemeConfig.layoutStyle === "custom" && (this.getThemeConfig.primary = this.getThemeConfig.themeColor);
      // 颜色加深
      document.documentElement.style.setProperty("--nari-c-primary", this.getThemeConfig.primary);
      // 颜色变浅
      for (let i = 1; i <= 9; i++) {
        document.documentElement.style.setProperty(`--nari-c-primary-light-${i}`, `${useChangeColor().getLightColor(this.getThemeConfig.primary, i / 10)}`);
      }
      this.setLocalThemeConfig();
    },
    // 初始化：刷新页面时，设置了值，直接取缓存中的值进行初始化
    initLayoutConfig() {
      window.addEventListener("load", () => {
        // 默认样式
        this.onColorPickerChange();
        // 深色模式
        if (this.getThemeConfig.isIsDark) this.onAddDarkChange();
        const attributes = {
          layout: this.getThemeConfig.layout,
          "layout-style": this.getThemeConfig.layoutStyle
        };
        this.setAttributes(window.document.body, attributes);
      });
    },
    // 存储布局配置
    setLocalThemeConfig() {
      Local.remove("themeConfigPrev");
      this.getThemeConfig.layoutHeaderHeight = this.getThemeConfig.isTagsview ? "95px" : "60px";
      document.documentElement.style.setProperty("--nari-header-height", this.getThemeConfig.layoutHeaderHeight);
      Local.set("themeConfigPrev", this.getThemeConfig);
      this.setLocalThemeConfigStyle();
    },
    // 存储布局配置全局主题样式（html根标签）
    setLocalThemeConfigStyle() {
      Local.set("themeConfigStyle", document.documentElement.style.cssText);
    },
    // 布局配置弹窗打开
    openDrawer() {
      this.getThemeConfig.isDrawer = true;
    },
    // 关闭弹窗时，初始化变量
    onDrawerClose() {
      this.getThemeConfig.isDrawer = false;
      this.setLocalThemeConfig();
    },
    // 布局切换
    onSetLayout(layout) {
      // Local.set("oldLayout", layout);
      if (this.getThemeConfig.layout === layout) {
        return false;
      }
      window.document.body.setAttribute("layout", layout);
      this.getThemeConfig.layout = layout;
      this.getThemeConfig.isDrawer = false;
    },
    // 菜单 / 顶栏背景等
    onBgColorPickerChange(attrs) {
      Object.keys(attrs).forEach((attr) => {
        document.documentElement.style.setProperty(`--nari-${attr}`, attrs[attr]);
      });
      // document.documentElement.style.setProperty(`--nari-${bg}`, rgb);
      this.setLocalThemeConfigStyle();
    },
    // 一键恢复默认
    onResetConfigClick() {
      Local.clear();
      window.location.reload();
    },
    setAttributes(element, attributes) {
      Object.keys(attributes).forEach((attr) => {
        element.setAttribute(attr, attributes[attr]);
      });
    }
  }
};
</script>

<style scoped lang="scss">
.layout-breadcrumb-setting-bar {
  padding: 0 20px 10px 20px;
  .layout-breadcrumb-setting-bar-flex {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 4px 0;
    &-label {
      flex: 1;
      color: var(--nari-c-text-1);
    }
  }
  .layout-drawer-content-flex {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    overflow: hidden;
    .layout-drawer-content-item {
      width: 50%;
      height: 80px;
      border: 1px solid transparent;
      border-radius: 5px;
      padding: 0 5px;
      position: relative;
      cursor: pointer;
      overflow: hidden;
      .el-container {
        height: 100%;
        .el-aside-dark {
          background-color: var(--nari-c-setting-header);
        }
        .el-aside {
          background-color: var(--nari-c-setting-aside);
        }
        .el-header {
          background-color: var(--nari-c-setting-header);
        }
        .el-main {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 5px;
          background-color: var(--nari-c-setting-main);
        }
      }
      .el-circular {
        border-radius: 2px;
        overflow: hidden;
        border: 4px solid transparent;
        transition: all 0.3s ease-in-out;
      }
      .drawer-layout-active {
        border: 4px solid;
        border-color: var(--nari-c-primary);
      }
      .layout-tips-txt {
        font-size: 12px;
        letter-spacing: 2px;
        white-space: nowrap;
        color: #8e8e8e;
        text-align: center;
        transform: rotate(30deg);
      }
      &:hover {
        .el-circular {
          transition: all 0.3s ease-in-out;
          border: 4px solid;
          border-color: var(--nari-c-primary);
        }
        .layout-tips-txt {
          color: darken(#8e8e8e, 30%);
        }
      }
    }
  }
  .theme-color {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;

    &-item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 20px;
      height: 20px;
      font-weight: 700;
      text-align: center;
      border-radius: 2px;
      cursor: pointer;
      &:not(:last-child) {
        margin-right: 8px;
      }

      &:nth-child(1),
      &:nth-child(2) {
        border: 1px solid var(--nari-c-border-base);
      }
      i {
        font-size: 18px;
      }
    }
  }
  .copy-config {
    margin: 10px 0;
    .copy-config-btn-reset {
      width: 100%;
      margin: 10px 0 0;
    }
  }
}
</style>
