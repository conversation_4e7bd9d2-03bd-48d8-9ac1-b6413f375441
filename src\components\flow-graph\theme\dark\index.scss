@import "variable.scss";
@import "toolbar.scss";
@import "loading.scss";
// 主容器
.knowledge-graph-container {
  background-color: $bg-dark; // 使用浅色背景
}

// 图形容器
.graph-container {
  background-color: $bg-dark; // 使用浅色背景
}

// 用于JS动态高亮文本，例如在搜索结果中
.highlight {
  background-color: #ffdd00; // 黄色高亮背景可以保留
  color: $text-primary; // 高亮文字用深色
}

// 提示框
.tooltip {
  background-color: rgba($bg-dark, 0.95);
  color: $text-primary;
  box-shadow: 0 2px 10px $shadow-dark;
  border: 1px solid $border-dark;
}

.tooltip-title {
  border-bottom: 1px solid rgba($text-primary, 0.2);
  color: $text-primary;
}

.tooltip-desc {
  color: $text-secondary;
}

.tooltip-category {
  color: $text-secondary;
}

.tooltip-attrs {
  color: $text-secondary;
}

.node-group {
  stroke: $border-dark !important;
}
