<template>
  <div class="rounded-md bg-gray-100 overflow-hidden border border-black/5">
    <div class="flex items-center px-2 py-1 leading-[18px] bg-gray-50 uppercase text-xs font-medium text-gray-500">{{ `${isRequest ? "请求来自" : "响应来自"} ${toolName}` }}</div>
    <div class="p-2 border-t border-black/5 leading-4 text-xs text-gray-700">{{ content }}</div>
  </div>
</template>

<script>
export default {
  name: "Panel",
  props: {
    isRequest: {
      type: Boolean,
      default: false
    },
    toolName: {
      type: String,
      default: ""
    },
    content: {
      type: String,
      default: ""
    }
  },
  data() {
    return {};
  }
};
</script>
