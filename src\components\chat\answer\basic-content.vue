<template>
  <div class="answer-text-container p-2">
    <template v-if="isJSONString">
      <CustomContent
        :data="content"
        :item="item"
      />
    </template>
    <template v-else>
      <Markdown
        v-if="item.annotation?.logAnnotation"
        :content="item.annotation?.logAnnotation.content || ''"
        :item="item"
      />
      <Markdown
        v-else
        :content="item.content"
        :item="item"
      />
    </template>
  </div>
</template>

<script>
import CustomContent from "./custom-content";
import Markdown from "@/components/base/Markdown";
import { isJSON } from "@/utils/libs";
export default {
  name: "BasicContent",
  components: {
    CustomContent,
    Markdown
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      content: []
    };
  },
  computed: {
    isJSONString() {
      return isJSON(this.item.content) && isNaN(this.item.content);
    }
  },
  created() {
    this.isJSONString && this.generateData();
  },
  methods: {
    generateData() {
      this.content = JSON.parse(this.item.content);
    }
  }
};
</script>
