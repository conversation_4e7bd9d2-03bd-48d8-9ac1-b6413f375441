<template>
  <div class="my-2 space-y-2">
    <Tool
      v-for="(item, index) in toolThoughtList"
      :key="index"
      :payload="item"
      :allToolIcons="allToolIcons"
    />
  </div>
</template>

<script>
import Tool from "./tool";
export default {
  name: "Thought",
  components: {
    Tool
  },
  props: {
    thought: {
      type: Object,
      default: () => ({})
    },
    allToolIcons: {
      type: Object,
      default: () => ({})
    },
    isFinished: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    toolThoughtList() {
      const [toolNames, isValueArray] = () => {
        try {
          if (Array.isArray(JSON.parse(this.thought.tool))) return [JSON.parse(this.thought.tool), true];
        } catch (e) {
          console.error("error: ", e);
        }
        return [[this.thought.tool], false];
      };

      return toolNames.map((toolName, index) => {
        return {
          name: toolName,
          label: this.thought.tool_labels?.toolName?.language ?? toolName,
          input: this.getValue(this.thought.tool_input, isValueArray, index),
          output: this.getValue(this.thought.observation, isValueArray, index),
          isFinished: this.isFinished
        };
      });
    }
  },
  methods: {
    getValue(value, isValueArray, index) {
      if (isValueArray) {
        try {
          return JSON.parse(value)[index];
        } catch (e) {
          console.error("error: ", e);
        }
      }
      return value;
    }
  }
};
</script>
