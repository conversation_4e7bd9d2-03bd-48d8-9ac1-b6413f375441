<template>
  <div class="plan">
    <div class="toolbar">
      <el-button
        type="primary"
        @click="handlePreview"
        >预览</el-button
      >
      <el-button
        type="primary"
        @click="handleSave"
        >保存</el-button
      >
    </div>
    <div class="form-box">
      <!-- 预案事故名称 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">预案事故名称</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input v-model="plan.planName"></el-input>
        </el-col>
      </el-row>
      <!-- 预案事故编号 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">编号</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input v-model="plan.planCode"></el-input>
        </el-col>
      </el-row>
      <!-- 负责单位\创建日期 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">负责单位</span>
        </el-col>
        <el-col
          :span="8"
          class="col-value auto-row-col"
        >
          <el-input v-model="plan.responsibleUnit"></el-input>
        </el-col>
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">创建日期</span>
        </el-col>
        <el-col
          :span="8"
          class="col-value auto-row-col"
        >
          <el-input v-model="plan.createDate"></el-input>
        </el-col>
      </el-row>
      <!-- 编制人\审核人 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">编制人</span>
        </el-col>
        <el-col
          :span="8"
          class="col-value auto-row-col"
        >
          <el-input v-model="plan.author"></el-input>
        </el-col>
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">审核人</span>
        </el-col>
        <el-col
          :span="8"
          class="col-value auto-row-col"
        >
          <el-input v-model="plan.reviewer"></el-input>
        </el-col>
      </el-row>
      <!-- 概述 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">概述</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input
            v-model="plan.overview"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          ></el-input>
        </el-col>
      </el-row>
      <!-- 事前运行方式 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">事前运行方式</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input
            v-model="plan.preAccidentOperation"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          ></el-input>
        </el-col>
      </el-row>
      <!-- 事后运行方式 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">事后运行方式</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input
            v-model="plan.postAccidentOperation"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          ></el-input>
        </el-col>
      </el-row>
      <!-- 事后风险方式 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">事后风险方式</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input
            v-model="plan.riskAnalysis"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          ></el-input>
        </el-col>
      </el-row>
      <!-- 主要控制目标 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">主要控制目标</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <div
            v-if="typeof plan['主要控制目标'] == 'object'"
            class="form-box"
          >
            <el-row
              v-for="(p, index) in plan['主要控制目标']"
              :key="index"
              class="detail-row"
            >
              <el-col
                :span="4"
                class="col-label"
                style="height: auto"
              >
                {{ p[0] }}
              </el-col>
              <el-col
                :span="20"
                class="col-value auto-row-col"
              >
                {{ p[1] }}
              </el-col>
            </el-row>
          </div>
          <div v-else>
            {{ plan.controlObjectives }}
          </div>
        </el-col>
      </el-row>
      <!-- 事故处理过程及要点 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">事故处理过程及要点</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input
            v-model="plan.handlingProcedures"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          ></el-input>
        </el-col>
      </el-row>
      <!-- 事故信息上报 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">事故信息上报</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <div class="form-box">
            <el-row class="content-row-auto">
              <el-col
                :span="4"
                class="col-label"
                style="height: auto"
              >
                <span class="text-label">上报单位</span>
              </el-col>
              <el-col
                :span="20"
                class="col-value auto-row-col"
              >
                <el-input v-model="reportingUnit"></el-input>
              </el-col>
            </el-row>
            <el-row class="content-row-auto">
              <el-col
                :span="4"
                class="col-label"
                style="height: auto"
              >
                <span class="text-label">上报对象</span>
              </el-col>
              <el-col
                :span="20"
                class="col-value auto-row-col"
              >
                <el-input v-model="recipient"></el-input>
              </el-col>
            </el-row>
            <el-row class="content-row-auto">
              <el-col
                :span="4"
                class="col-label"
                style="height: auto"
              >
                <span class="text-label">上报内容</span>
              </el-col>
              <el-col
                :span="20"
                class="col-value auto-row-col"
              >
                <el-input
                  v-model="plan.report.content"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                ></el-input>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
      <!-- 事故信息发布 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">事故信息发布</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <div class="form-box">
            <el-row class="content-row-auto">
              <el-col
                :span="4"
                class="col-label"
                style="height: auto"
              >
                <span class="text-label">发布单位</span>
              </el-col>
              <el-col
                :span="20"
                class="col-value auto-row-col"
              >
                <el-input v-model="publishingUnit"></el-input>
              </el-col>
            </el-row>
            <el-row class="content-row-auto">
              <el-col
                :span="4"
                class="col-label"
                style="height: auto"
              >
                <span class="text-label">发布对象</span>
              </el-col>
              <el-col
                :span="20"
                class="col-value auto-row-col"
              >
                <el-input v-model="targetAudience"></el-input>
              </el-col>
            </el-row>
            <el-row class="content-row-auto">
              <el-col
                :span="4"
                class="col-label"
                style="height: auto"
              >
                <span class="text-label">发布内容</span>
              </el-col>
              <el-col
                :span="20"
                class="col-value auto-row-col"
              >
                <el-input
                  v-model="plan.publish.content"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                ></el-input>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
      <!-- 事故影响评估 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">事故影响评估</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input
            v-model="plan.impactAssessment"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          ></el-input>
        </el-col>
      </el-row>
      <!-- 备注 -->
      <el-row class="content-row-auto">
        <el-col
          :span="4"
          class="col-label"
          style="height: auto"
        >
          <span class="text-label">备注</span>
        </el-col>
        <el-col
          :span="20"
          class="col-value auto-row-col"
        >
          <el-input
            v-model="plan.remarks"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          ></el-input>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { PlanApi } from "@/api/plan";
// import PlanData from "@/mock/plan.json";

export default {
  props: {
    id: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      plan: {
        report: {},
        publish: {}
      }
    };
  },
  computed: {
    reportingUnit: {
      get() {
        return this.plan.report?.reportingUnit?.join(",");
      },
      set(value) {
        this.plan.report.reportingUnit = value.split(",");
      }
    },
    recipient: {
      get() {
        return this.plan.report?.recipient?.join(",");
      },
      set(value) {
        this.plan.report.recipient = value.split(",");
      }
    },
    publishingUnit: {
      get() {
        return this.plan.publish?.publishingUnit?.join(",");
      },
      set(value) {
        this.plan.publish.publishingUnit = value.split(",");
      }
    },
    targetAudience: {
      get() {
        return this.plan.publish?.targetAudience?.join(",");
      },
      set(value) {
        this.plan.publish.targetAudience = value.split(",");
      }
    }
  },
  created() {
    // this.plan = PlanData.data;
    this.getPlanDetail();
  },
  mounted() {},
  methods: {
    getPlanDetail() {
      PlanApi.fetchPlanDetail(this.id).then((res) => {
        this.plan = res.data || {};
        this.$set(this.plan, "report", this.plan.report || {});
        this.$set(this.plan, "publish", this.plan.publish || {});
      });
    },
    handleSave() {
      PlanApi.savePlan(this.plan)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .catch((err) => {
          this.$message.error(err.message);
        });
    },
    handlePreview() {
      this.$router.push({
        path: "/plan/preview",
        query: {
          id: this.id,
          data: JSON.stringify(this.plan)
        }
      });
    }
  }
};
</script>
<style scoped lang="scss">
.plan-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #1a1a1a;
}

.toolbar {
  // padding: 10px;
  // background-color: #1a1a1a;
  // border-bottom: 1px solid #2c3e50;
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.form-box {
  // background-color: #1a1a1a;
  // padding: 20px;
  color: #ffffff;
}

.col-label {
  color: #3498db;
  font-weight: bold;
}

.text-label {
  color: #3498db;
}

:deep(.el-input__inner) {
  background-color: #2c3e50;
  border: 1px solid #3498db;
  color: #ffffff;
}

:deep(.el-textarea__inner) {
  background-color: #2c3e50;
  border: 1px solid #3498db;
  color: #ffffff;
}

:deep(.el-button--primary) {
  background-color: #3498db;
  border-color: #3498db;
  &:hover {
    background-color: #2980b9;
    border-color: #2980b9;
  }
}
</style>
