<template>
  <div>
    <div class="chat-text question-text flex items-center">{{ item.content }}</div>
    <div
      class="image-container"
      v-if="isAvatar"
    >
      <div class="avatar">
        <img
          src="@/assets/images/chat/nw_avatar.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Question",
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    isAvatar: {
      type: Boolean,
      default: true
    }
  }
};
</script>
