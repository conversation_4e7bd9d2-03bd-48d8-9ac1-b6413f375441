<template>
  <div class="layout-container layout-horizontal flex-col">
    <Headers v-show="isSideBar" />
    <Mains />
    <el-backtop target=".layout-scrollbar"></el-backtop>
  </div>
</template>

<script>
import Headers from "@/layout/component/header.vue";
import Mains from "@/layout/component/main.vue";
export default {
  name: "layoutHorizontal",
  components: { Headers, Mains },
  computed: {
    isSideBar() {
      return this.$store.state.themeConfig.themeConfig.isSideBar;
    }
  }
};
</script>
