<template>
  <el-header
    class="layout-header"
    :height="setHeaderHeight"
  >
    <NavBarsIndex />
  </el-header>
</template>

<script>
import NavBarsIndex from "@/layout/navBars/index.vue";
export default {
  name: "layoutHeader",
  components: { NavBarsIndex },
  data() {
    return {};
  },
  computed: {
    // 设置顶部 header 的具体高度
    setHeaderHeight() {
      const { isTagsview, layout } = this.$store.state.themeConfig.themeConfig;
      if (isTagsview && (layout === "defaults" || layout === "horizontal")) return "95px";
      else return "60px";
    }
  }
};
</script>
