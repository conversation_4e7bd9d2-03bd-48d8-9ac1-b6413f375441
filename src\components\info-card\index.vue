<template>
  <div
    class="selection-card"
    :class="{ 'yes-selected': selectedValue === true, 'no-selected': selectedValue === false }"
  >
    <div class="card-content">
      <!-- 左侧状态图标 -->
      <div class="status-icon"></div>

      <!-- 中间文字区域 -->
      <div
        class="card-text"
        :class="{ 'is-link': cardUrl && selectedValue }"
        @click="handleClick"
      >
        {{ cardText }}
      </div>

      <!-- 右侧选择器，使用el-radio-button -->
      <div class="radio-group-container">
        <el-radio-group
          v-model="selectedValue"
          size="mini"
          :disabled="disabled"
        >
          <el-radio-button :label="true">是</el-radio-button>
          <el-radio-button :label="false">否</el-radio-button>
        </el-radio-group>
      </div>
      <!-- <div
        class="card-url"
        v-if="cardUrl"
        @click="handleClick"
      >
        详情
      </div> -->
    </div>

    <nari-dialog
      class="selection-card-dialog"
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :title="cardText"
      :show-footer="false"
    >
      <img
        :src="imgSrc"
        alt="img"
      />
    </nari-dialog>
  </div>
</template>

<script>
export default {
  name: "InfoCard",
  props: {
    // 卡片显示的文字
    cardText: {
      type: String,
      required: true
    },
    // 卡片的值，用于v-model绑定
    value: {
      type: Boolean,
      default: null
    },
    // 卡片的标识键
    cardKey: {
      type: String,
      default: ""
    },
    cardUrl: {
      type: String,
      default: ""
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedValue: this.value,
      dialogVisible: false,
      imgSrc: ""
    };
  },
  watch: {
    // 监听内部选择变化，通知父组件
    selectedValue(newVal) {
      // 在事件中包含cardKey和cardText
      this.$emit("input", newVal, {
        key: this.cardKey,
        text: this.cardText
      });
    },
    // 监听外部值变化，更新内部状态
    value(newVal) {
      this.selectedValue = newVal;
    }
  },
  methods: {
    handleClick() {
      // 没有图片路径或有图片路径并已经选否后，不打开弹窗
      if (!this.cardUrl || (this.cardUrl && !this.selectedValue)) return;
      this.imgSrc = this.cardUrl;
      this.dialogVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.selection-card {
  width: 31%;
  min-height: 45px;
  border: 2px solid #1e3364;
  border-radius: 5px;
  background-color: #0c1e44;
  color: white;
  margin-bottom: 15px;
  margin-right: 10px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.selection-card:nth-child(3n) {
  margin-right: 0;
}

.card-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 8px 10px;
  min-height: 36px;
  position: relative;
}

.status-icon {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #777;
  margin-right: 6px;
  flex-shrink: 0;
}

.card-text {
  flex: 1;
  font-size: 13px;
  line-height: 1.3;
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: none;
  min-width: 0;
  margin-right: 8px;
  display: flex; /* 添加这个 */
  align-items: center; /* 添加这个实现文本垂直居中 */
  min-height: 20px; /* 添加最小高度 */
  &.is-link {
    color: var(--nari-c-danger);
    text-decoration: underline;
    cursor: pointer;
    &:hover {
      color: var(--nari-c-green);
    }
  }
}

.card-url {
  font-size: 12px;
  position: absolute;
  left: 10px;
  bottom: 0;
  &:hover {
    color: aqua;
    text-decoration: underline;
    cursor: pointer;
  }
}

.radio-group-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 当"是"被选中时的样式 */
.yes-selected {
  border-color: #e6a23c;
}

.yes-selected .status-icon {
  background-color: #e6a23c;
}

/* 当"否"被选中时的样式 */
.no-selected {
  border-color: #67c23a;
}

.no-selected .status-icon {
  background-color: #67c23a;
}

/* Element UI radio button 样式调整 */
:deep(.el-radio-group) {
  .el-radio-button__inner {
    background-color: transparent;
    border-color: #e6a23c;
    color: white;
    padding: 6px 10px;
    font-size: 12px;
  }

  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: white;
  }
}

.yes-selected :deep(.el-radio-button) {
  &:first-child {
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: #e6a23c;
      border-color: #e6a23c;
      box-shadow: -1px 0 0 0 #e6a23c;
    }
  }
}

.no-selected :deep(.el-radio-button) {
  &:last-child {
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: #67c23a;
      border-color: #67c23a;
      box-shadow: -1px 0 0 0 #67c23a;
    }
  }
}

/* 针对长文本的特殊处理 */
.selection-card:nth-child(4) .card-text {
  font-size: 12px;
  line-height: 1.2;
}

@media screen and (max-width: 768px) {
  .selection-card {
    width: 47%;
    margin-right: 6%;
  }

  .selection-card:nth-child(3n) {
    margin-right: 6%;
  }

  .selection-card:nth-child(2n) {
    margin-right: 0;
  }
}

@media screen and (max-width: 480px) {
  .selection-card {
    width: 100%;
    margin-right: 0;
  }
}
</style>
