<template>
  <div id="app">
    <router-view />
    <Settings ref="settingsRef" />
  </div>
</template>

<script>
import { Local } from "@/utils/storage.js";
import Settings from "@/layout/navBars/breadcrumb/settings.vue";
export default {
  name: "App",
  components: { Settings },
  mounted() {
    this.openSettingsDrawer();
    this.getLayoutThemeConfig();
  },
  methods: {
    // 布局配置弹窗打开
    openSettingsDrawer() {
      this.$bus.$on("openSettingsDrawer", () => {
        this.$refs.settingsRef.openDrawer();
      });
    },
    // 获取缓存中的布局配置
    getLayoutThemeConfig() {
      const root = document.documentElement;
      if (Local.get("themeConfigPrev")) {
        this.$store.dispatch("themeConfig/setThemeConfig", Local.get("themeConfigPrev"));
        const themeConfig = this.$store.state.themeConfig.themeConfig;
        root.style.cssText = Local.get("themeConfigStyle");
        root.setAttribute("layout-theme-color", themeConfig.theme);
        const themeMode = themeConfig.isIsDark ? "dark" : "light";
        root.setAttribute("data-theme", themeMode);
      } else {
        Local.set("themeConfigPrev", this.$store.state.themeConfig.themeConfig);
        root.setAttribute("data-theme", "light");
      }
    }
  },
  destroyed() {
    this.$bus.$off("openSettingsDrawer");
  }
};
</script>
